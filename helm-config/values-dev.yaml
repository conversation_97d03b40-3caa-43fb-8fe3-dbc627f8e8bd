replicas: 1
name: onehub-backend-feedback-service-dev

environment: "DEV"

hpaConf:
  reqCpu: 50m
  reqMem: 0.5Gi
  #limCpu: 1000m # remove limit to avoid throttling
  limMem: 1.2Gi
  minRep: 2
  maxRep: 2
  avuCpu: 95
  avuMem: 80

azureAd:
  instance: https://login.microsoftonline.com/
  tenantId: 74892fe7-b6cb-43e7-912b-52194d3fd7c8
  clientId: 82cd72ca-0c69-4d5c-980b-20856c1d62fe
  clientSecret: mock
  audience: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe"

swagger:
  enabled: true

workflow:
  baseUrl: "https://servicebox-test.adnoc.ae/WaaS"
  module: "FeedbackApproval"
  workflowScope: "oneTalentFeedback"
  remoteEntryUrl: "https://dev.onetalent.adnoc.ae/feedback/remoteEntry.js"
  flows:
    Feedback Request:
      NotificationType: "Feedback"
      OneHubUrl: "https://onehub2-uat.adnoc.ae"
    Voluntary Feedback:
      NotificationType: "Voluntary Feedback"
      OneHubUrl: "https://onehub2-uat.adnoc.ae"
      ViewDetailsUrl: "https://onehub2-dev2.adnoc.ae/e-service-item/feedback/9735972d-09a3-476f-842f-969427b76d57/my/feedback?drawer=FeedbackDetailsReceived"

caching:
  namespace: "dev."

ai:
  baseUrl: "https://dev.api.adnoc.ae/dataiku/onetalent/"
  environmentPrefix: "stg"

surveyApi:
  baseUrl: "https://dev.api.adnoc.ae/servicebox/onetalent/"

employeeSearchService:
  baseUrl: "https://dev.api.adnoc.ae/search/onetalent/stg"

seq:
  enabled: true
  host: "http://seq-forwarder-non-prod.seq-forwarder.svc.cluster.local"
  apiKey: mock

ingress:
  host: api.dev.onetalent.adnoc.ae
  path: /feedback
  corsAllowOrigin: "https://onehub2-stg.adnoc.ae"

adminService:
  baseUrl: "http://onetalent-backend-query-service.onetalent-dev.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

synchronizationSfService:
  baseUrl: "http://onetalent-backend-sap-sf-sync-service.onetalent-dev.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

performanceService:
  baseUrl: "http://onetalent-backend-performance-service.onetalent-dev.svc.cluster.local"
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

userMapping:
  enabled: true
  baseUrl: "https://servicebox-uat.adnoc.ae/OneHub.Api/"
  targetScope: "https://servicebox-uat.adnoc.ae/.default"
  impersonateAsUserFromHeader: true

massTransit:
  queueTopicPrefix: dev

hangfire:
  dashboard:
    enabled: true
