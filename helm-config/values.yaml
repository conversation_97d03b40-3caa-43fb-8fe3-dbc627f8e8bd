replicas: 2
name: onehub-backend-feedback-service
imageVersion: local

logging:
  MinimumLevel:
    Default: "Information"
    Override:
      Microsoft.EntityFrameworkCore.Database.Command: "Information"

applicationInsights:
  connectionString: mock
  enableAdaptiveSampling: false

appConfig:
  connectionString: mock

pagingOptions:
  defaultPageSize: 10
  maxPageSize: 500

image:
  repository: acrshduatotl01.azurecr.io/onetalent-backend-feedback-service
  port: 8080
  args: []

database:
  connectionString: mock
  additionalParams: TrustServerCertificate=true;Min Pool Size=10;Max Pool Size=1000;

swagger:
  serverRelativePath: "feedback"
  useSsl: true

caching:
  expirationInMinutes: 5
  localCacheExpirationInMinutes: 5
  maximumKeyLength: 1024
  prefix: "feedback"

ingress:
  corsAllowHeaders: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,traceparent,tracestate,request-id,request-context,test-header"

adminService:
  baseUrl: "https://example.com"
  scope: "example-scope"

surveyApi:
  scope: "4285e1fe-a7bf-4167-9a5b-e9194ffe7d85/.default"

employeeSearchService:
  scope: "api://82cd72ca-0c69-4d5c-980b-20856c1d62fe/.default"

userMapping:
  impersonateAsUserFromHeader: false

httpLogging:
  Disabled: false
  AllowedHeaders:
    - "sec-ch-ua"
    - "sec-ch-ua-platform"
    - "sec-ch-ua-mobile"
    - "Sec-Fetch-Site"
    - "Sec-Fetch-Mode"
    - "Sec-Fetch-Dest"

initMigration: true

featureManagement: {}

massTransit:
  isEnabled: true
  sharedAccessKey: mock
  sharedAccessKeyName: mock
  endpoint: mock
  subscriptionPrefix: feedback
  topics:
    atrCycleTopic:
      isEnabled: true
      subscriptions:
        atrCycleChangedSubscription:
          isEnabled: true
    employeeTopic:
      isEnabled: true
      subscriptions:
        employeeChangedSubscription:
          isEnabled: true
    feedbackTopic:
      isEnabled: true
      subscriptions:
        feedbackChangedSubscription:
          isEnabled: true

hangfire:
  dashboard:
    enabled: false
    login: admin
    password: mock
    prefixPath: /feedback
  inMemoryStorage: false
  backgroundJobServerName: FeedbackServiceHangfire
  dbConnectionStringName: FeedbackDb
  hangfireScheme: fhf

ai:
  oauthEnabled: false
  oauthScope: "mock"
  authSchema: "mock"
  apiKeys:
    FM: mock
