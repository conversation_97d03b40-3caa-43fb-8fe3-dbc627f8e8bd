dotnet tool restore

$srcRoot = Join-Path -Path $PSScriptRoot -ChildPath "../src/" -Resolve
$devopsFolder = Join-Path -Path $PSScriptRoot -ChildPath "../../devops-common"

if (!(Test-Path -Path $devopsFolder) -or (Get-ChildItem -Path $devopsFolder | Measure-Object).Count -eq 0) {
  Write-Error "Please clone devops-common repository inside $devopsFolder"
  exit 1
}

dotnet pwsh -File "$devopsFolder/scripts/openapi/process-client.ps1" -WebAppPath "$srcRoot/WebAPI" -ClientWorkingDir "$srcRoot/WebAPI.Client"