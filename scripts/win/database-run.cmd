@echo off

REM === Check for container runtime
set _command=docker
where /q %_command%
if errorlevel 1 (
  set _command=podman
)
echo Runtime to be used: %_command%
REM ==============

set tag=2022-latest
set image=mcr.microsoft.com/mssql/server

echo %image%:%tag% will be used

echo Starting database

%_command% run ^
  --rm ^
  -e MSSQL_SA_PASSWORD="P@ssword11" ^
  -e ACCEPT_EULA=Y ^
  -p 1433:1433 ^
  --name ms_sql ^
  %image%:%tag%
