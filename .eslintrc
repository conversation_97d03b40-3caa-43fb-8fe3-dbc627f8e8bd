{
  "env": {
    "browser": true,
    "node": true,
    "es2021": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:@typescript-eslint/recommended",
    "plugin:@tanstack/eslint-plugin-query/recommended",
    "prettier"
  ],
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "plugins": [
    "react",
    "react-hooks",
    "prettier",
    "import",
    "@typescript-eslint",
    "eslint-plugin-unicorn",
    "local-rules"
  ],
  "globals": {
    "React": true
  },
  "settings": {
    "import/resolver": {
      "typescript": {
        "project": "./tsconfig.json"
      }
    },
    "react": {
      "version": "18.2.0"
    }
  },
  "parser": "@typescript-eslint/parser",
  "rules": {
    "@typescript-eslint/no-explicit-any": "error",
    "prettier/prettier": [
      "error",
      {
        "endOfLine": "auto"
      }
    ],
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "react-hooks/rules-of-hooks": "error",
    "react/jsx-indent": "off",
    "react/jsx-indent-props": "off",
    "react/forbid-prop-types": "off",
    "react/prop-types": "off",
    "react/require-default-props": "off",
    "react-hooks/exhaustive-deps": "error",
    "react/no-array-index-key": "off",
    "react/no-unescaped-entities": [
      "error",
      {
        "forbid": [">", "}"]
      }
    ],
    "object-shorthand": ["error", "always"],
    "arrow-body-style": ["error", "as-needed"],
    "arrow-parens": ["error", "always"],
    "space-in-parens": ["error", "never"],
    "no-await-in-loop": "off",
    "no-promise-executor-return": "error",
    "object-curly-spacing": ["error", "always"],
    "computed-property-spacing": ["error", "never"],
    "array-bracket-spacing": ["error", "never"],
    "space-unary-ops": "error",
    "comma-spacing": [
      "error",
      {
        "before": false,
        "after": true
      }
    ],
    "comma-style": ["error", "last"],
    "space-infix-ops": [
      "error",
      {
        "int32Hint": false
      }
    ],
    "keyword-spacing": [
      "error",
      {
        "before": true
      }
    ],
    "prefer-arrow-callback": "error",
    "no-sequences": "error",
    "quote-props": ["error", "as-needed"],
    "jsx-quotes": ["error", "prefer-double"],
    "no-console": "error",
    "no-multi-spaces": "error",
    "padded-blocks": ["error", "never"],
    "arrow-spacing": [
      "error",
      {
        "before": true,
        "after": true
      }
    ],
    "key-spacing": [
      "error",
      {
        "beforeColon": false
      }
    ],
    "react/display-name": "off",
    "no-multiple-empty-lines": [
      "error",
      {
        "max": 1,
        "maxEOF": 1
      }
    ],
    "no-spaced-func": "error",
    "comma-dangle": ["error", "never"],
    "no-trailing-spaces": "error",
    //"indent": ["error", 2, { "SwitchCase": 1 }],
    // "linebreak-style": ["error", "unix"],
    "quotes": [
      "error",
      "single",
      {
        "avoidEscape": true
      }
    ],
    "no-restricted-imports": "off",
    "@typescript-eslint/no-restricted-imports": [
      "error",
      {
        "name": "services",
        "patterns": ["@/services/*", "@/services"],
        "message": "Please use domainModel instead"
      }
    ],
    "@typescript-eslint/no-unused-vars": [
      "error",
      {
        "varsIgnorePattern": "^_",
        "argsIgnorePattern": "^_"
      }
    ],
    "import/order": [
      "error",
      {
        "groups": [
          "builtin",
          "external",
          "internal",
          "parent",
          "sibling",
          "index"
        ],
        "pathGroups": [
          {
            "pattern": "react**",
            "group": "external",
            "position": "before"
          },
          {
            "pattern": "@oh/**",
            "group": "internal",
            "position": "before"
          },
          {
            "pattern": "@/**",
            "group": "internal",
            "position": "after"
          },
          {
            "pattern": "./**",
            "group": "internal",
            "position": "after"
          },
          {
            "pattern": "../**",
            "group": "internal",
            "position": "after"
          },
          {
            "pattern": "../../**",
            "group": "internal",
            "position": "after"
          }
        ],
        "pathGroupsExcludedImportTypes": ["builtin"],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    // later, this will be enforced as an error once we add all the data attributes
    "local-rules/require-data-attribute": "warn",
    "import/no-restricted-paths": [
      "error",
      {
        "zones": [
          {
            "target": "./src/shared",
            "from": ["./src/atr-performance-forms", "./src/support"]
          },
          {
            "target": "./src/atr-performance-forms",
            "from": ["./src/support"]
          },
          {
            "target": "./src/support",
            "from": ["./src/atr-performance-forms"]
          }
        ]
      }
    ]
  },
  "overrides": [
    {
      "files": ["jest/setupTests.tsx", "*.test.tsx"],
      "rules": {
        "local-rules/require-data-attribute": "off"
      }
    }
  ],
  "ignorePatterns": ["node_modules/", "webpack.config.ts"]
}
