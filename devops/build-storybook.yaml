trigger:
  branches:
    include:
      - main
      
parameters:
  - name: env
    type: string
    default: dev
    values:
     - dev
     - stg

resources:
  repositories:
    - repository: devops-common
      type: git
      name: OneTalent/devops-common
      ref: main

stages:
- template: pipelines/templates/frontend-build.template.yml@devops-common
  parameters:
    env: ${{ parameters.env }}
    service_name: "onetalent-ui-kit"
    repository_name: "onetalent-ui-kit"
    dockerfile_path: "Dockerfile"
    helm_chart_folder: "helm/"
    version_prefix: "9.0"
