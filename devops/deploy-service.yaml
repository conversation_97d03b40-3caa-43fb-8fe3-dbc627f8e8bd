trigger: none
appendCommitMessageToRunName: false

resources:
  repositories:
  - repository: devops-common
    type: git
    name: OneTalent/devops-common
    ref: main

  pipelines:
  - pipeline: build-pipeline
    source: build-feedback-service
    trigger: 
      branches:
        include: 
        - main

variables: 
- name: migration_arg
  value: ''

stages:
- template: pipelines/templates/helm-deploy-all-envs.template.yml@devops-common
  parameters:
    application_name: onetalent-backend-feedback-service
    build_output_name: build-pipeline
    config_map_path: "helm-config"
    pre_deploy_steps:
    - bash: |
        escaped_conn=$(echo '$(DB_Feedback_ConnectionString)' | sed 's/,/\\,/g')
        echo "##vso[task.setvariable variable=escaped_connection_string]$escaped_conn"
      displayName: 'Escape connection string'
    additional_deploy_params: >-
      --set database.connectionString="$(escaped_connection_string)"
      --set azureAd.clientSecret=$(Client_Secret)
      --set seq.apiKey=$(Seq_ApiKey)
      --set applicationInsights.connectionString=$(AppInsights_ConnString)
      --set massTransit.endpoint=$(MassTransit_Endpoint)
      --set massTransit.sharedAccessKey=$(MassTransit_SharedAccessKey)
      --set massTransit.sharedAccessKeyName=$(MassTransit_SharedAccessKeyName)
      --set appConfig.connectionString=$(AppConfig_ConnString)
      --set caching.connectionString=$(RedisConfig_ConnString)
      --set ai.apiKeys.FM=$(AI_FM_ENV_ApiKey)
      --set ai.oauthScope=$(APIM_Scope)
      --set ai.oauthEnabled=$(AI_OAuth_Enabled)
      --set ai.authSchema=$(AI_AuthSchema)
      --set hangfire.dashboard.password=$(Hangfire_Password)
      $(migration_arg)
