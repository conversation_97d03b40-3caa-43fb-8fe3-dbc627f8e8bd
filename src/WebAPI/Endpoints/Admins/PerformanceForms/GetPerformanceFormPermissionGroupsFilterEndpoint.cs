using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class GetPerformanceFormPermissionGroupsFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var permissionGroups = await atrAdminQueryService.GetPerformanceFormPermissionGroupsAsync(search, cancellationToken);

        return permissionGroups is not null ?
           Results.Ok(permissionGroups.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptions>());
    }
}
