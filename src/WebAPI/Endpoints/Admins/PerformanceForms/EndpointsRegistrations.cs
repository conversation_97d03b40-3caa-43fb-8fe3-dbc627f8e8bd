using Asp.Versioning.Builder;
using OneTalent.AtrAdminService.WebAPI.DTOs;
using OneTalent.Common.Extensions.Paging;
using OneTalent.WebAPI.Extensions;
using OneTalent.WebAPI.Extensions.Authorization;
using System.Net.Mime;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class EndpointsRegistrations
{
    public static void RegisterPerformanceFormsQueryEndpoints(this WebApplication app, ApiVersionSet versionSet)
    {
        var group = app.MapGroup($"{ApiVersions.ApiPrefix}/performance-forms")
            .WithTags("Admin - Performance Forms - Queries")
            .WithApiVersionSet(versionSet)
            .RequireAuthorization(AuthPolicies.ValidUser);

        group
           .MapPost("search", SearchPerformanceFormsEndpoint.ExecuteAsync)
           .Produces<PagedListResult<PerformanceFormDto>>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status404NotFound)
           .MapToApiVersion(ApiVersions.V1);

        group
           .MapGet("statuses", GetPerformanceFormStatusesEndpoint.ExecuteAsync)
           .Produces<PerformanceFormStatusDto[]>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status404NotFound)
           .MapToApiVersion(ApiVersions.V1);

        group
           .MapGet("companies", GetCompaniesFilterEndpoint.ExecuteAsync)
           .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status404NotFound)
           .MapToApiVersion(ApiVersions.V1);

        group
          .MapGet("divisions", GetDivisionsFilterEndpoint.ExecuteAsync)
          .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
          .ProducesProblem(StatusCodes.Status404NotFound)
          .MapToApiVersion(ApiVersions.V1);

        group
          .MapGet("directorates", GetDirectoratesFilterEndpoint.ExecuteAsync)
          .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
          .ProducesProblem(StatusCodes.Status404NotFound)
          .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("functions", GetFunctionsFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("templates", GetPerformanceFormTemplatesFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("atr-groups", GetPerformanceFormPermissionGroupsFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("employees", GetPerformanceFormEmployeesFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("line-managers", GetPerformanceFormLineManagersFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);
        
        group
         .MapGet("b2b-managers", GetPerformanceFormB2bManagersFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
         .MapGet("dottetline-managers", GetPerformanceFormDottetLineManagersFilterEndpoint.ExecuteAsync)
         .Produces<NamedOptionsDto[]>(contentType: MediaTypeNames.Application.Json)
         .ProducesProblem(StatusCodes.Status404NotFound)
         .MapToApiVersion(ApiVersions.V1);

        group
           .MapPost(
               "{performanceFormId}/history",
               GetPerformanceFormHistoryEndpoint.ExecuteAsync)
           .Produces<PagedListResult<PerformanceFormHistoryDto>>(contentType: MediaTypeNames.Application.Json)
           .ProducesProblem(StatusCodes.Status401Unauthorized)
           .MapToApiVersion(ApiVersions.V1);
    }
}
