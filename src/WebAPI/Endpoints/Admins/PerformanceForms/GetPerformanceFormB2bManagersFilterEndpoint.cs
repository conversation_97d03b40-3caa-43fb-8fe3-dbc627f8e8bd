using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class GetPerformanceFormB2bManagersFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var employees = await atrAdminQueryService.GetPerformanceFormEmployeesAsync(search, SearchEmployeeOptions.B2BManager, cancellationToken);

        return employees is not null ?
           Results.Ok(employees.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptions>());
    }
}
