using Microsoft.AspNetCore.Mvc;
using OneTalent.AtrAdminService.Application.AtrAdmin.Services;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.WebAPI.DTOs;

namespace OneTalent.AtrAdminService.WebAPI.Endpoints.Admins.PerformanceForms;

public static class GetPerformanceFormTemplatesFilterEndpoint
{
    internal static async Task<IResult> ExecuteAsync(
        [FromQuery] string? search,
        IAtrAdminQueryService atrAdminQueryService,
        CancellationToken cancellationToken)
    {
        var templates = await atrAdminQueryService.GetPerformanceFormTemplatesAsync(search, cancellationToken);

        return templates is not null ?
           Results.Ok(templates.ToNamedOptionsDto()) :
           Results.Ok(Array.Empty<NamedOptions>());
    }
}

