using OneTalent.AtrAdminService.Application.Common.Extensions;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Contracts.Constants;
using OneTalent.AtrService.Client;

namespace OneTalent.AtrAdminService.WebAPI.DTOs;

internal static class Mappers
{
    public static EmployeeShortDto ToEmployeeShortDto(this IEmployee source)
    {
        return source switch
        {
            Employee employee => new EmployeeShortDto(
                employee.EmployeeId,
                employee.CompanyCode,
                employee.CompanyName,
                employee.FullNameEnglish,
                employee.Email,
                employee.JobTitle,
                employee.PositionName
            ),
            EmployeeBase employeeBase => new EmployeeShortDto(
                employeeBase.EmployeeId,
                "-",
                "-",
                "N/A",
                string.Empty, // Empty e-mail is used by UI logic to filter-out unknown users
                "-",
                "-"
            ),
            _ => new EmployeeShortDto(
                "-1",
                "-",
                "-",
                "N/A",
                string.Empty,  // Empty e-mail is used by UI logic to filter-out unknown users
                "-",
                "-"
            ),
        };
    }

    public static EmployeeInfoDto ToEmployeeInfoDto(this Employee source) => new(
        source.EmployeeId,
        source.CompanyCode,
        source.CompanyName,
        source.FullNameEnglish,
        source.Email,
        source.JobTitle,
        source.PositionName,
        source.IsManager ?? false,
        []
       );

    public static ActionDto ToActionDto(this Application.Actions.Models.Action source) => new(
        source.Id,
        source.IsValidation,
        source.Status,
        source.Type,
        source.Initiator.ToEmployeeShortDto(),
        source.StartDate,
        source.EndDate,
        source.InputFileName,
        source.OutputFileName);

    public static FileInfoDto ToFileInfoDto(this AtrAdminFileInfo source) => new(source.Name, source.LengthInBytes);

    public static IEnumerable<PerformanceFormStatusDto> ToStatusesDto(this IEnumerable<PerformanceFormStatus> source) =>
        source.Select(x => new PerformanceFormStatusDto
        {
            Id = x.Id,
            Name = x.Name,
        });

    public static IEnumerable<NamedOptionsDto> ToNamedOptionsDto(this IEnumerable<NamedOptions> source) =>
        source.Select(x => new NamedOptionsDto(x.Id, x.Name));

    public static TDto ToDto<TEnum, TDto>(this TEnum source) where TEnum : Enum
        where TDto : class?, INamedOptionDto<TEnum>?, new()
    {
        var option = source.ToLookupOption();

        return new TDto { Id = option.Id, Name = option.Name };
    }

    public static string ToMajorStatus(this string performanceFormStatusId)
    {
        var isValid = long.TryParse(performanceFormStatusId, out var performanceFormStatusIdLong);
        if (!isValid)
        {
            throw new InvalidOperationException($"Unsupported Statuses value: {performanceFormStatusId}");
        }

        return performanceFormStatusIdLong switch
            {
                (long)PerformanceFormStatuses.Draft => PerformanceFormStep.Draft.GetDisplayName(),
                (long)PerformanceFormStatuses.SelfAssessmentNotStarted => PerformanceFormStep.SelfAssessment.GetDisplayName(),
                (long)PerformanceFormStatuses.SelfAssessmentInProgress => PerformanceFormStep.SelfAssessment.GetDisplayName(),
                (long)PerformanceFormStatuses.ManagerAssessmentNotStarted => PerformanceFormStep.ManagerAssessment.GetDisplayName(),
                (long)PerformanceFormStatuses.ManagerAssessmentInProgress => PerformanceFormStep.ManagerAssessment.GetDisplayName(),
                (long)PerformanceFormStatuses.ManagerAssessmentCompleted => PerformanceFormStep.ManagerAssessment.GetDisplayName(),
                (long)PerformanceFormStatuses.DottedLineManagerEndorsementNotStarted => PerformanceFormStep.DottedLineManagerEndorsement.GetDisplayName(),
                (long)PerformanceFormStatuses.SecondLineManagerEndorsementNotStarted => PerformanceFormStep.SecondLineManagerEndorsement.GetDisplayName(),
                (long)PerformanceFormStatuses.SecondLineManagerEndorsementCompleted => PerformanceFormStep.SecondLineManagerEndorsement.GetDisplayName(),
                (long)PerformanceFormStatuses.NormalizationNotStarted => PerformanceFormStep.Normalization.GetDisplayName(),
                (long)PerformanceFormStatuses.NormalizationInProgress => PerformanceFormStep.Normalization.GetDisplayName(),
                (long)PerformanceFormStatuses.NormalizationCompleted => PerformanceFormStep.Normalization.GetDisplayName(),
                (long)PerformanceFormStatuses.RatingApprovalPending => PerformanceFormStep.RatingApproval.GetDisplayName(),
                (long)PerformanceFormStatuses.RatingApprovalApproved => PerformanceFormStep.RatingApproval.GetDisplayName(),
                (long)PerformanceFormStatuses.Completed => PerformanceFormStep.Completed.GetDisplayName(),
                _ => throw new InvalidOperationException($"Unsupported Statuses value: {performanceFormStatusId}")
            };
    }

    public static string ToMinorStatus(this string performanceFormStatusId)
    {
        var isValid = long.TryParse(performanceFormStatusId, out var performanceFormStatusIdLong);
        if (!isValid)
        {
            throw new InvalidOperationException($"Unsupported Statuses value: {performanceFormStatusId}");
        }

        return performanceFormStatusIdLong switch
        {
            (long)PerformanceFormStatuses.Draft => string.Empty,
            (long)PerformanceFormStatuses.SelfAssessmentNotStarted => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.SelfAssessmentInProgress => PerformanceFormMinorStep.InProgress.GetDisplayName(),
            (long)PerformanceFormStatuses.ManagerAssessmentNotStarted => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.ManagerAssessmentInProgress => PerformanceFormMinorStep.InProgress.GetDisplayName(),
            (long)PerformanceFormStatuses.ManagerAssessmentCompleted => PerformanceFormMinorStep.Completed.GetDisplayName(),
            (long)PerformanceFormStatuses.DottedLineManagerEndorsementNotStarted => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.SecondLineManagerEndorsementNotStarted => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.SecondLineManagerEndorsementCompleted => PerformanceFormMinorStep.Completed.GetDisplayName(),
            (long)PerformanceFormStatuses.NormalizationNotStarted => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.NormalizationInProgress => PerformanceFormMinorStep.InProgress.GetDisplayName(),
            (long)PerformanceFormStatuses.NormalizationCompleted => PerformanceFormMinorStep.Completed.GetDisplayName(),
            (long)PerformanceFormStatuses.RatingApprovalPending => PerformanceFormMinorStep.NotStarted.GetDisplayName(),
            (long)PerformanceFormStatuses.RatingApprovalApproved => PerformanceFormMinorStep.InProgress.GetDisplayName(),
            (long)PerformanceFormStatuses.Completed => string.Empty,
            _ => throw new InvalidOperationException($"Unsupported Statuses value: {performanceFormStatusId}")
        };
    }

    public static PerformanceFormHistoryDto ToPerformanceFormHistoryDto(this PerformanceFormHistoryRecord history) =>
       new(
           history.PerformanceFormId,
           history.ActionDateTime,
           history.ActionType,
           history.ActionOwnerId,
           history.ActionOwnerName,
           history.MajorStatusTo,
           history.MinorStatusTo,
           history.MajorStatusFrom,
           history.MinorStatusFrom,
           history.ActionReason,
           history.ActionOwnerJobRole,
           history.GroupCompany,
           history.Directorate,
           history.Function,
           history.Division,
           history.PerformanceCycleName,
           history.AssessmentLineManagerName,
           history.AssessmentB2BManagerName,
           history.DottedLineManagerName,
           history.CurrentLineManager,
           history.ManagerRating,
           history.ManagerRatingName,
           history.ManagerNineBoxPotential,
           history.NineBoxPotentialRating,
           history.NormalizedRating,
           history.NormalizedRatingName,
           history.NormalizedNineBoxPotential,
           history.WorkflowId,
           history.WfAssigneeName);

public static PerformanceFormDto ToPerformanceFormDto(this AssessmentForm source) =>
  new(
      source.Id,
      source.TemplateId,
      source.TemplateName,
      source.PerformanceFormStatusId.ToMajorStatus(),
      source.PerformanceFormStatusId.ToMinorStatus(),
      source.EmployeeId,
      source.EmployeeFullName,
      source.CompanyName,
      source.DirectorateName,
      source.FunctionName,
      source.DivisionName,
      source.AtrGroupName,
      source.AssessmentLineManagerId,
      source.AssessmentLineManagerName,
      source.AssessmentB2BManagerId,
      source.AssessmentB2BManagerName,
      source.DottedLineManagerId,
      source.DottedLineManagerName,
      source.LastUpdated,
      source.UpdatedBy);
}
