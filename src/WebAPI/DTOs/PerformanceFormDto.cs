using OneTalent.AtrAdminService.Contracts.Constants;

namespace OneTalent.AtrAdminService.WebAPI.DTOs;

public record PerformanceFormDto(
    string Id,
    string TemplateId,
    string TemplateName,
    string MajorStatus,
    string MinorStatus,
    string EmployeeId,
    string EmployeeFullName,
    string EmployeeCompanyName,
    string? EmployeeDirectorate,
    string EmployeeFunction,
    string EmployeeDivision,
    string AtrGroupName,
    string AssessmentLineManagerId,
    string AssessmentLineManagerName,
    string? AssessmentB2BManagerId,
    string? AssessmentB2BManagerName,
    string? DottedLineManagerId,
    string? DottedLineManagerName,
    DateTimeOffset? LastUpdated,
    string? UpdatedBy
);
