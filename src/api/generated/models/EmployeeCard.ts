/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { ManagerDetails } from './ManagerDetails';
import {
    ManagerDetailsFromJSON,
    ManagerDetailsFromJSONTyped,
    ManagerDetailsToJSON,
    ManagerDetailsToJSONTyped,
} from './ManagerDetails';

/**
 * 
 * @export
 * @interface EmployeeCard
 */
export interface EmployeeCard {
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    employeeId: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    fullName: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    jobTitle: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    companyCode: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    companyName: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    positionName: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeCard
     */
    positionId?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof EmployeeCard
     */
    isDeleted: boolean;
    /**
     * 
     * @type {ManagerDetails}
     * @memberof EmployeeCard
     */
    lineManager?: ManagerDetails;
    /**
     * 
     * @type {ManagerDetails}
     * @memberof EmployeeCard
     */
    b2BManager?: ManagerDetails;
}

/**
 * Check if a given object implements the EmployeeCard interface.
 */
export function instanceOfEmployeeCard(value: object): value is EmployeeCard {
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('fullName' in value) || value['fullName'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('jobTitle' in value) || value['jobTitle'] === undefined) return false;
    if (!('companyCode' in value) || value['companyCode'] === undefined) return false;
    if (!('companyName' in value) || value['companyName'] === undefined) return false;
    if (!('positionName' in value) || value['positionName'] === undefined) return false;
    if (!('isDeleted' in value) || value['isDeleted'] === undefined) return false;
    return true;
}

export function EmployeeCardFromJSON(json: any): EmployeeCard {
    return EmployeeCardFromJSONTyped(json, false);
}

export function EmployeeCardFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmployeeCard {
    if (json == null) {
        return json;
    }
    return {
        
        'employeeId': json['employeeId'],
        'fullName': json['fullName'],
        'email': json['email'],
        'jobTitle': json['jobTitle'],
        'companyCode': json['companyCode'],
        'companyName': json['companyName'],
        'positionName': json['positionName'],
        'positionId': json['positionId'] == null ? undefined : json['positionId'],
        'isDeleted': json['isDeleted'],
        'lineManager': json['lineManager'] == null ? undefined : ManagerDetailsFromJSON(json['lineManager']),
        'b2BManager': json['b2BManager'] == null ? undefined : ManagerDetailsFromJSON(json['b2BManager']),
    };
}

  export function EmployeeCardToJSON(json: any): EmployeeCard {
      return EmployeeCardToJSONTyped(json, false);
  }

  export function EmployeeCardToJSONTyped(value?: EmployeeCard | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'employeeId': value['employeeId'],
        'fullName': value['fullName'],
        'email': value['email'],
        'jobTitle': value['jobTitle'],
        'companyCode': value['companyCode'],
        'companyName': value['companyName'],
        'positionName': value['positionName'],
        'positionId': value['positionId'],
        'isDeleted': value['isDeleted'],
        'lineManager': ManagerDetailsToJSON(value['lineManager']),
        'b2BManager': ManagerDetailsToJSON(value['b2BManager']),
    };
}

