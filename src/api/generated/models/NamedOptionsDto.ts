/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface NamedOptionsDto
 */
export interface NamedOptionsDto {
    /**
     * 
     * @type {string}
     * @memberof NamedOptionsDto
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof NamedOptionsDto
     */
    name: string;
}

/**
 * Check if a given object implements the NamedOptionsDto interface.
 */
export function instanceOfNamedOptionsDto(value: object): value is NamedOptionsDto {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('name' in value) || value['name'] === undefined) return false;
    return true;
}

export function NamedOptionsDtoFromJSON(json: any): NamedOptionsDto {
    return NamedOptionsDtoFromJSONTyped(json, false);
}

export function NamedOptionsDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): NamedOptionsDto {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'name': json['name'],
    };
}

  export function NamedOptionsDtoToJSON(json: any): NamedOptionsDto {
      return NamedOptionsDtoToJSONTyped(json, false);
  }

  export function NamedOptionsDtoToJSONTyped(value?: NamedOptionsDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'id': value['id'],
        'name': value['name'],
    };
}

