/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { FeatureToggleModel } from './FeatureToggleModel';
import {
    FeatureToggleModelFromJSON,
    FeatureToggleModelFromJSONTyped,
    FeatureToggleModelToJSON,
    FeatureToggleModelToJSONTyped,
} from './FeatureToggleModel';

/**
 * 
 * @export
 * @interface FeatureTogglesResponse
 */
export interface FeatureTogglesResponse {
    /**
     * 
     * @type {string}
     * @memberof FeatureTogglesResponse
     */
    currentUserEmail: string;
    /**
     * 
     * @type {{ [key: string]: FeatureToggleModel; }}
     * @memberof FeatureTogglesResponse
     */
    features: { [key: string]: FeatureToggleModel; };
}

/**
 * Check if a given object implements the FeatureTogglesResponse interface.
 */
export function instanceOfFeatureTogglesResponse(value: object): value is FeatureTogglesResponse {
    if (!('currentUserEmail' in value) || value['currentUserEmail'] === undefined) return false;
    if (!('features' in value) || value['features'] === undefined) return false;
    return true;
}

export function FeatureTogglesResponseFromJSON(json: any): FeatureTogglesResponse {
    return FeatureTogglesResponseFromJSONTyped(json, false);
}

export function FeatureTogglesResponseFromJSONTyped(json: any, ignoreDiscriminator: boolean): FeatureTogglesResponse {
    if (json == null) {
        return json;
    }
    return {
        
        'currentUserEmail': json['currentUserEmail'],
        'features': (mapValues(json['features'], FeatureToggleModelFromJSON)),
    };
}

  export function FeatureTogglesResponseToJSON(json: any): FeatureTogglesResponse {
      return FeatureTogglesResponseToJSONTyped(json, false);
  }

  export function FeatureTogglesResponseToJSONTyped(value?: FeatureTogglesResponse | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'currentUserEmail': value['currentUserEmail'],
        'features': (mapValues(value['features'], FeatureToggleModelToJSON)),
    };
}

