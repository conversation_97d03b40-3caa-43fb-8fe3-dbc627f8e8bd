/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * 
 * mock
 * @export
 */
export const Rights = {
    Mock: 'mock'
} as const;
export type Rights = typeof Rights[keyof typeof Rights];


export function instanceOfRights(value: any): boolean {
    for (const key in Rights) {
        if (Object.prototype.hasOwnProperty.call(Rights, key)) {
            if (Rights[key as keyof typeof Rights] === value) {
                return true;
            }
        }
    }
    return false;
}

export function RightsFromJSON(json: any): Rights {
    return RightsFromJSONTyped(json, false);
}

export function RightsFromJSONTyped(json: any, ignoreDiscriminator: boolean): Rights {
    return json as Rights;
}

export function RightsToJSON(value?: Rights | null): any {
    return value as any;
}

export function RightsToJSONTyped(value: any, ignoreDiscriminator: boolean): Rights {
    return value as Rights;
}

