/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EmployeeShortDto } from './EmployeeShortDto';
import {
    EmployeeShortDtoFromJSON,
    EmployeeShortDtoFromJSONTyped,
    EmployeeShortDtoToJSON,
    EmployeeShortDtoToJSONTyped,
} from './EmployeeShortDto';

/**
 * 
 * @export
 * @interface PerformanceFormHistoryDto
 */
export interface PerformanceFormHistoryDto {
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    performanceFormId: string;
    /**
     * 
     * @type {Date}
     * @memberof PerformanceFormHistoryDto
     */
    actionDateTime: Date;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionType: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwnerId?: string | null;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwner?: EmployeeShortDto;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    majorStatusTo: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    minorStatusTo: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    majorStatusFrom?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    minorStatusFrom?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionReason?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    actionOwnerJobRole?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    groupCompany: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    directorate?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    _function?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    division?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    atrCycleName: string;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    assessmentLineManager?: EmployeeShortDto;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    assessmentB2BManager?: EmployeeShortDto;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    dottedLineManager?: EmployeeShortDto;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    currentLineManager?: EmployeeShortDto;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormHistoryDto
     */
    managerRating: number;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    managerRatingName: string;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    managerNineBoxPotential: string;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormHistoryDto
     */
    nineBoxPotentialRating: number;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedRating?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedRatingName?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    normalizedNineBoxPotential?: string | null;
    /**
     * 
     * @type {string}
     * @memberof PerformanceFormHistoryDto
     */
    workflowId?: string | null;
    /**
     * 
     * @type {EmployeeShortDto}
     * @memberof PerformanceFormHistoryDto
     */
    wfAssignee?: EmployeeShortDto;
}

/**
 * Check if a given object implements the PerformanceFormHistoryDto interface.
 */
export function instanceOfPerformanceFormHistoryDto(value: object): value is PerformanceFormHistoryDto {
    if (!('performanceFormId' in value) || value['performanceFormId'] === undefined) return false;
    if (!('actionDateTime' in value) || value['actionDateTime'] === undefined) return false;
    if (!('actionType' in value) || value['actionType'] === undefined) return false;
    if (!('majorStatusTo' in value) || value['majorStatusTo'] === undefined) return false;
    if (!('minorStatusTo' in value) || value['minorStatusTo'] === undefined) return false;
    if (!('groupCompany' in value) || value['groupCompany'] === undefined) return false;
    if (!('atrCycleName' in value) || value['atrCycleName'] === undefined) return false;
    if (!('managerRating' in value) || value['managerRating'] === undefined) return false;
    if (!('managerRatingName' in value) || value['managerRatingName'] === undefined) return false;
    if (!('managerNineBoxPotential' in value) || value['managerNineBoxPotential'] === undefined) return false;
    if (!('nineBoxPotentialRating' in value) || value['nineBoxPotentialRating'] === undefined) return false;
    return true;
}

export function PerformanceFormHistoryDtoFromJSON(json: any): PerformanceFormHistoryDto {
    return PerformanceFormHistoryDtoFromJSONTyped(json, false);
}

export function PerformanceFormHistoryDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormHistoryDto {
    if (json == null) {
        return json;
    }
    return {
        
        'performanceFormId': json['performanceFormId'],
        'actionDateTime': (new Date(json['actionDateTime'])),
        'actionType': json['actionType'],
        'actionOwnerId': json['actionOwnerId'] == null ? undefined : json['actionOwnerId'],
        'actionOwner': json['actionOwner'] == null ? undefined : EmployeeShortDtoFromJSON(json['actionOwner']),
        'majorStatusTo': json['majorStatusTo'],
        'minorStatusTo': json['minorStatusTo'],
        'majorStatusFrom': json['majorStatusFrom'] == null ? undefined : json['majorStatusFrom'],
        'minorStatusFrom': json['minorStatusFrom'] == null ? undefined : json['minorStatusFrom'],
        'actionReason': json['actionReason'] == null ? undefined : json['actionReason'],
        'actionOwnerJobRole': json['actionOwnerJobRole'] == null ? undefined : json['actionOwnerJobRole'],
        'groupCompany': json['groupCompany'],
        'directorate': json['directorate'] == null ? undefined : json['directorate'],
        '_function': json['function'] == null ? undefined : json['function'],
        'division': json['division'] == null ? undefined : json['division'],
        'atrCycleName': json['atrCycleName'],
        'assessmentLineManager': json['assessmentLineManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['assessmentLineManager']),
        'assessmentB2BManager': json['assessmentB2BManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['assessmentB2BManager']),
        'dottedLineManager': json['dottedLineManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['dottedLineManager']),
        'currentLineManager': json['currentLineManager'] == null ? undefined : EmployeeShortDtoFromJSON(json['currentLineManager']),
        'managerRating': json['managerRating'],
        'managerRatingName': json['managerRatingName'],
        'managerNineBoxPotential': json['managerNineBoxPotential'],
        'nineBoxPotentialRating': json['nineBoxPotentialRating'],
        'normalizedRating': json['normalizedRating'] == null ? undefined : json['normalizedRating'],
        'normalizedRatingName': json['normalizedRatingName'] == null ? undefined : json['normalizedRatingName'],
        'normalizedNineBoxPotential': json['normalizedNineBoxPotential'] == null ? undefined : json['normalizedNineBoxPotential'],
        'workflowId': json['workflowId'] == null ? undefined : json['workflowId'],
        'wfAssignee': json['wfAssignee'] == null ? undefined : EmployeeShortDtoFromJSON(json['wfAssignee']),
    };
}

  export function PerformanceFormHistoryDtoToJSON(json: any): PerformanceFormHistoryDto {
      return PerformanceFormHistoryDtoToJSONTyped(json, false);
  }

  export function PerformanceFormHistoryDtoToJSONTyped(value?: PerformanceFormHistoryDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'performanceFormId': value['performanceFormId'],
        'actionDateTime': ((value['actionDateTime']).toISOString()),
        'actionType': value['actionType'],
        'actionOwnerId': value['actionOwnerId'],
        'actionOwner': EmployeeShortDtoToJSON(value['actionOwner']),
        'majorStatusTo': value['majorStatusTo'],
        'minorStatusTo': value['minorStatusTo'],
        'majorStatusFrom': value['majorStatusFrom'],
        'minorStatusFrom': value['minorStatusFrom'],
        'actionReason': value['actionReason'],
        'actionOwnerJobRole': value['actionOwnerJobRole'],
        'groupCompany': value['groupCompany'],
        'directorate': value['directorate'],
        'function': value['_function'],
        'division': value['division'],
        'atrCycleName': value['atrCycleName'],
        'assessmentLineManager': EmployeeShortDtoToJSON(value['assessmentLineManager']),
        'assessmentB2BManager': EmployeeShortDtoToJSON(value['assessmentB2BManager']),
        'dottedLineManager': EmployeeShortDtoToJSON(value['dottedLineManager']),
        'currentLineManager': EmployeeShortDtoToJSON(value['currentLineManager']),
        'managerRating': value['managerRating'],
        'managerRatingName': value['managerRatingName'],
        'managerNineBoxPotential': value['managerNineBoxPotential'],
        'nineBoxPotentialRating': value['nineBoxPotentialRating'],
        'normalizedRating': value['normalizedRating'],
        'normalizedRatingName': value['normalizedRatingName'],
        'normalizedNineBoxPotential': value['normalizedNineBoxPotential'],
        'workflowId': value['workflowId'],
        'wfAssignee': EmployeeShortDtoToJSON(value['wfAssignee']),
    };
}

