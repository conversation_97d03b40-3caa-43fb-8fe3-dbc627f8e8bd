/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { PerformanceFormHistoryDto } from './PerformanceFormHistoryDto';
import {
    PerformanceFormHistoryDtoFromJSON,
    PerformanceFormHistoryDtoFromJSONTyped,
    PerformanceFormHistoryDtoToJSON,
    PerformanceFormHistoryDtoToJSONTyped,
} from './PerformanceFormHistoryDto';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';

/**
 * 
 * @export
 * @interface PerformanceFormHistoryDtoPagedListResult
 */
export interface PerformanceFormHistoryDtoPagedListResult {
    /**
     * 
     * @type {Array<PerformanceFormHistoryDto>}
     * @memberof PerformanceFormHistoryDtoPagedListResult
     */
    items: Array<PerformanceFormHistoryDto>;
    /**
     * 
     * @type {PaginationData}
     * @memberof PerformanceFormHistoryDtoPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the PerformanceFormHistoryDtoPagedListResult interface.
 */
export function instanceOfPerformanceFormHistoryDtoPagedListResult(value: object): value is PerformanceFormHistoryDtoPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function PerformanceFormHistoryDtoPagedListResultFromJSON(json: any): PerformanceFormHistoryDtoPagedListResult {
    return PerformanceFormHistoryDtoPagedListResultFromJSONTyped(json, false);
}

export function PerformanceFormHistoryDtoPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormHistoryDtoPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(PerformanceFormHistoryDtoFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

  export function PerformanceFormHistoryDtoPagedListResultToJSON(json: any): PerformanceFormHistoryDtoPagedListResult {
      return PerformanceFormHistoryDtoPagedListResultToJSONTyped(json, false);
  }

  export function PerformanceFormHistoryDtoPagedListResultToJSONTyped(value?: PerformanceFormHistoryDtoPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(PerformanceFormHistoryDtoToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

