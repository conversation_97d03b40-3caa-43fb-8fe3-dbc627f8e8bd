/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { OrderBy } from './OrderBy';
import {
    OrderByFromJSON,
    OrderByFromJSONTyped,
    OrderByToJSON,
    OrderByToJSONTyped,
} from './OrderBy';

/**
 * 
 * @export
 * @interface PerformanceFormSearchQuery
 */
export interface PerformanceFormSearchQuery {
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    formIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    divisionIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    companyCodes?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    directorateIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    functionIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    templateIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    atrGroupIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    employeeIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    lineManagerIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    b2BManagerIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    dottetLineManagerIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    performanceFormStatusIds?: Array<string> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof PerformanceFormSearchQuery
     */
    atrCycleIds?: Array<string> | null;
    /**
     * 
     * @type {OrderBy}
     * @memberof PerformanceFormSearchQuery
     */
    orderBy?: OrderBy;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormSearchQuery
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof PerformanceFormSearchQuery
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the PerformanceFormSearchQuery interface.
 */
export function instanceOfPerformanceFormSearchQuery(value: object): value is PerformanceFormSearchQuery {
    return true;
}

export function PerformanceFormSearchQueryFromJSON(json: any): PerformanceFormSearchQuery {
    return PerformanceFormSearchQueryFromJSONTyped(json, false);
}

export function PerformanceFormSearchQueryFromJSONTyped(json: any, ignoreDiscriminator: boolean): PerformanceFormSearchQuery {
    if (json == null) {
        return json;
    }
    return {
        
        'formIds': json['formIds'] == null ? undefined : json['formIds'],
        'divisionIds': json['divisionIds'] == null ? undefined : json['divisionIds'],
        'companyCodes': json['companyCodes'] == null ? undefined : json['companyCodes'],
        'directorateIds': json['directorateIds'] == null ? undefined : json['directorateIds'],
        'functionIds': json['functionIds'] == null ? undefined : json['functionIds'],
        'templateIds': json['templateIds'] == null ? undefined : json['templateIds'],
        'atrGroupIds': json['atrGroupIds'] == null ? undefined : json['atrGroupIds'],
        'employeeIds': json['employeeIds'] == null ? undefined : json['employeeIds'],
        'lineManagerIds': json['lineManagerIds'] == null ? undefined : json['lineManagerIds'],
        'b2BManagerIds': json['b2BManagerIds'] == null ? undefined : json['b2BManagerIds'],
        'dottetLineManagerIds': json['dottetLineManagerIds'] == null ? undefined : json['dottetLineManagerIds'],
        'performanceFormStatusIds': json['performanceFormStatusIds'] == null ? undefined : json['performanceFormStatusIds'],
        'atrCycleIds': json['atrCycleIds'] == null ? undefined : json['atrCycleIds'],
        'orderBy': json['orderBy'] == null ? undefined : OrderByFromJSON(json['orderBy']),
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

  export function PerformanceFormSearchQueryToJSON(json: any): PerformanceFormSearchQuery {
      return PerformanceFormSearchQueryToJSONTyped(json, false);
  }

  export function PerformanceFormSearchQueryToJSONTyped(value?: PerformanceFormSearchQuery | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'formIds': value['formIds'],
        'divisionIds': value['divisionIds'],
        'companyCodes': value['companyCodes'],
        'directorateIds': value['directorateIds'],
        'functionIds': value['functionIds'],
        'templateIds': value['templateIds'],
        'atrGroupIds': value['atrGroupIds'],
        'employeeIds': value['employeeIds'],
        'lineManagerIds': value['lineManagerIds'],
        'b2BManagerIds': value['b2BManagerIds'],
        'dottetLineManagerIds': value['dottetLineManagerIds'],
        'performanceFormStatusIds': value['performanceFormStatusIds'],
        'atrCycleIds': value['atrCycleIds'],
        'orderBy': OrderByToJSON(value['orderBy']),
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

