/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Company } from './Company';
import {
    CompanyFromJSON,
    CompanyFromJSONTyped,
    CompanyToJSON,
    CompanyToJSONTyped,
} from './Company';
import type { PaginationData } from './PaginationData';
import {
    PaginationDataFromJSON,
    PaginationDataFromJSONTyped,
    PaginationDataToJSON,
    PaginationDataToJSONTyped,
} from './PaginationData';

/**
 * 
 * @export
 * @interface CompanyPagedListResult
 */
export interface CompanyPagedListResult {
    /**
     * 
     * @type {Array<Company>}
     * @memberof CompanyPagedListResult
     */
    items: Array<Company>;
    /**
     * 
     * @type {PaginationData}
     * @memberof CompanyPagedListResult
     */
    paging: PaginationData;
}

/**
 * Check if a given object implements the CompanyPagedListResult interface.
 */
export function instanceOfCompanyPagedListResult(value: object): value is CompanyPagedListResult {
    if (!('items' in value) || value['items'] === undefined) return false;
    if (!('paging' in value) || value['paging'] === undefined) return false;
    return true;
}

export function CompanyPagedListResultFromJSON(json: any): CompanyPagedListResult {
    return CompanyPagedListResultFromJSONTyped(json, false);
}

export function CompanyPagedListResultFromJSONTyped(json: any, ignoreDiscriminator: boolean): CompanyPagedListResult {
    if (json == null) {
        return json;
    }
    return {
        
        'items': ((json['items'] as Array<any>).map(CompanyFromJSON)),
        'paging': PaginationDataFromJSON(json['paging']),
    };
}

  export function CompanyPagedListResultToJSON(json: any): CompanyPagedListResult {
      return CompanyPagedListResultToJSONTyped(json, false);
  }

  export function CompanyPagedListResultToJSONTyped(value?: CompanyPagedListResult | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'items': ((value['items'] as Array<any>).map(CompanyToJSON)),
        'paging': PaginationDataToJSON(value['paging']),
    };
}

