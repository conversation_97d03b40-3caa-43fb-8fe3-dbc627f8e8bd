/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { EmployeeCard } from './EmployeeCard';
import {
    EmployeeCardFromJSON,
    EmployeeCardFromJSONTyped,
    EmployeeCardToJSON,
    EmployeeCardToJSONTyped,
} from './EmployeeCard';

/**
 * 
 * @export
 * @interface EmployeeFullInfoModel
 */
export interface EmployeeFullInfoModel {
    /**
     * 
     * @type {EmployeeCard}
     * @memberof EmployeeFullInfoModel
     */
    primaryEmployeeInfo: EmployeeCard;
    /**
     * 
     * @type {Array<EmployeeCard>}
     * @memberof EmployeeFullInfoModel
     */
    otherEmployeeProfiles: Array<EmployeeCard>;
}

/**
 * Check if a given object implements the EmployeeFullInfoModel interface.
 */
export function instanceOfEmployeeFullInfoModel(value: object): value is EmployeeFullInfoModel {
    if (!('primaryEmployeeInfo' in value) || value['primaryEmployeeInfo'] === undefined) return false;
    if (!('otherEmployeeProfiles' in value) || value['otherEmployeeProfiles'] === undefined) return false;
    return true;
}

export function EmployeeFullInfoModelFromJSON(json: any): EmployeeFullInfoModel {
    return EmployeeFullInfoModelFromJSONTyped(json, false);
}

export function EmployeeFullInfoModelFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmployeeFullInfoModel {
    if (json == null) {
        return json;
    }
    return {
        
        'primaryEmployeeInfo': EmployeeCardFromJSON(json['primaryEmployeeInfo']),
        'otherEmployeeProfiles': ((json['otherEmployeeProfiles'] as Array<any>).map(EmployeeCardFromJSON)),
    };
}

  export function EmployeeFullInfoModelToJSON(json: any): EmployeeFullInfoModel {
      return EmployeeFullInfoModelToJSONTyped(json, false);
  }

  export function EmployeeFullInfoModelToJSONTyped(value?: EmployeeFullInfoModel | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'primaryEmployeeInfo': EmployeeCardToJSON(value['primaryEmployeeInfo']),
        'otherEmployeeProfiles': ((value['otherEmployeeProfiles'] as Array<any>).map(EmployeeCardToJSON)),
    };
}

