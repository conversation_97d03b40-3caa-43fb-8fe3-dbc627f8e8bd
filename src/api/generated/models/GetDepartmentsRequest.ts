/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface GetDepartmentsRequest
 */
export interface GetDepartmentsRequest {
    /**
     * 
     * @type {number}
     * @memberof GetDepartmentsRequest
     */
    pageNumber?: number | null;
    /**
     * 
     * @type {number}
     * @memberof GetDepartmentsRequest
     */
    pageSize?: number | null;
}

/**
 * Check if a given object implements the GetDepartmentsRequest interface.
 */
export function instanceOfGetDepartmentsRequest(value: object): value is GetDepartmentsRequest {
    return true;
}

export function GetDepartmentsRequestFromJSON(json: any): GetDepartmentsRequest {
    return GetDepartmentsRequestFromJSONTyped(json, false);
}

export function GetDepartmentsRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): GetDepartmentsRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'pageNumber': json['pageNumber'] == null ? undefined : json['pageNumber'],
        'pageSize': json['pageSize'] == null ? undefined : json['pageSize'],
    };
}

  export function GetDepartmentsRequestToJSON(json: any): GetDepartmentsRequest {
      return GetDepartmentsRequestToJSONTyped(json, false);
  }

  export function GetDepartmentsRequestToJSONTyped(value?: GetDepartmentsRequest | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'pageNumber': value['pageNumber'],
        'pageSize': value['pageSize'],
    };
}

