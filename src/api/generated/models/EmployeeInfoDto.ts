/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { Rights } from './Rights';
import {
    RightsFromJSON,
    RightsFromJSONTyped,
    RightsToJSON,
    RightsToJSONTyped,
} from './Rights';

/**
 * 
 * @export
 * @interface EmployeeInfoDto
 */
export interface EmployeeInfoDto {
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    employeeId: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    companyCode: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    companyName: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    fullNameEnglish: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    email: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    jobTitle: string;
    /**
     * 
     * @type {string}
     * @memberof EmployeeInfoDto
     */
    positionName: string;
    /**
     * 
     * @type {boolean}
     * @memberof EmployeeInfoDto
     */
    isManager: boolean;
    /**
     * 
     * @type {Array<Rights>}
     * @memberof EmployeeInfoDto
     */
    permissions: Array<Rights>;
}

/**
 * Check if a given object implements the EmployeeInfoDto interface.
 */
export function instanceOfEmployeeInfoDto(value: object): value is EmployeeInfoDto {
    if (!('employeeId' in value) || value['employeeId'] === undefined) return false;
    if (!('companyCode' in value) || value['companyCode'] === undefined) return false;
    if (!('companyName' in value) || value['companyName'] === undefined) return false;
    if (!('fullNameEnglish' in value) || value['fullNameEnglish'] === undefined) return false;
    if (!('email' in value) || value['email'] === undefined) return false;
    if (!('jobTitle' in value) || value['jobTitle'] === undefined) return false;
    if (!('positionName' in value) || value['positionName'] === undefined) return false;
    if (!('isManager' in value) || value['isManager'] === undefined) return false;
    if (!('permissions' in value) || value['permissions'] === undefined) return false;
    return true;
}

export function EmployeeInfoDtoFromJSON(json: any): EmployeeInfoDto {
    return EmployeeInfoDtoFromJSONTyped(json, false);
}

export function EmployeeInfoDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): EmployeeInfoDto {
    if (json == null) {
        return json;
    }
    return {
        
        'employeeId': json['employeeId'],
        'companyCode': json['companyCode'],
        'companyName': json['companyName'],
        'fullNameEnglish': json['fullNameEnglish'],
        'email': json['email'],
        'jobTitle': json['jobTitle'],
        'positionName': json['positionName'],
        'isManager': json['isManager'],
        'permissions': ((json['permissions'] as Array<any>).map(RightsFromJSON)),
    };
}

  export function EmployeeInfoDtoToJSON(json: any): EmployeeInfoDto {
      return EmployeeInfoDtoToJSONTyped(json, false);
  }

  export function EmployeeInfoDtoToJSONTyped(value?: EmployeeInfoDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'employeeId': value['employeeId'],
        'companyCode': value['companyCode'],
        'companyName': value['companyName'],
        'fullNameEnglish': value['fullNameEnglish'],
        'email': value['email'],
        'jobTitle': value['jobTitle'],
        'positionName': value['positionName'],
        'isManager': value['isManager'],
        'permissions': ((value['permissions'] as Array<any>).map(RightsToJSON)),
    };
}

