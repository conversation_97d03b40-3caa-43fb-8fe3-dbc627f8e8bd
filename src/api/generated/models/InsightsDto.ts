/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface InsightsDto
 */
export interface InsightsDto {
    /**
     * 
     * @type {number}
     * @memberof InsightsDto
     */
    completedSelfAssessments: number;
    /**
     * 
     * @type {number}
     * @memberof InsightsDto
     */
    completedManagerAssessments: number;
    /**
     * 
     * @type {number}
     * @memberof InsightsDto
     */
    totalAssessments: number;
}

/**
 * Check if a given object implements the InsightsDto interface.
 */
export function instanceOfInsightsDto(value: object): value is InsightsDto {
    if (!('completedSelfAssessments' in value) || value['completedSelfAssessments'] === undefined) return false;
    if (!('completedManagerAssessments' in value) || value['completedManagerAssessments'] === undefined) return false;
    if (!('totalAssessments' in value) || value['totalAssessments'] === undefined) return false;
    return true;
}

export function InsightsDtoFromJSON(json: any): InsightsDto {
    return InsightsDtoFromJSONTyped(json, false);
}

export function InsightsDtoFromJSONTyped(json: any, ignoreDiscriminator: boolean): InsightsDto {
    if (json == null) {
        return json;
    }
    return {
        
        'completedSelfAssessments': json['completedSelfAssessments'],
        'completedManagerAssessments': json['completedManagerAssessments'],
        'totalAssessments': json['totalAssessments'],
    };
}

  export function InsightsDtoToJSON(json: any): InsightsDto {
      return InsightsDtoToJSONTyped(json, false);
  }

  export function InsightsDtoToJSONTyped(value?: InsightsDto | null, ignoreDiscriminator: boolean = false): any {
    if (value == null) {
        return value;
    }

    return {
        
        'completedSelfAssessments': value['completedSelfAssessments'],
        'completedManagerAssessments': value['completedManagerAssessments'],
        'totalAssessments': value['totalAssessments'],
    };
}

