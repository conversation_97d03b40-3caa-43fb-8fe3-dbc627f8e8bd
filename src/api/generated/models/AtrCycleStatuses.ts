/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * 
 * Inactive
 * 
 * Current
 * 
 * Next
 * @export
 */
export const AtrCycleStatuses = {
    Inactive: 'Inactive',
    Current: 'Current',
    Next: 'Next'
} as const;
export type AtrCycleStatuses = typeof AtrCycleStatuses[keyof typeof AtrCycleStatuses];


export function instanceOfAtrCycleStatuses(value: any): boolean {
    for (const key in AtrCycleStatuses) {
        if (Object.prototype.hasOwnProperty.call(AtrCycleStatuses, key)) {
            if (AtrCycleStatuses[key as keyof typeof AtrCycleStatuses] === value) {
                return true;
            }
        }
    }
    return false;
}

export function AtrCycleStatusesFromJSON(json: any): AtrCycleStatuses {
    return AtrCycleStatusesFromJSONTyped(json, false);
}

export function AtrCycleStatusesFromJSONTyped(json: any, ignoreDiscriminator: boolean): AtrCycleStatuses {
    return json as AtrCycleStatuses;
}

export function AtrCycleStatusesToJSON(value?: AtrCycleStatuses | null): any {
    return value as any;
}

export function AtrCycleStatusesToJSONTyped(value: any, ignoreDiscriminator: boolean): AtrCycleStatuses {
    return value as AtrCycleStatuses;
}

