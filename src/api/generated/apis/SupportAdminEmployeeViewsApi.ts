/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  CompanyPagedListResult,
  DepartmentPagedListResult,
  EmployeeFullInfoModel,
  GetCompaniesRequest,
  GetDepartmentsRequest,
  GetEmployeeDetailsRequest,
  GetPositionsRequestV2,
  PositionPagedListResult,
  ProblemDetails,
  SearchEmployeePagedListResult,
  SearchEmployeeRequest,
} from '../models/index';
import {
    CompanyPagedListResultFromJSON,
    CompanyPagedListResultToJSON,
    DepartmentPagedListResultFromJSON,
    DepartmentPagedListResultToJSON,
    EmployeeFullInfoModelFromJSON,
    EmployeeFullInfoModelToJSON,
    GetCompaniesRequestFromJSON,
    GetCompaniesRequestToJSON,
    GetDepartmentsRequestFromJSON,
    GetDepartmentsRequestToJSON,
    GetEmployeeDetailsRequestFromJSON,
    GetEmployeeDetailsRequestToJSON,
    GetPositionsRequestV2FromJSON,
    GetPositionsRequestV2ToJSON,
    PositionPagedListResultFromJSON,
    PositionPagedListResultToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    SearchEmployeePagedListResultFromJSON,
    SearchEmployeePagedListResultToJSON,
    SearchEmployeeRequestFromJSON,
    SearchEmployeeRequestToJSON,
} from '../models/index';

export interface SupportAdminV1CompaniesAllPostRequest {
    getCompaniesRequest: GetCompaniesRequest;
}

export interface SupportAdminV1DepartmentsAllPostRequest {
    getDepartmentsRequest: GetDepartmentsRequest;
}

export interface SupportAdminV1EmployeesDetailsPostRequest {
    getEmployeeDetailsRequest: GetEmployeeDetailsRequest;
}

export interface SupportAdminV1EmployeesEmployeeIdFullDetailsGetRequest {
    employeeId: string;
}

export interface SupportAdminV1EmployeesSearchPostRequest {
    searchEmployeeRequest: SearchEmployeeRequest;
}

export interface SupportAdminV1PositionsAllPostRequest {
    getPositionsRequestV2: GetPositionsRequestV2;
}

/**
 * 
 */
export class SupportAdminEmployeeViewsApi extends runtime.BaseAPI {

    /**
     */
    async supportAdminV1CompaniesAllPostRaw(requestParameters: SupportAdminV1CompaniesAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<CompanyPagedListResult>> {
        if (requestParameters['getCompaniesRequest'] == null) {
            throw new runtime.RequiredError(
                'getCompaniesRequest',
                'Required parameter "getCompaniesRequest" was null or undefined when calling supportAdminV1CompaniesAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/companies/all`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetCompaniesRequestToJSON(requestParameters['getCompaniesRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => CompanyPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1CompaniesAllPost(requestParameters: SupportAdminV1CompaniesAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<CompanyPagedListResult> {
        const response = await this.supportAdminV1CompaniesAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async supportAdminV1DepartmentsAllPostRaw(requestParameters: SupportAdminV1DepartmentsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<DepartmentPagedListResult>> {
        if (requestParameters['getDepartmentsRequest'] == null) {
            throw new runtime.RequiredError(
                'getDepartmentsRequest',
                'Required parameter "getDepartmentsRequest" was null or undefined when calling supportAdminV1DepartmentsAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/departments/all`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetDepartmentsRequestToJSON(requestParameters['getDepartmentsRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => DepartmentPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1DepartmentsAllPost(requestParameters: SupportAdminV1DepartmentsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<DepartmentPagedListResult> {
        const response = await this.supportAdminV1DepartmentsAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async supportAdminV1EmployeesDetailsPostRaw(requestParameters: SupportAdminV1EmployeesDetailsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeFullInfoModel>> {
        if (requestParameters['getEmployeeDetailsRequest'] == null) {
            throw new runtime.RequiredError(
                'getEmployeeDetailsRequest',
                'Required parameter "getEmployeeDetailsRequest" was null or undefined when calling supportAdminV1EmployeesDetailsPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/employees/details`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetEmployeeDetailsRequestToJSON(requestParameters['getEmployeeDetailsRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeFullInfoModelFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1EmployeesDetailsPost(requestParameters: SupportAdminV1EmployeesDetailsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeFullInfoModel> {
        const response = await this.supportAdminV1EmployeesDetailsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async supportAdminV1EmployeesEmployeeIdFullDetailsGetRaw(requestParameters: SupportAdminV1EmployeesEmployeeIdFullDetailsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeFullInfoModel>> {
        if (requestParameters['employeeId'] == null) {
            throw new runtime.RequiredError(
                'employeeId',
                'Required parameter "employeeId" was null or undefined when calling supportAdminV1EmployeesEmployeeIdFullDetailsGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/employees/{employeeId}/full-details`.replace(`{${"employeeId"}}`, encodeURIComponent(String(requestParameters['employeeId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeFullInfoModelFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1EmployeesEmployeeIdFullDetailsGet(requestParameters: SupportAdminV1EmployeesEmployeeIdFullDetailsGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeFullInfoModel> {
        const response = await this.supportAdminV1EmployeesEmployeeIdFullDetailsGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async supportAdminV1EmployeesSearchPostRaw(requestParameters: SupportAdminV1EmployeesSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<SearchEmployeePagedListResult>> {
        if (requestParameters['searchEmployeeRequest'] == null) {
            throw new runtime.RequiredError(
                'searchEmployeeRequest',
                'Required parameter "searchEmployeeRequest" was null or undefined when calling supportAdminV1EmployeesSearchPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/employees/search`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: SearchEmployeeRequestToJSON(requestParameters['searchEmployeeRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => SearchEmployeePagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1EmployeesSearchPost(requestParameters: SupportAdminV1EmployeesSearchPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<SearchEmployeePagedListResult> {
        const response = await this.supportAdminV1EmployeesSearchPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async supportAdminV1PositionsAllPostRaw(requestParameters: SupportAdminV1PositionsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<PositionPagedListResult>> {
        if (requestParameters['getPositionsRequestV2'] == null) {
            throw new runtime.RequiredError(
                'getPositionsRequestV2',
                'Required parameter "getPositionsRequestV2" was null or undefined when calling supportAdminV1PositionsAllPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/positions/all`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: GetPositionsRequestV2ToJSON(requestParameters['getPositionsRequestV2']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => PositionPagedListResultFromJSON(jsonValue));
    }

    /**
     */
    async supportAdminV1PositionsAllPost(requestParameters: SupportAdminV1PositionsAllPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<PositionPagedListResult> {
        const response = await this.supportAdminV1PositionsAllPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
