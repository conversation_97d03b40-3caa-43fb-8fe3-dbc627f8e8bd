/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  EmployeeInfoDto,
} from '../models/index';
import {
    EmployeeInfoDtoFromJSON,
    EmployeeInfoDtoToJSON,
} from '../models/index';

export interface AtrAdminV1EmployeesEmployeeIdEmployeeInfoGetRequest {
    employeeId: string;
}

/**
 * 
 */
export class EmployeesQueriesApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1EmployeesEmployeeIdEmployeeInfoGetRaw(requestParameters: AtrAdminV1EmployeesEmployeeIdEmployeeInfoGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<EmployeeInfoDto>> {
        if (requestParameters['employeeId'] == null) {
            throw new runtime.RequiredError(
                'employeeId',
                'Required parameter "employeeId" was null or undefined when calling atrAdminV1EmployeesEmployeeIdEmployeeInfoGet().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/employees/{employeeId}/employee-info`.replace(`{${"employeeId"}}`, encodeURIComponent(String(requestParameters['employeeId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => EmployeeInfoDtoFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1EmployeesEmployeeIdEmployeeInfoGet(requestParameters: AtrAdminV1EmployeesEmployeeIdEmployeeInfoGetRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<EmployeeInfoDto> {
        const response = await this.atrAdminV1EmployeesEmployeeIdEmployeeInfoGetRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
