/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ProblemDetails,
  UpsertFeatureRequest,
} from '../models/index';
import {
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    UpsertFeatureRequestFromJSON,
    UpsertFeatureRequestToJSON,
} from '../models/index';

export interface SupportAdminV1FeatureManagementPostRequest {
    upsertFeatureRequest: UpsertFeatureRequest;
}

/**
 * 
 */
export class SupportAdminFeatureManagementApi extends runtime.BaseAPI {

    /**
     */
    async supportAdminV1FeatureManagementGetRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/feature-management`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async supportAdminV1FeatureManagementGet(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.supportAdminV1FeatureManagementGetRaw(initOverrides);
    }

    /**
     */
    async supportAdminV1FeatureManagementPostRaw(requestParameters: SupportAdminV1FeatureManagementPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['upsertFeatureRequest'] == null) {
            throw new runtime.RequiredError(
                'upsertFeatureRequest',
                'Required parameter "upsertFeatureRequest" was null or undefined when calling supportAdminV1FeatureManagementPost().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/support-admin/v1/feature-management`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: UpsertFeatureRequestToJSON(requestParameters['upsertFeatureRequest']),
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async supportAdminV1FeatureManagementPost(requestParameters: SupportAdminV1FeatureManagementPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.supportAdminV1FeatureManagementPostRaw(requestParameters, initOverrides);
    }

}
