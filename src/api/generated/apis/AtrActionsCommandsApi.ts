/* tslint:disable */
/* eslint-disable */
/**
 * OneTalent.AdminService - PublicAPI
 * PublicAPI documentation
 *
 * The version of the OpenAPI document: 1.0.143
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ActionDto,
  ActionStatus,
  ActionType,
  ProblemDetails,
  ScheduleActionRequest,
} from '../models/index';
import {
    ActionDtoFromJSON,
    ActionDtoToJSON,
    ActionStatusFromJSON,
    ActionStatusToJSON,
    ActionTypeFromJSON,
    ActionTypeToJSON,
    ProblemDetailsFromJSON,
    ProblemDetailsToJSON,
    ScheduleActionRequestFromJSON,
    ScheduleActionRequestToJSON,
} from '../models/index';

export interface AtrAdminV1AdminsMeActionsActionIdPatchRequest {
    actionId: string;
}

export interface AtrAdminV1AdminsMeActionsCurrentInputFileDeleteRequest {
    actionType: ActionType;
}

export interface AtrAdminV1AdminsMeActionsCurrentInputFilePostRequest {
    actionType: ActionType;
    file: Blob;
}

export interface AtrAdminV1AdminsMeActionsPostRequest {
    actionType: ActionType;
    scheduleActionRequest: ScheduleActionRequest;
}

/**
 * 
 */
export class AtrActionsCommandsApi extends runtime.BaseAPI {

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdPatchRaw(requestParameters: AtrAdminV1AdminsMeActionsActionIdPatchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ActionStatus>> {
        if (requestParameters['actionId'] == null) {
            throw new runtime.RequiredError(
                'actionId',
                'Required parameter "actionId" was null or undefined when calling atrAdminV1AdminsMeActionsActionIdPatch().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/admins/me/actions/{actionId}`.replace(`{${"actionId"}}`, encodeURIComponent(String(requestParameters['actionId']))),
            method: 'PATCH',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ActionStatusFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1AdminsMeActionsActionIdPatch(requestParameters: AtrAdminV1AdminsMeActionsActionIdPatchRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ActionStatus> {
        const response = await this.atrAdminV1AdminsMeActionsActionIdPatchRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFileDeleteRaw(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFileDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1AdminsMeActionsCurrentInputFileDelete().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/admins/me/actions/current/input-file`,
            method: 'DELETE',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFileDelete(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFileDeleteRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.atrAdminV1AdminsMeActionsCurrentInputFileDeleteRaw(requestParameters, initOverrides);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFilePostRaw(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFilePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1AdminsMeActionsCurrentInputFilePost().'
            );
        }

        if (requestParameters['file'] == null) {
            throw new runtime.RequiredError(
                'file',
                'Required parameter "file" was null or undefined when calling atrAdminV1AdminsMeActionsCurrentInputFilePost().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const consumes: runtime.Consume[] = [
            { contentType: 'multipart/form-data' },
            { contentType: 'application/x-www-form-urlencoded' },
        ];
        // @ts-ignore: canConsumeForm may be unused
        const canConsumeForm = runtime.canConsumeForm(consumes);

        let formParams: { append(param: string, value: any): any };
        let useForm = false;
        // use FormData to transmit files using content-type "multipart/form-data"
        useForm = canConsumeForm;
        if (useForm) {
            formParams = new FormData();
        } else {
            formParams = new URLSearchParams();
        }

        if (requestParameters['file'] != null) {
            formParams.append('file', requestParameters['file'] as any);
        }

        const response = await this.request({
            path: `/atr-admin/v1/admins/me/actions/current/input-file`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: formParams,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsCurrentInputFilePost(requestParameters: AtrAdminV1AdminsMeActionsCurrentInputFilePostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.atrAdminV1AdminsMeActionsCurrentInputFilePostRaw(requestParameters, initOverrides);
    }

    /**
     */
    async atrAdminV1AdminsMeActionsPostRaw(requestParameters: AtrAdminV1AdminsMeActionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ActionDto>> {
        if (requestParameters['actionType'] == null) {
            throw new runtime.RequiredError(
                'actionType',
                'Required parameter "actionType" was null or undefined when calling atrAdminV1AdminsMeActionsPost().'
            );
        }

        if (requestParameters['scheduleActionRequest'] == null) {
            throw new runtime.RequiredError(
                'scheduleActionRequest',
                'Required parameter "scheduleActionRequest" was null or undefined when calling atrAdminV1AdminsMeActionsPost().'
            );
        }

        const queryParameters: any = {};

        if (requestParameters['actionType'] != null) {
            queryParameters['actionType'] = requestParameters['actionType'];
        }

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.apiKey) {
            headerParameters["Authorization"] = await this.configuration.apiKey("Authorization"); // oauth2 authentication
        }

        const response = await this.request({
            path: `/atr-admin/v1/admins/me/actions`,
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: ScheduleActionRequestToJSON(requestParameters['scheduleActionRequest']),
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ActionDtoFromJSON(jsonValue));
    }

    /**
     */
    async atrAdminV1AdminsMeActionsPost(requestParameters: AtrAdminV1AdminsMeActionsPostRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ActionDto> {
        const response = await this.atrAdminV1AdminsMeActionsPostRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
