import { useMemo } from 'react';

import { useAppConfig } from '@/shared/contexts';
import { useFetchApi } from '@/shared/core/domainModel';

import * as AdminService from './generated';

function useConfig() {
  const fetchApi = useFetchApi();
  const { appConfig } = useAppConfig();

  const baseConfig = useMemo(
    () => ({
      basePath: appConfig.FE_API_URL,
      fetchApi
    }),
    [appConfig, fetchApi]
  );

  return useMemo(
    () => ({
      config: new AdminService.Configuration(baseConfig)
    }),
    [baseConfig]
  );
}

export function useAdminServiceApi() {
  const { config } = useConfig();

  return useMemo(
    () => ({
      atr: {
        employeesApi: new AdminService.EmployeesQueriesApi(config),
        atrActionsCommandsApi: new AdminService.AtrActionsCommandsApi(config),
        atrActionsQueriesApi: new AdminService.AtrActionsQueriesApi(config),
        atrTemplatesQueriesApi: new AdminService.AtrTemplatesQueriesApi(config),
        atrPerformanceFormsApi: new AdminService.AtrPerformanceFormsApi(config)
      },
      support: {
        employeeViewsApi: new AdminService.SupportAdminEmployeeViewsApi(config)
      },
      shared: {
        sharedApi: new AdminService.SharedApi(config)
      }
    }),
    [config]
  );
}

export function useAtrAdminServiceApi() {
  const { atr } = useAdminServiceApi();
  return atr;
}

export function useSupportAdminServiceApi() {
  const { support } = useAdminServiceApi();
  return support;
}

export function useSharedServiceApi() {
  const { shared } = useAdminServiceApi();
  return shared;
}

export type AdminServiceApi = ReturnType<typeof useAdminServiceApi>;
