using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.AtrAdmin.Services;

public interface IAtrAdminQueryService
{
    Task<PagedListResult<Action>> SearchMyActionsAsync(SearchActionsQuery query, CancellationToken cancellationToken);

    Task<ActionStatus?> GetMyActionStatusAsync(ItemId actionId, CancellationToken cancellationToken);

    Task<AtrAdminFileInfo?> GetMyUploadedFileInfoAsync(FileActionType fileActionType, CancellationToken cancellationToken);

    Task<AtrAdminFile?> DownloadMyUploadedFileAsync(FileActionType fileActionType, CancellationToken cancellationToken);

    Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken);

    Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken);

    Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken);
}
