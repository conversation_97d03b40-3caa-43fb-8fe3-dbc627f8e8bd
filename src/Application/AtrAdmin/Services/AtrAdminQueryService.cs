using OneTalent.AtrAdminService.Application.Actions.Models;
using OneTalent.AtrAdminService.Application.Actions.Models.Queries;
using OneTalent.AtrAdminService.Application.Actions.Services;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Services;
using OneTalent.AtrAdminService.Application.Files.Models;
using OneTalent.AtrAdminService.Application.Files.Repositories;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Services;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Services;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.Common.Extensions.Paging;
using Action = OneTalent.AtrAdminService.Application.Actions.Models.Action;

namespace OneTalent.AtrAdminService.Application.AtrAdmin.Services;

public class AtrAdminQueryService(
    IEmployeeQueryService employeeQueryService,
    IActionQueryService actionQueryService,
    IFileStorageRepository fileStorageRepository,
    IUploadedFileInfoReadRepository uploadedFileInfoRepository,
    IPerformanceFormQueryService performanceFormQueryService,
    IPerformanceFormHistoryQueryService historyQueryService
) : IAtrAdminQueryService
{
    public Task<PagedListResult<Action>> SearchMyActionsAsync(SearchActionsQuery query, CancellationToken cancellationToken) =>
        actionQueryService.SearchActionsAsync(query, cancellationToken);

    public Task<ActionStatus?> GetMyActionStatusAsync(ItemId actionId, CancellationToken cancellationToken) =>
        actionQueryService.GetActionStatusAsync(actionId, cancellationToken);

    public async Task<AtrAdminFileInfo?> GetMyUploadedFileInfoAsync(FileActionType fileActionType,
        CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        return await uploadedFileInfoRepository.GetAsync(employee.EmployeeId, fileActionType, cancellationToken);
    }

    public async Task<AtrAdminFile?> DownloadMyUploadedFileAsync(FileActionType fileActionType,
        CancellationToken cancellationToken)
    {
        var employee = await employeeQueryService.GetCurrentUserEmployeeAsync(cancellationToken);

        var fileInfo = await uploadedFileInfoRepository.GetAsync(employee.EmployeeId, fileActionType, cancellationToken);

        if (fileInfo is null)
        {
            return null;
        }

        var stream = await fileStorageRepository.DownloadAsync(fileInfo.FilePath, cancellationToken);

        return new AtrAdminFile(fileInfo.Name, stream);
    }

    public async Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken) =>
        await performanceFormQueryService.GetPerformanceFormStatusesAsync(cancellationToken);

    public async Task<NamedOptions[]> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken) =>
        await performanceFormQueryService.GetPerformanceFormTemplatesAsync(search, cancellationToken);

    public async Task<NamedOptions[]> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken) =>
        await performanceFormQueryService.GetPerformanceFormPermissionGroupsAsync(search, cancellationToken);

    public async Task<NamedOptions[]> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken) =>
        await performanceFormQueryService.GetPerformanceFormEmployeesAsync(search, option, cancellationToken);

    public async Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken) 
    {
        var atrForms = await performanceFormQueryService.SearchPerformanceFormsAsync(employeeId, searchQuery, cancellationToken);
        foreach (var item in atrForms.Items)
        {
            item.EmployeeFullName = await LoadEmployeeNameAsync(item.EmployeeId, cancellationToken);
            item.AssessmentLineManagerName = await LoadEmployeeNameAsync(item.AssessmentLineManagerId, cancellationToken);
            item.AssessmentB2BManagerName = item.AssessmentB2BManagerId is not null ? await LoadEmployeeNameAsync(item.AssessmentB2BManagerId, cancellationToken) : null;
            item.DottedLineManagerName = item.DottedLineManagerId is not null ? await LoadEmployeeNameAsync(item.DottedLineManagerId, cancellationToken) : null;
            item.CompanyName = await LoadCompanyNameAsync(item.CompanyCode, cancellationToken);
            item.FunctionName = await LoadFunctionNameAsync(item.FunctionId, cancellationToken);
            item.DivisionName = await LoadDivisionNameAsync(item.DivisionId, cancellationToken);
            item.DirectorateName = item.DirectorateId is not null ? await LoadDirectorateNameAsync(item.DirectorateId, cancellationToken) : null;
        }

        return atrForms;
    }

    private async Task<string> LoadCompanyNameAsync(string companyCode, CancellationToken cancellationToken) =>
      (await employeeQueryService.GetFilteredCompaniesAsync("", cancellationToken)).FirstOrDefault(x => x.Id == companyCode)?.Name;
    
    private async Task<string> LoadFunctionNameAsync(string companyCode, CancellationToken cancellationToken) =>
      (await employeeQueryService.GetFilteredFunctionsAsync("", cancellationToken)).FirstOrDefault(x => x.Id == companyCode)?.Name;
    
    private async Task<string> LoadDivisionNameAsync(string companyCode, CancellationToken cancellationToken) =>
      (await employeeQueryService.GetFilteredDivisionsAsync("", cancellationToken)).FirstOrDefault(x => x.Id == companyCode)?.Name;
    
    private async Task<string?> LoadDirectorateNameAsync(string companyCode, CancellationToken cancellationToken) =>
      (await employeeQueryService.GetFilteredDirectoratesAsync("", cancellationToken)).FirstOrDefault(x => x.Id == companyCode)?.Name;

    private async Task<string> LoadEmployeeNameAsync(string eployeeId, CancellationToken cancellationToken) =>
      (await employeeQueryService.GetActiveOrTerminatedEmployeeByIdAsync(eployeeId, cancellationToken) as Employee)?.FullNameEnglish;

    public async Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken) =>
        await historyQueryService.GetPerformanceFormHistoryAsync(query, cancellationToken);
}
