using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.Common.Extensions.Paging;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;

public interface IAtrApiRepository
{
    Task<PerformanceFormStatus[]> GetAssessmentStatusesAsync(CancellationToken cancellationToken);

    Task<PagedListResult<AssessmentForm>> SearchAssessmentsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken);

    Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken);
}
