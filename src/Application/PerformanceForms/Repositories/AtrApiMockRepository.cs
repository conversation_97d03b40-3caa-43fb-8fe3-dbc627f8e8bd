using IdGen;
using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.Common.Extensions;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Contracts.Constants;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;

public class AtrApiMockRepository: IAtrApiRepository
{
    public Task<PerformanceFormStatus[]> GetAssessmentStatusesAsync(CancellationToken cancellationToken)
    {
        var statuses = LookupOptionExtension.ToLookupOptions<PerformanceFormStatuses>();

        return Task.FromResult(statuses.Select(s => new PerformanceFormStatus(
            s.Id,
            s.Name)
        ).ToArray());
    }

    public Task<PagedListResult<AssessmentForm>> SearchAssessmentsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken)
    {
        return Task.FromResult(new PagedListResult<AssessmentForm>(
            new OffsetPage(1, 1), 
            new[] { new AssessmentForm{
                Id = "1001",
                TemplateId = "1002",
                TemplateName = "TemplateName",
               PerformanceFormStatusId = "1",
                EmployeeId = "1001",
                EmployeeFullName = "EmployeeFullName",
                CompanyCode = "CompanyCode",
                CompanyName = "CompanyName",
                DirectorateId = "DirectorateId",
                DirectorateName = "DirectorateName",
                FunctionId = "FunctionId",
                FunctionName = "FunctionName",
                DivisionId = "DivisionId",
                DivisionName = "DivisionName",
                AtrGroupName = "AtrGroupName",
                AssessmentLineManagerId = "1002",
                AssessmentLineManagerName = "AssessmentLineManagerName",
                AssessmentB2BManagerId = "1003",
                AssessmentB2BManagerName = "AssessmentB2BManagerName",
                DottedLineManagerId = "1004",
                DottedLineManagerName = "DottedLineManagerName",
                LastUpdated = DateTimeOffset.Now,
                UpdatedBy = "1002"} }, 
            1));
    }

    public Task<PagedListResult<PerformanceFormHistoryRecord>> GetPerformanceFormHistoryAsync(PerformanceFormHistoryQuery query, CancellationToken cancellationToken)
    {
        var history = MockPerformanceFormHistory.PerformanceFormHistory
            .Select(record => record with { PerformanceFormId = query.PerformanceFormId })
            .OrderByDescending(x => x.ActionDateTime);

        var pagedHistory = history
            .Skip((query.PageNumber - 1) * query.PageSize)
            .Take(query.PageSize);

        return Task.FromResult(new PagedListResult<PerformanceFormHistoryRecord>(
            new PaginationData(
                query.PageNumber,
                query.PageSize,
                pagedHistory.Count()),
            pagedHistory));
    }

    public Task<NamedOptions[]> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken)
    {
        //TODO: mock data
       var result = new List<NamedOptions> { new("1", "Test Template") }.ToArray();
        return Task.FromResult(result);
    }

    public Task<NamedOptions[]> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken)
    {
        //TODO: mock data
        var result = new List<NamedOptions> { new("1", "Test Template") }.ToArray();
        return Task.FromResult(result);
    }

    public Task<NamedOptions[]> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken)
    {
        //TODO: mock data
        var result = new List<NamedOptions> { new("1", "Test Employee") }.ToArray();
        return Task.FromResult(result);
    }
}
