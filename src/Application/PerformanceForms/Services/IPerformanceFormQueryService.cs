using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Services;

public interface IPerformanceFormQueryService
{
    Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken);

    Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken);

    Task<NamedOptions[]> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken);
}
