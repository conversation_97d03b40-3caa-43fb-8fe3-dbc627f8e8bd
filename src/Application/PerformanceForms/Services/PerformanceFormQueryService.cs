using OneTalent.AtrAdminService.Application.AtrAdmin.Models;
using OneTalent.AtrAdminService.Application.Employees.Queries.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Repositories;
using OneTalent.Common.Extensions.Paging;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Services;

public class PerformanceFormQueryService(
    IAtrApiRepository atrApiRepository)
    : IPerformanceFormQueryService
{
    public async Task<PerformanceFormStatus[]> GetPerformanceFormStatusesAsync(CancellationToken cancellationToken) =>
        await atrApiRepository.GetAssessmentStatusesAsync(cancellationToken);

    public async Task<PagedListResult<AssessmentForm>> SearchPerformanceFormsAsync(string employeeId, PerformanceFormSearchQuery searchQuery, CancellationToken cancellationToken) =>
        await atrApiRepository.SearchAssessmentsAsync(employeeId, searchQuery, cancellationToken);

    public async Task<NamedOptions[]?> GetPerformanceFormTemplatesAsync(string search, CancellationToken cancellationToken) =>
        await atrApiRepository.GetPerformanceFormTemplatesAsync(search, cancellationToken);

    public async Task<NamedOptions[]?> GetPerformanceFormPermissionGroupsAsync(string search, CancellationToken cancellationToken) =>
        await atrApiRepository.GetPerformanceFormPermissionGroupsAsync(search, cancellationToken);

    public async Task<NamedOptions[]?> GetPerformanceFormEmployeesAsync(string search, SearchEmployeeOptions option, CancellationToken cancellationToken) =>
        await atrApiRepository.GetPerformanceFormEmployeesAsync(search, option, cancellationToken);
}
