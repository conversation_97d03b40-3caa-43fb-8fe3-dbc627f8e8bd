using OneTalent.Common.Extensions.Sorting;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Models;

public record PerformanceFormSearchQuery(
    string[]? FormIds,
    string[]? DivisionIds,
    string[]? CompanyCodes,
    string[]? DirectorateIds,
    string[]? FunctionIds,
    string[]? TemplateIds,
    string[]? PermissionGroupIds,
    string[]? SearchEmployeeIds,
    string[]? LineManagerIds, 
    string[]? B2BManagerIds,
    string[]? DottetLineManagerIds,
    string[]? SelfAssessmentStatuses,
    string[]? PerformanceFormStatuses,
    int PageNumber,
    int PageSize,
    OrderBy? OrderBy);
