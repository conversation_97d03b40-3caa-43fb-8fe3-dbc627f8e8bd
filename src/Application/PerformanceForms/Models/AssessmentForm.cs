using OneTalent.AtrAdminService.Contracts.Constants;

namespace OneTalent.AtrAdminService.Application.PerformanceForms.Models;

public class AssessmentForm
{
    public string Id { get; set; }
    public string TemplateId { get; set; }
    public string TemplateName { get; set; }
    public string PerformanceFormStatusId { get; set; }
    public string EmployeeId { get; set; }
    public string EmployeeFullName { get; set; }
    public string CompanyCode { get; set; }
    public string CompanyName { get; set; }
    public string? DirectorateId { get; set; }
    public string? DirectorateName { get; set; }
    public string FunctionId { get; set; }
    public string FunctionName { get; set; }
    public string DivisionId { get; set; }
    public string DivisionName { get; set; }
    public string AtrGroupName { get; set; }
    public string AssessmentLineManagerId { get; set; }
    public string AssessmentLineManagerName { get; set; }
    public string? AssessmentB2BManagerId { get; set; }
    public string? AssessmentB2BManagerName { get; set; }
    public string? DottedLineManagerId { get; set; }
    public string? DottedLineManagerName { get; set; }
    public DateTimeOffset? LastUpdated { get; set; }
    public string? UpdatedBy { get; set; }
}
