using OneTalent.AtrAdminService.Application.Employees.Queries.Models;

namespace OneTalent.AtrAdminService.Application.Employees.Queries.Services;

public interface IEmployeeQueryService
{
    Task<IEmployee> GetActiveOrTerminatedEmployeeByIdAsync(string employeeId, CancellationToken cancellationToken);
    Task<Employee> GetCurrentUserEmployeeAsync(CancellationToken cancellationToken);
    Task<NamedOptions[]> GetFilteredCompaniesAsync(string search, CancellationToken cancellationToken);
    Task<NamedOptions[] > GetFilteredDirectoratesAsync(string search, CancellationToken cancellationToken);
    Task<NamedOptions[]> GetFilteredFunctionsAsync(string search, CancellationToken cancellationToken);
    Task<NamedOptions[]> GetFilteredDivisionsAsync(string search, CancellationToken cancellationToken);
}
