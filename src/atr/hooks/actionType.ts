import { useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { ActionType } from '@/atr/domain';

function getActionTypeFromSearch(search: string): ActionType {
  const params = new URLSearchParams(search);
  const value = params.get('actionType');
  if (value && Object.values(ActionType).includes(value as ActionType)) {
    return value as ActionType;
  }
  return ActionType.Launch; // default
}

export function useActionTypeQueryParam() {
  const location = useLocation();
  const navigate = useNavigate();
  const actionType = getActionTypeFromSearch(location.search);

  const setActionType = useCallback(
    (newActionType: ActionType) => {
      const params = new URLSearchParams(location.search);
      params.set('actionType', newActionType);
      navigate({ search: params.toString() }, { replace: true });
    },
    [location.search, navigate]
  );

  return { actionType, setActionType } as const;
}
