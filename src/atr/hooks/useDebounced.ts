import { useCallback, useRef, useState } from 'react';

export function useDebounced<T>({
  initialValue,
  wait = 300
}: {
  initialValue: T;
  wait?: number;
}) {
  const [debouncedValue, setValue] = useState(initialValue);
  const debouncedRef = useRef<NodeJS.Timeout>();

  const handleSetValue = useCallback((value: T) => {
    setValue(value);
    debouncedRef.current && clearTimeout(debouncedRef.current);
  }, []);

  const handleSetDebouncedValue = useCallback(
    (value: T) => {
      debouncedRef.current = setTimeout(() => {
        setValue(value);
      }, wait);
    },
    [wait]
  );

  return {
    debouncedValue,
    setDebouncedValue: handleSetDebouncedValue,
    setValue: handleSetValue
  };
}
