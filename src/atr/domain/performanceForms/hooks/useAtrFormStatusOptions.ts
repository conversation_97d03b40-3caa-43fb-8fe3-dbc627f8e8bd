import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { STALE_TIME } from 'shared/constants';
import { logger } from 'shared/logger';

import { useAtrAdminServiceApi } from '@/api';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

export const useAtrFormStatusOptions = () => {
  const { atrPerformanceFormsApi: atrPerformanceFormsQueriesApi } =
    useAtrAdminServiceApi();
  const queryFn = useCallback(async () => {
    try {
      return atrPerformanceFormsQueriesApi.atrAdminV1PerformanceFormsStatusesGet();
    } catch (error) {
      logger.error(
        'Failed to load atrAdminV1PerformanceFormsStatusesGet',
        error
      );

      throw error;
    }
  }, [atrPerformanceFormsQueriesApi]);

  return useQuery({
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.getStatusOptions(),
    queryFn,
    select: (rawResponse) =>
      rawResponse.map((statusOption) => ({
        id: statusOption.id,
        displayName: statusOption.name
      })),
    staleTime: STALE_TIME.SESSION
  });
};
