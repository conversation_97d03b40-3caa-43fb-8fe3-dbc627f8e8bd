import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { AdminService, useAtrAdminServiceApi } from '@/api';
import { logger } from '@/shared/logger';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

export interface DeletePerformanceFormParams {
  performanceFormId: string;
  reason: string;
}

export interface UseDeletePerformanceFormOptions {
  onSuccess?: () => void;
  onError?: () => void;
}

export const useDeletePerformanceForm = (
  options: UseDeletePerformanceFormOptions = {}
) => {
  const { atrPerformanceFormsApi } = useAtrAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();
  const { onSuccess, onError } = options;

  return useMutation({
    mutationFn: async ({ performanceFormId, reason }: DeletePerformanceFormParams) => {
      try {
        const deleteRequest: AdminService.DeletePerformanceFormRequest = {
          reason
        };

        return await atrPerformanceFormsApi.atrAdminV1PerformanceFormsPerformanceFormsPerformanceFormIdDelete(
          {
            performanceFormId,
            deletePerformanceFormRequest: deleteRequest
          }
        );
      } catch (error) {
        logger.error(
          'Failed to delete performance form',
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate and refetch performance forms data
      queryClient.invalidateQueries({
        queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.all()
      });

      // Show success toast
      addToast({
        variant: ToastVariant.Success,
        title: 'Form was successfully deleted'
      });

      onSuccess?.();
    },
    onError: (error) => {
      logger.error('Delete performance form failed', error);
      
      // Show error toast
      addToast({
        variant: ToastVariant.Error,
        title: 'An error occurred, form was not deleted'
      });

      onError?.();
    }
  });
};
