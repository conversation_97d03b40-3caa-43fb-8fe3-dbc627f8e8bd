import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { logger } from 'shared/logger';

import { useAtrAdminServiceApi } from '@/api';

import { PerformanceFormsSearchPayload } from '../models';
import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

import { STALE_TIME } from '../../../../shared/constants';

export const useAtrFormIdOptions = (
  payload: Pick<PerformanceFormsSearchPayload, 'formIds'>,
  enabled = true
) => {
  const { atrPerformanceFormsApi: atrPerformanceFormsQueriesApi } =
    useAtrAdminServiceApi();
  const queryFn = useCallback(
    async (input: PerformanceFormsSearchPayload) => {
      try {
        return atrPerformanceFormsQueriesApi.atrAdminV1PerformanceFormsSearchPost(
          { performanceFormSearchQuery: input }
        );
      } catch (error) {
        logger.error('Failed to load v1PerformanceFormsSearchPost', error);

        throw error;
      }
    },
    [atrPerformanceFormsQueriesApi]
  );

  return useQuery({
    enabled,
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.getAtrFormIdSearch(payload),
    queryFn: () => queryFn(payload),
    select: (rawResponse) =>
      rawResponse.items.map((atrFormIdOption) => ({
        id: atrFormIdOption.id,
        displayName: atrFormIdOption.id
      })),
    staleTime: STALE_TIME.MINUTES.TEN
  });
};
