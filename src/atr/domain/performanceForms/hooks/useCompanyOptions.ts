import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { STALE_TIME } from 'shared/constants';
import { logger } from 'shared/logger';

import { useAtrAdminServiceApi } from '@/api';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

export const useCompanyOptions = (search?: string) => {
  const { atrPerformanceFormsApi: atrPerformanceFormsQueriesApi } =
    useAtrAdminServiceApi();
  const queryFn = useCallback(
    async (search?: string) => {
      try {
        return atrPerformanceFormsQueriesApi.atrAdminV1PerformanceFormsCompaniesGet(
          search
            ? {
                search
              }
            : undefined
        );
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsCompaniesGet',
          error
        );

        throw error;
      }
    },
    [atrPerformanceFormsQueriesApi]
  );

  return useQuery({
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.getCompanyOptions(search),
    queryFn: () => queryFn(search),
    select: (rawResponse) =>
      rawResponse.map((option) => ({
        id: option.id,
        displayName: option.name
      })),
    staleTime: STALE_TIME.MINUTES.TEN
  });
};
