import { useQuery } from '@tanstack/react-query';

import { logger } from 'shared/logger';

import { useAtrAdminServiceApi } from '@/api';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

interface Options {
  enabled?: boolean;
}

export const usePerformanceFormById = (
  id: string,
  { enabled = true }: Options = {}
) => {
  const { atrPerformanceFormsApi: atrPerformanceFormsQueriesApi } =
    useAtrAdminServiceApi();

  return useQuery({
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.performanceFormById(id),
    queryFn: async () => {
      try {
        const { items } =
          await atrPerformanceFormsQueriesApi.atrAdminV1PerformanceFormsSearchPost(
            { performanceFormSearchQuery: { formIds: [id] } }
          );

        return items[0];
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsSearchPost',
          error
        );

        throw error;
      }
    },
    enabled
  });
};
