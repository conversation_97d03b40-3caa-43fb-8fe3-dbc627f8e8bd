import { logger } from '@services/logger';
import { useQuery } from '@tanstack/react-query';

import { AdminService, useAtrAdminServiceApi } from '@/api';
import { transformPaginationData } from '@/shared/core/domainModel';

import { PERFORMANCE_FORMS_REQUEST_KEYS } from '../performanceFormsRequestKeys';

interface Options {
  enabled?: boolean;
}

export const usePerformanceFormHistory = (
  input: AdminService.AtrAdminV1PerformanceFormsPerformanceFormIdHistoryPostRequest,
  { enabled = true }: Options = {}
) => {
  const { atrPerformanceFormsApi: atrPerformanceFormsQueriesApi } =
    useAtrAdminServiceApi();

  return useQuery({
    queryKey: PERFORMANCE_FORMS_REQUEST_KEYS.performanceFormHistory(input),
    queryFn: async () => {
      try {
        const { items, paging } =
          await atrPerformanceFormsQueriesApi.atrAdminV1PerformanceFormsPerformanceFormIdHistoryPost(
            input
          );

        return {
          // TODO: remove the mapper once the `id` property is added to the `PerformanceFormHistoryDto`
          items: items.map((item, index) => ({ ...item, id: `${index}` })),
          page: transformPaginationData(paging)
        };
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsPerformanceFormIdHistoryPost',
          error
        );

        throw error;
      }
    },
    enabled
  });
};
