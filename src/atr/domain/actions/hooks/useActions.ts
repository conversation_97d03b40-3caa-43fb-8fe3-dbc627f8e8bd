import { logger } from '@services/logger';
import { useQuery } from '@tanstack/react-query';

import { AdminService, useAtrAdminServiceApi } from '@/api';
import { DEFAULT_REQUESTS_PROPS } from '@/shared/constants';
import { transformPaginationData } from '@/shared/core/domainModel';

import { ACTIONS_REQUEST_KEYS } from '../actionsRequestKeys';

export const useActions = (
  input: AdminService.SearchMyActionsRequest = DEFAULT_REQUESTS_PROPS
) => {
  const { atrActionsQueriesApi } = useAtrAdminServiceApi();

  return useQuery({
    queryKey: ACTIONS_REQUEST_KEYS.actions(input),
    queryFn: async () => {
      try {
        const { items, paging } =
          await atrActionsQueriesApi.atrAdminV1AdminsMeActionsSearchPost({
            searchMyActionsRequest: input
          });

        return { items, page: transformPaginationData(paging) };
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1AdminsMeActionsSearchPost',
          error
        );

        throw error;
      }
    }
  });
};
