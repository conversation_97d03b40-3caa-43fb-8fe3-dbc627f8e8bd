import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { ActionType } from '@/atr/domain';
import { logger } from '@/shared/logger';

import { EMPLOYEES_FILES } from '../filesRequestKeys';

export const useDeleteMyFile = (type: ActionType) => {
  const { atrActionsCommandsApi } = useAtrAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();

  return useMutation({
    mutationFn: async () => {
      try {
        await atrActionsCommandsApi.atrAdminV1AdminsMeActionsCurrentInputFileDelete(
          {
            actionType: type
          }
        );
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1AdminsMeActionsCurrentInputFileDelete',
          error
        );

        throw error;
      }
    },
    onSuccess: () => {
      queryClient.setQueryData(EMPLOYEES_FILES.getMyFiles(type), () => null);
    },
    onError: () => {
      addToast({
        variant: ToastVariant.Error,
        title:
          'An error occurred, the file was not deleted. Please try again or contact support if the issue persists'
      });
    }
  });
};
