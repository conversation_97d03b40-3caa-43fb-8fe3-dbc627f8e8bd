import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { ACTIONS_REQUEST_KEYS } from '@/atr/domain/actions/actionsRequestKeys';
import {
  ActionModel,
  ActionStatus,
  ActionType
} from '@/atr/domain/actions/models';
import { ScheduleActionRequest } from '@/atr/domain/files/';
import { EMPLOYEES_FILES } from '@/atr/domain/files/filesRequestKeys';
import { logger } from '@/shared/logger';

export const useMyFileProcessing = (type: ActionType) => {
  const { atrActionsCommandsApi } = useAtrAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();

  return useMutation({
    mutationFn: async (model: ScheduleActionRequest) => {
      try {
        return await atrActionsCommandsApi.atrAdminV1AdminsMeActionsPost({
          actionType: type,
          scheduleActionRequest: model
        });
      } catch (error) {
        logger.error('Failed to load atrAdminV1AdminsMeActionsPost', error);

        throw error;
      }
    },
    onSuccess: (data) => {
      queryClient.setQueryData(EMPLOYEES_FILES.getMyFiles(type), () => data);
      queryClient.invalidateQueries({
        queryKey: ACTIONS_REQUEST_KEYS.all()
      });
    },
    onError: (e: unknown) => {
      const error = e as { response?: { status?: number } };
      if (error.response?.status === 409) {
        addToast({
          variant: ToastVariant.Error,
          title:
            'The action has already been initiated. Please refresh the page'
        });
      } else {
        queryClient.setQueryData(
          EMPLOYEES_FILES.getMyFiles(type),
          (old: ActionModel) => ({ ...old, status: ActionStatus.Failed })
        );
      }
    }
  });
};
