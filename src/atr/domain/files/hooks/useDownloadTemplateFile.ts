import { useAtrAdminServiceApi } from '@/api';
import { ActionType } from '@/atr/domain';
import { getFilenameFromContentDisposition } from '@/atr/domain/files/utils';
import { logger } from '@/shared/logger';
import { downloadFile } from '@/shared/utils';

export function useDownloadTemplateFile(type: ActionType) {
  const { atrTemplatesQueriesApi } = useAtrAdminServiceApi();
  return async () => {
    try {
      const response = await atrTemplatesQueriesApi.atrAdminV1TemplatesGetRaw({
        actionType: type
      });

      const contentDisposition = response.raw.headers.get(
        'content-disposition'
      );
      const parsedFilename =
        getFilenameFromContentDisposition(contentDisposition);

      const blob = await response.raw.blob();

      downloadFile(blob, parsedFilename ?? 'template file.csv');
    } catch (error) {
      logger.error('Failed to load atrAdminV1MeTemplatesGet', error);
      throw error;
    }
  };
}
