import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';
import { useMutation, useQueryClient } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { ActionType } from '@/atr/domain/actions';
import { logger } from '@/shared/logger';

import { EMPLOYEES_FILES } from '../filesRequestKeys';

export const useUploadFile = (type: ActionType) => {
  const { atrActionsCommandsApi } = useAtrAdminServiceApi();
  const queryClient = useQueryClient();
  const { addToast } = useToast();

  return useMutation({
    mutationKey: ['uploadFile', type],
    mutationFn: async (file: File) => {
      try {
        await atrActionsCommandsApi.atrAdminV1AdminsMeActionsCurrentInputFilePost(
          {
            actionType: type,
            file
          }
        );
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1AdminsMeActionsCurrentInputFilePost',
          error
        );
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: EMPLOYEES_FILES.getMyFiles(type)
      });
    },
    onError: () => {
      addToast({
        variant: ToastVariant.Error,
        title:
          'An error occurred, the file was not uploaded. Please verify the file and try again or contact support if the issue persists'
      });
    }
  });
};
