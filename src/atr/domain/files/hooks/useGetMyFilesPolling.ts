import { useEffect, useRef } from 'react';

import {
  isActionStatusCanceledOrFailed,
  isActionStatusCancelling,
  isActionStatusCompleted,
  isActionStatusPendingOrInProgress
} from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

export const useGetMyFilesPolling = () => {
  const { myFile, refetchMyFile } = useMyFilesActions();

  const pollingRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Start polling when process in progress
    if (
      isActionStatusPendingOrInProgress(myFile?.status) ||
      isActionStatusCancelling(myFile?.status)
    ) {
      if (!pollingRef.current) {
        pollingRef.current = setInterval(() => {
          refetchMyFile();
        }, 20000);
      }
    }
    // Stop polling when completed
    if (
      (isActionStatusCompleted(myFile?.status) ||
        isActionStatusCanceledOrFailed(myFile?.status)) &&
      pollingRef.current
    ) {
      clearInterval(pollingRef.current);
      pollingRef.current = null;
    }
    return () => {
      if (pollingRef.current) {
        clearInterval(pollingRef.current);
        pollingRef.current = null;
      }
    };
  }, [myFile, refetchMyFile]);
};
