import { useQuery } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { ActionType } from '@/atr/domain';
import { logger } from '@/shared/logger';

import { EMPLOYEES_FILES } from '../filesRequestKeys';

export function useMyFiles(type: ActionType) {
  const { atrActionsQueriesApi } = useAtrAdminServiceApi();

  return useQuery({
    queryKey: EMPLOYEES_FILES.getMyFiles(type),
    queryFn: async () => {
      try {
        return await atrActionsQueriesApi.atrAdminV1AdminsMeActionsCurrentGet({
          actionType: type
        });
      } catch (error) {
        logger.error('Failed to load v1AdminsMeFilesFileActionTypeGet', error);

        throw error;
      }
    }
  });
}
