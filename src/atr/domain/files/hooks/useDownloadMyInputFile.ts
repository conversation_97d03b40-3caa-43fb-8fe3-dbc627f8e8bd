import { useAtrAdminServiceApi } from '@/api';
import { ActionModel } from '@/atr/domain';
import { getFilenameFromContentDisposition } from '@/atr/domain/files/utils';
import { logger } from '@/shared/logger';
import { downloadFile } from '@/shared/utils';

export function useDownloadMyInputFile() {
  const { atrActionsQueriesApi } = useAtrAdminServiceApi();

  return async (actionId: NonNullable<ActionModel['id']>) => {
    try {
      const response =
        await atrActionsQueriesApi.atrAdminV1AdminsMeActionsActionIdInputFileGetRaw(
          {
            actionId: actionId.toString()
          }
        );

      const contentDisposition = response.raw.headers.get(
        'content-disposition'
      );
      const parsedFilename =
        getFilenameFromContentDisposition(contentDisposition);

      const blob = await response.raw.blob();

      downloadFile(blob, parsedFilename ?? 'input file.csv');
    } catch (error) {
      logger.error(
        'Failed to load atrAdminV1AdminsMeActionsActionIdInputFileGet',
        error
      );
      throw error;
    }
  };
}
