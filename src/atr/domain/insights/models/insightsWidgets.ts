import { InsightsDto } from '../../../../api/generated';

export const insightsWidgetsNumber = (insights?: InsightsDto) => {
  const unratedCount =
    insights &&
    insights.totalAssessments -
      insights.completedSelfAssessments -
      insights.completedManagerAssessments;

  const completedSelfAssesmentsPercentage =
    insights &&
    (
      (insights.completedSelfAssessments / insights.totalAssessments) *
      100
    ).toFixed(2);

  const completedManagerAssesmentsPercentage =
    insights &&
    (
      (insights.completedManagerAssessments / insights.totalAssessments) *
      100
    ).toFixed(2);

  const completedUnratedPercentage =
    unratedCount &&
    ((unratedCount / insights.totalAssessments) * 100).toFixed(2);
  return {
    unratedCount,
    completedSelfAssesmentsPercentage,
    completedManagerAssesmentsPercentage,
    completedUnratedPercentage
  };
};
