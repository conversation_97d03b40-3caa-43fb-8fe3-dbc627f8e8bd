import { useQuery } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api/useAdminServiceApi';
import { logger } from '@/shared/logger';

import { INSIGHTS_KEYS } from './insightsKeys';

export const useGetInsights = () => {
  const { atrPerformanceFormsApi } = useAtrAdminServiceApi();

  return useQuery({
    queryKey: INSIGHTS_KEYS.all(),
    queryFn: async () => {
      try {
        return await atrPerformanceFormsApi.atrAdminV1PerformanceFormsInsightsGet();
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1PerformanceFormsInsightsGet',
          error
        );

        throw error;
      }
    }
  });
};
