import { FC, useCallback } from 'react';

import { Button, ButtonVariant, Link } from '@ot/onetalent-ui-kit';

import { Drawer } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

import { ExportAllForms } from '../components/ExportAllForms';
import { FiltersSection } from '../components/FiltersSection';
import { InsightsWidgets } from '../components/InsightsWidgets';
import { PerformanceFormsTable } from '../components/PerformanceFormsTable';
import { PerformanceFormsContextProvider } from '../domain/performanceForms/contexts';

import {
  AdminFeatureFlag,
  FeatureFlag
} from '../../shared/modules/featureFlags';

export const PerformanceFormsPage: FC = () => {
  const { toggleDrawer } = useToggleVisibility();

  const handleShowExcelBulkActions = useCallback(() => {
    toggleDrawer({
      name: Drawer.ExcelBulkActionsDrawer
    });
  }, [toggleDrawer]);

  const handleShowActionLog = useCallback(() => {
    toggleDrawer({
      name: Drawer.ActionLogDrawer
    });
  }, [toggleDrawer]);

  return (
    <div
      data-attributes="PerformanceFormsPage"
      className="flex flex-auto flex-col py-16"
    >
      <FeatureFlag name={AdminFeatureFlag.AdminAtrInsights}>
        <InsightsWidgets />
      </FeatureFlag>

      <PerformanceFormsContextProvider>
        <div className="mb-24 flex flex-auto items-center justify-end gap-8">
          <Link onClick={handleShowActionLog}>Action Log</Link>
          <Button
            variant={ButtonVariant.Tertiary}
            onClick={handleShowExcelBulkActions}
          >
            Excel Bulk Actions
          </Button>

          <div className="mx-8 flex h-40 w-[1px] border-r border-solid border-divider-dark"></div>

          <ExportAllForms />
        </div>

        <div className="mb-12 flex min-h-40 w-full items-center justify-start rounded-2xl bg-surface-grey_0 p-16">
          <FiltersSection />
        </div>

        <PerformanceFormsTable />
      </PerformanceFormsContextProvider>
    </div>
  );
};
