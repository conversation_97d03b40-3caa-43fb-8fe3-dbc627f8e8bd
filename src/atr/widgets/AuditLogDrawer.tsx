import { useCallback } from 'react';

import { Button, Drawer } from '@ot/onetalent-ui-kit';

import { AuditLog } from '@/atr/components';
import { Drawer as DrawerType } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

export const AuditLogDrawer = () => {
  const { toggleDrawer } = useToggleVisibility();

  const onClose = useCallback(() => {
    toggleDrawer({
      name: DrawerType.AuditLogDrawer,
      performanceFormId: undefined,
      show: false
    });
  }, [toggleDrawer]);

  return (
    <Drawer
      dataAttributes="AuditLogDrawer"
      header="Audit Log"
      variant="Primary"
      showExpand={false}
      isExpanded={true}
      onClose={onClose}
      footer={
        <Button variant="Secondary" onClick={onClose}>
          Close
        </Button>
      }
      isOpen
      showClose
      fullHeight
    >
      <AuditLog />
    </Drawer>
  );
};
