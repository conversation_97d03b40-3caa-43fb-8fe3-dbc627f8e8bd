import { useCallback, useMemo } from 'react';
import { useLocation } from 'react-router-dom';

import { <PERSON><PERSON>, Drawer } from '@ot/onetalent-ui-kit';

import { ActionLog } from '@/atr/components';
import { Drawer as DrawerType } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

export const ActionLogDrawer = () => {
  const { toggleDrawer } = useToggleVisibility();

  const { search } = useLocation();
  const searchParams = useMemo(() => new URLSearchParams(search), [search]);

  const referred = useMemo(
    () => searchParams.get('referred') as DrawerType | null,
    [searchParams]
  );

  const closeActionLogDrawer = useCallback(() => {
    toggleDrawer({
      name: DrawerType.ActionLogDrawer,
      show: false,
      referred
    });
  }, [referred, toggleDrawer]);

  const showReferredDrawer = useCallback(() => {
    const options: Record<string, string> = {};
    searchParams.forEach((val, key) => {
      if (key !== 'referred' && key !== 'drawer') options[key] = val;
    });

    referred &&
      toggleDrawer({
        ...options,
        name: referred,
        show: true
      });
  }, [referred, searchParams, toggleDrawer]);

  const onBack = useCallback(() => {
    closeActionLogDrawer();
    showReferredDrawer();
  }, [closeActionLogDrawer, showReferredDrawer]);

  return (
    <Drawer
      dataAttributes="ActionLogDrawer"
      header="Action Log"
      variant="Primary"
      showExpand={false}
      isExpanded={true}
      onClose={closeActionLogDrawer}
      onBack={onBack}
      footer={
        <Button variant="Secondary" onClick={closeActionLogDrawer}>
          Close
        </Button>
      }
      isOpen
      showBack={!!referred}
      showClose
      fullHeight
    >
      <ActionLog />
    </Drawer>
  );
};
