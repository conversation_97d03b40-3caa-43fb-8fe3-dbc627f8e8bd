import { FC, useCallback } from 'react';

import {
  Button,
  ButtonSize,
  ButtonVariant,
  Drawer
} from '@ot/onetalent-ui-kit';

import { BulkActionsDrawerContent } from '@/atr/components/BulkActions';
import { Drawer as DrawerType } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';

type ExcelBulkActionsDrawerFooterProps = {
  onClick?: () => void;
};

const ExcelBulkActionsDrawerFooter: FC<ExcelBulkActionsDrawerFooterProps> = ({
  onClick
}) => (
  <Button
    variant={ButtonVariant.Secondary}
    onClick={onClick}
    size={ButtonSize.Medium}
  >
    Close
  </Button>
);

export const ExcelBulkActionsDrawer: FC = () => {
  const { toggleDrawer } = useToggleVisibility();

  const hideExcelBulkActionsDrawer = useCallback(() => {
    toggleDrawer({
      name: DrawerType.ExcelBulkActionsDrawer,
      actionType: null,
      show: false
    });
  }, [toggleDrawer]);

  return (
    <Drawer
      dataAttributes="Drawer"
      fullHeight
      header="Excel Bulk Actions"
      isOpen
      showClose
      variant="Primary"
      showExpand={false}
      isExpanded={true}
      onClose={hideExcelBulkActionsDrawer}
      footer={
        <ExcelBulkActionsDrawerFooter onClick={hideExcelBulkActionsDrawer} />
      }
    >
      <BulkActionsDrawerContent />
    </Drawer>
  );
};
