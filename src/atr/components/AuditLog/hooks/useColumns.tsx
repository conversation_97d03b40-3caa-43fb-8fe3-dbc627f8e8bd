import { useMemo } from 'react';

import { DataColumnProps } from '@epam/uui-core';

import { PerformanceFormHistoryModel } from '@/atr/domain';
import { CellContent, UserInfo, UserInfoSize } from '@/shared/components';
import { formatDateToDateTime } from '@/shared/utils';

enum ColumnName {
  ActionDateTime = 'Action DateTime',
  Action = 'Action',
  ActionOwnerId = 'Action Owner ID',
  ActionOwnerName = 'Action Owner Name',
  MajorStatusFrom = 'Major Status From',
  MinorStatusFrom = 'Minor Status From',
  MajorStatusTo = 'Major Status To',
  MinorStatusTo = 'Minor Status To',
  ActionReason = 'Action Reason',
  ActionOwnerRole = 'Action Owner Role',
  GroupCompany = 'Group Company',
  Directorate = 'Directorate',
  Function = 'Function',
  Division = 'Division',
  AtrCycleName = 'ATR Cycle Name',
  AssessmentManagerLm = 'Assessment Manager (LM)',
  AssessmentManagerB2b = 'Assessment Manager (B2B)',
  DottedLineManager = 'Dotted Line Manager',
  CurrentLineManager = 'Current Line Manager',
  ManagerRating = 'Manager Rating',
  ManagerRatingName = 'Manager Rating Name',
  Manager9BoxPotential = 'Manager 9-Box Potential',
  NormalizedRating = 'Normalized Rating',
  NormalizedRatingName = 'Normalized Rating Name',
  Normalized9BoxPotential = 'Normalized 9-Box Potential',
  WorkflowId = 'Workflow ID',
  WfAssigneeName = 'WF Assignee Name'
}

const titleClassName = 'text-body-2-regular';

export const useColumns = (): DataColumnProps<PerformanceFormHistoryModel>[] =>
  useMemo(
    () => [
      {
        key: ColumnName.ActionDateTime,
        caption: ColumnName.ActionDateTime,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent>
            {formatDateToDateTime(history.actionDateTime)}
          </CellContent>
        )
      },
      {
        key: ColumnName.Action,
        caption: ColumnName.Action,
        width: 228,
        minWidth: 228,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.actionType }}
          >
            {history.actionType}
          </CellContent>
        )
      },
      {
        key: ColumnName.ActionOwnerId,
        caption: ColumnName.ActionOwnerId,
        width: 140,
        minWidth: 140,
        render: (history) => (
          <CellContent tooltipProps={{ title: history.actionOwnerId }}>
            {history.actionOwnerId}
          </CellContent>
        )
      },
      {
        key: ColumnName.ActionOwnerName,
        caption: ColumnName.ActionOwnerName,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.actionOwner && (
              <UserInfo
                {...history.actionOwner}
                size={UserInfoSize.ExtraSmall}
                fullName={history.actionOwner?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      },
      {
        key: ColumnName.MajorStatusFrom,
        caption: ColumnName.MajorStatusFrom,
        width: 158,
        minWidth: 158,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.majorStatusFrom }}
          >
            {history.majorStatusFrom}
          </CellContent>
        )
      },
      {
        key: ColumnName.MinorStatusFrom,
        caption: ColumnName.MinorStatusFrom,
        width: 170,
        minWidth: 170,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.minorStatusFrom }}
          >
            {history.minorStatusFrom}
          </CellContent>
        )
      },
      {
        key: ColumnName.MajorStatusTo,
        caption: ColumnName.MajorStatusTo,
        width: 135,
        minWidth: 135,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.majorStatusTo }}
          >
            {history.majorStatusTo}
          </CellContent>
        )
      },
      {
        key: ColumnName.MinorStatusTo,
        caption: ColumnName.MinorStatusTo,
        width: 181,
        minWidth: 181,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.minorStatusTo }}
          >
            {history.minorStatusTo}
          </CellContent>
        )
      },
      {
        key: ColumnName.ActionReason,
        caption: ColumnName.ActionReason,
        width: 233,
        minWidth: 233,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.actionReason }}
          >
            {history.actionReason}
          </CellContent>
        )
      },
      {
        key: ColumnName.ActionOwnerRole,
        caption: ColumnName.ActionOwnerRole,
        width: 243,
        minWidth: 243,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.actionOwnerJobRole }}
          >
            {history.actionOwnerJobRole}
          </CellContent>
        )
      },
      {
        key: ColumnName.GroupCompany,
        caption: ColumnName.GroupCompany,
        width: 152,
        minWidth: 152,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.groupCompany }}
          >
            {history.groupCompany}
          </CellContent>
        )
      },
      {
        key: ColumnName.Directorate,
        caption: ColumnName.Directorate,
        width: 152,
        minWidth: 152,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.directorate }}
          >
            {history.directorate}
          </CellContent>
        )
      },
      {
        key: ColumnName.Function,
        caption: ColumnName.Function,
        width: 152,
        minWidth: 152,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history._function }}
          >
            {history._function}
          </CellContent>
        )
      },
      {
        key: ColumnName.Division,
        caption: ColumnName.Division,
        width: 152,
        minWidth: 152,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.division }}
          >
            {history.division}
          </CellContent>
        )
      },
      {
        key: ColumnName.AtrCycleName,
        caption: ColumnName.AtrCycleName,
        width: 200,
        minWidth: 200,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.atrCycleName }}
          >
            {history.atrCycleName}
          </CellContent>
        )
      },
      {
        key: ColumnName.AssessmentManagerLm,
        caption: ColumnName.AssessmentManagerLm,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.assessmentLineManager && (
              <UserInfo
                {...history.assessmentLineManager}
                size={UserInfoSize.ExtraSmall}
                fullName={history.assessmentLineManager?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      },
      {
        key: ColumnName.AssessmentManagerB2b,
        caption: ColumnName.AssessmentManagerB2b,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.assessmentB2BManager && (
              <UserInfo
                {...history.assessmentB2BManager}
                size={UserInfoSize.ExtraSmall}
                fullName={history.assessmentB2BManager?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      },
      {
        key: ColumnName.DottedLineManager,
        caption: ColumnName.DottedLineManager,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.dottedLineManager && (
              <UserInfo
                {...history.dottedLineManager}
                size={UserInfoSize.ExtraSmall}
                fullName={history.dottedLineManager?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      },
      {
        key: ColumnName.CurrentLineManager,
        caption: ColumnName.CurrentLineManager,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.currentLineManager && (
              <UserInfo
                {...history.currentLineManager}
                size={UserInfoSize.ExtraSmall}
                fullName={history.currentLineManager?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      },
      {
        key: ColumnName.ManagerRating,
        caption: ColumnName.ManagerRating,
        width: 142,
        minWidth: 142,
        render: (history) => (
          <CellContent tooltipProps={{ title: history.managerRating }}>
            {history.managerRating}
          </CellContent>
        )
      },
      {
        key: ColumnName.ManagerRatingName,
        caption: ColumnName.ManagerRatingName,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.managerRatingName }}
          >
            {history.managerRatingName}
          </CellContent>
        )
      },
      {
        key: ColumnName.Manager9BoxPotential,
        caption: ColumnName.Manager9BoxPotential,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.managerNineBoxPotential }}
          >
            {history.managerNineBoxPotential}
          </CellContent>
        )
      },
      {
        key: ColumnName.NormalizedRating,
        caption: ColumnName.NormalizedRating,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent tooltipProps={{ title: history.normalizedRating }}>
            {history.normalizedRating}
          </CellContent>
        )
      },
      {
        key: ColumnName.NormalizedRatingName,
        caption: ColumnName.NormalizedRatingName,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.normalizedRatingName }}
          >
            {history.normalizedRatingName}
          </CellContent>
        )
      },
      {
        key: ColumnName.Normalized9BoxPotential,
        caption: ColumnName.Normalized9BoxPotential,
        width: 208,
        minWidth: 208,
        render: (history) => (
          <CellContent
            classNames="line-clamp-2"
            tooltipProps={{ title: history.normalizedNineBoxPotential }}
          >
            {history.normalizedNineBoxPotential}
          </CellContent>
        )
      },
      {
        key: ColumnName.WorkflowId,
        caption: ColumnName.WorkflowId,
        width: 192,
        minWidth: 192,
        render: (history) => (
          <CellContent tooltipProps={{ title: history.workflowId }}>
            {history.workflowId}
          </CellContent>
        )
      },
      {
        key: ColumnName.WfAssigneeName,
        caption: ColumnName.WfAssigneeName,
        width: 212,
        minWidth: 212,
        render: (history) => (
          <CellContent>
            {history.wfAssignee && (
              <UserInfo
                {...history.wfAssignee}
                size={UserInfoSize.ExtraSmall}
                fullName={history.wfAssignee?.fullNameEnglish}
                titleClassName={titleClassName}
                hasTooltip
              />
            )}
          </CellContent>
        )
      }
    ],
    []
  );
