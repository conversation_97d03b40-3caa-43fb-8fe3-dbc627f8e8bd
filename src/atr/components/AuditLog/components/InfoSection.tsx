import { FC, PropsWithChildren } from 'react';

import { Text } from '@ot/onetalent-ui-kit';
import clsx from 'clsx';

import { PerformanceFormModel } from '@/atr/domain';
import { UserInfo, UserInfoSize } from '@/shared/components';

interface FieldProps {
  label: string;
}

const Field: FC<PropsWithChildren<FieldProps>> = ({ label, children }) => (
  <div className="grid gap-[2px]">
    <Text className="text-body-2-regular text-text-body">{label}</Text>
    <Text
      className="text-body-1-medium"
      {...(typeof children === 'string' && {
        tooltip: { title: children },
        tooltipMode: 'always',
        lineClamp: 1
      })}
    >
      {children}
    </Text>
  </div>
);

interface InfoSectionProps {
  data?: PerformanceFormModel;
}

export const InfoSection: FC<InfoSectionProps> = ({ data }) => {
  if (!data) return null;

  const { id, templateName, employee } = data;

  return (
    <section
      data-attributes="InfoSection"
      className={clsx(
        'grid grid-cols-[1fr_3fr_1fr_2fr] gap-[72px]',
        'border border-solid border-divider-dark',
        'rounded-xl',
        'p-16'
      )}
    >
      <Field label="ATR Form ID">{id}</Field>
      <Field label="Template Name">{templateName}</Field>
      <Field label="Employee ID">{employee?.employeeId}</Field>
      <Field label="Employee Full Name">
        <UserInfo
          {...employee}
          size={UserInfoSize.ExtraSmall}
          fullName={employee?.fullNameEnglish}
          hasTooltip
        />
      </Field>
    </section>
  );
};
