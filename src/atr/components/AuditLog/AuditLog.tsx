import { Loader } from '@ot/onetalent-ui-kit';

import { useGetParameter } from '@oh/hooks';

import {
  usePerformanceFormById,
  usePerformanceFormHistory
} from '@/atr/domain';
import {
  IllustrationMessageError,
  PerformanceDataTable,
  useDataSourceState
} from '@/shared/components';
import { DEFAULT_PAGE_SIZE, DEFAULT_VISIBLE_COUNT } from '@/shared/constants';

import { InfoSection, NoRecordsFound } from './components';
import { useColumns } from './hooks';

const loaderText = 'Audit Log is being loaded';

export const AuditLog = () => {
  const performanceFormId = useGetParameter('performanceFormId') ?? '';
  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: DEFAULT_PAGE_SIZE,
    visibleCount: DEFAULT_VISIBLE_COUNT
  });

  const queryResult = usePerformanceFormHistory(
    { performanceFormId, getPerformanceFormHistoryRequest: pagination },
    { enabled: !!performanceFormId }
  );

  const { data, isLoading, isError } = usePerformanceFormById(
    performanceFormId,
    { enabled: !!performanceFormId }
  );

  const columns = useColumns();

  if (isLoading) {
    return (
      <Loader
        size="Medium"
        descriptionClassName="text-center"
        description={
          <>
            Please wait,
            <br />
            {loaderText}
          </>
        }
      />
    );
  }

  if (isError) {
    return <IllustrationMessageError />;
  }

  return (
    <div data-attributes="AuditLog" className="flex flex-col gap-8">
      <InfoSection data={data} />
      <PerformanceDataTable
        {...dataSource}
        dataAttributes="AuditLogTable"
        queryResult={queryResult}
        columns={columns}
        renderNoResults={NoRecordsFound}
        loaderText={loaderText}
        paginationSize="24"
      />
    </div>
  );
};
