import { useState } from 'react';

import { useToast } from '@ot/onetalent-ui-kit';
import { useMutation } from '@tanstack/react-query';

import { useAtrAdminServiceApi } from '@/api';
import { Drawer } from '@/shared/constants';
import { useToggleVisibility } from '@/shared/hooks';
import { logger } from '@/shared/logger';

import { useCheckResponseStatus } from './useCheckResponseStatus';

import { isActionStatusPending } from '../../../utils';

export function useExportAllForms() {
  const [currentActionId, setCurrentActionId] = useState<
    string | null | undefined
  >(undefined);
  const { atrActionsCommandsApi, atrActionsQueriesApi } =
    useAtrAdminServiceApi();
  const { addToast } = useToast();
  const { toggleDrawer } = useToggleVisibility();
  const checkResponseStatus = useCheckResponseStatus();

  return useMutation({
    mutationFn: async () => {
      try {
        if (currentActionId) {
          const currentExportAction = await atrActionsQueriesApi
            .atrAdminV1AdminsMeActionsActionIdGet({
              actionId: currentActionId
            })
            .catch(() => undefined);
          if (
            currentExportAction &&
            isActionStatusPending(currentExportAction.status)
          ) {
            addToast({
              title: 'Export pending. Action is in the queue',
              description:
                'Download will start automatically once the file is ready. Alternatively, open the Action Log for a manual download.',
              showActionButton: true,
              actionTitle: 'Open',
              duration: 10000,
              onActionClick: async () => {
                toggleDrawer({
                  name: Drawer.ActionLogDrawer
                });
              }
            });
            return;
          }
        }
        return await atrActionsCommandsApi
          .atrAdminV1AdminsMeActionsPost({
            actionType: 'Export',
            scheduleActionRequest: {}
          })
          .then((exportAction) => {
            addToast({
              title: 'Export initiated',
              description:
                'Download will start automatically once the file is ready. Alternatively, open the Action Log for a manual download. ',
              showActionButton: true,
              actionTitle: 'Open',
              duration: 10000,
              onActionClick: async () => {
                toggleDrawer({
                  name: Drawer.ActionLogDrawer
                });
              }
            });

            checkResponseStatus(exportAction);
            setCurrentActionId(exportAction.id);
          });
      } catch (error) {
        addToast({
          title: 'An error occurred, the export failed',
          description:
            'Please try again later or contact support if the issue persists',
          variant: 'Error'
        });
        logger.error('Failed to load atrAdminV1AdminsMeActionsPost', error);
        throw error;
      }
    }
  });
}
