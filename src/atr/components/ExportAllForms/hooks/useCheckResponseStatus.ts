import { useToast } from '@ot/onetalent-ui-kit';

import { ActionDto } from '@/api/generated/models/ActionDto';
import { useAtrAdminServiceApi } from '@/api/useAdminServiceApi';
import { useDownloadMyOutputFile } from '@/atr/domain';
import { logger } from '@/shared/logger';

import {
  isActionStatusCompleted,
  isActionStatusInProgress,
  isActionStatusPending
} from '../../../domain/files/utils';
export const useCheckResponseStatus = () => {
  const { atrActionsQueriesApi } = useAtrAdminServiceApi();
  const { addToast } = useToast();
  const downloadMyOutputFile = useDownloadMyOutputFile();
  return (exportAction: ActionDto) => {
    const handleCheckResponseStatus = async () => {
      try {
        const currentExportAction =
          await atrActionsQueriesApi.atrAdminV1AdminsMeActionsActionIdGet({
            actionId: exportAction.id as string
          });

        if (isActionStatusCompleted(currentExportAction.status)) {
          clearInterval(intervalId);
          addToast({
            title: 'Export completed successfully',
            variant: 'Success'
          });
          downloadMyOutputFile(exportAction.id as string);
          return;
        }

        if (
          isActionStatusInProgress(currentExportAction.status) ||
          isActionStatusPending(currentExportAction.status)
        ) {
          return;
        }
        clearInterval(intervalId);
      } catch (error) {
        logger.error(
          'Failed to load atrAdminV1AdminsMeActionsActionIdGet',
          error
        );
        throw error;
      }
    };

    const intervalId = setInterval(() => {
      handleCheckResponseStatus();
    }, 10000);
  };
};
