import { Link } from '@ot/onetalent-ui-kit';

import { useExportAllForms } from './hooks/useExportAllForms';

import { usePerformanceFormsContext } from '../../domain';

export const ExportAllForms = () => {
  const { mutate, isPending } = useExportAllForms();
  const { queryResult } = usePerformanceFormsContext();
  const isDisabled = isPending || !queryResult?.data?.paging?.totalResults;
  return (
    <Link
      onClick={() => mutate()}
      leftIcon="Download"
      disabled={isDisabled}
      tooltip={
        !queryResult?.data?.paging?.totalResults
          ? {
              title: 'Please create ATR Forms first'
            }
          : undefined
      }
    >
      Export All Forms
    </Link>
  );
};
