import { FC } from 'react';

import { Loader, LoaderMark } from '@ot/onetalent-ui-kit';

export type InsightsWidgetsCardContentProps = {
  value?: number;
  percentage?: string | 0;
  isPending?: boolean;
  isError?: boolean;
};

export const InsightsWidgetsCardContent: FC<
  InsightsWidgetsCardContentProps
> = ({ value, percentage, isPending, isError }) => {
  if (isPending) {
    return (
      <div
        data-attributes="InsightsWidgetsCardContent"
        className="flex h-full w-full"
      >
        <Loader size="Small" className="items-start" />
      </div>
    );
  }
  if (isError) {
    return <LoaderMark variant="Error" size="Small" />;
  }
  if (!value) {
    return (
      <span
        data-attributes="InsightsWidgetsCardContent"
        className="text-body-xl-regular text-text-heading"
      >
        N/A
      </span>
    );
  }
  return (
    <div data-attributes="InsightsWidgetsCardContent">
      <span className="text-header-3-medium text-text-heading">{value}</span>
      {percentage && (
        <>
          {' '}
          <span className="text-body-xl-regular text-text-heading">
            ({percentage}%)
          </span>
        </>
      )}
    </div>
  );
};
