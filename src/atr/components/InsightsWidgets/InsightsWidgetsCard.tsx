import { FC } from 'react';

import {
  InsightsWidgetsCardContent,
  InsightsWidgetsCardContentProps
} from './InsightsWidgetsCardContent';

type InsightsWidgetsCardProps = InsightsWidgetsCardContentProps & {
  title: string;
};
export const InsightsWidgetsCard: FC<InsightsWidgetsCardProps> = ({
  title,
  ...props
}) => (
  <div className="flex flex-col justify-between gap-[30px] rounded-[12px] bg-surface-grey_10 p-16">
    <div className="text-body-1-regular text-text-body">{title}</div>
    <InsightsWidgetsCardContent {...props} />
  </div>
);
