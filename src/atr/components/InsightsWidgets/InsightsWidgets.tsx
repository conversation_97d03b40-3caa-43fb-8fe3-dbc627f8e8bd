import { InsightsWidgetsCard } from './InsightsWidgetsCard';

import { useGetInsights } from '../../domain';
import { insightsWidgetsNumber } from '../../domain/insights/models/insightsWidgets';

export const InsightsWidgets = () => {
  const { data: insights, isPending, isError } = useGetInsights();
  const {
    completedSelfAssesmentsPercentage,
    completedManagerAssesmentsPercentage,
    completedUnratedPercentage,
    unratedCount
  } = insightsWidgetsNumber(insights);
  return (
    <div
      data-attributes="InsightsWidgets"
      className="mb-24 grid grid-cols-4 gap-16 rounded-2xl bg-surface-grey_0 p-16"
    >
      <InsightsWidgetsCard
        title="Total Number of ATR Forms"
        value={insights && insights.totalAssessments}
        isError={isError}
        isPending={isPending}
      />
      <InsightsWidgetsCard
        title="Completed Self-Assessments"
        value={insights && insights.completedSelfAssessments}
        percentage={completedSelfAssesmentsPercentage}
        isError={isError}
        isPending={isPending}
      />
      <InsightsWidgetsCard
        title="Completed Manager Assessments"
        value={insights && insights.completedManagerAssessments}
        percentage={completedManagerAssesmentsPercentage}
        isError={isError}
        isPending={isPending}
      />
      <InsightsWidgetsCard
        title="Unrated"
        value={unratedCount && unratedCount}
        percentage={completedUnratedPercentage}
        isError={isError}
        isPending={isPending}
      />
    </div>
  );
};
