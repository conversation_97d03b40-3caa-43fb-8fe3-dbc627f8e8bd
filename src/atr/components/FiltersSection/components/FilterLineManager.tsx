import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useLineManagerOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterLineManager = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useLineManagerOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="lineManagerIds"
      label="Assessment Manager (LM)"
      options={options}
      visibilityMode="hiddenByDefault"
      basis="242"
    />
  );
};
