import { useMemo } from 'react';
import { useSearchParams } from 'react-router-dom';

import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useAtrFormIdOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterAtrFormId = () => {
  const [searchParams] = useSearchParams();
  const [search, setSearch] = useDebouncedSearch();

  const defaultSelectedIds = useMemo(() => {
    const searchQuery = searchParams.get('formIds');
    if (searchQuery && typeof searchQuery === 'string') {
      return searchQuery.split(',');
    }
    return [];
  }, [searchParams]);

  const formIds = useMemo(() => {
    const searchIdsArray: string[] = [];
    if (defaultSelectedIds) {
      searchIdsArray.push(...defaultSelectedIds);
    }
    if (search) {
      searchIdsArray.push(search);
    }

    return searchIdsArray;
  }, [defaultSelectedIds, search]);

  const { data: options = [], isLoading } = useAtrFormIdOptions({
    formIds
  });

  return (
    <MultiselectFilter
      isLoading={isLoading}
      searchPlaceholder="Search by exact ID only"
      id="formIds"
      label="ATR Form ID"
      onSearch={setSearch}
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
