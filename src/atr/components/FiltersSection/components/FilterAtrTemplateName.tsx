import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useAtrTemplateNameOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterAtrTemplateName = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useAtrTemplateNameOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="templateIds"
      label="ATR Template Name"
      options={options}
      visibilityMode="always"
      basis="331"
    />
  );
};
