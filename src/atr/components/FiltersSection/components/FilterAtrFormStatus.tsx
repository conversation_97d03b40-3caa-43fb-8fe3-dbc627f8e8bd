import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useAtrFormStatusOptions } from 'atr/domain';

export const FilterAtrFormStatus = () => {
  const { data: options = [] } = useAtrFormStatusOptions();
  return (
    <MultiselectFilter
      id="performanceFormStatusIds"
      label="ATR Form Status"
      options={options}
      visibilityMode="always"
      basis="331"
    />
  );
};
