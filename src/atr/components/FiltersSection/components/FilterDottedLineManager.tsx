import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useDottedLineManagerOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterDottedLineManager = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useDottedLineManagerOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="dottetLineManagerIds"
      label="Dotted Line Manager"
      options={options}
      visibilityMode="always"
      basis="320"
    />
  );
};
