import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useCompanyOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterCompany = () => {
  const [search, setSearch] = useDebouncedSearch();
  const { data: options = [], isLoading } = useCompanyOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="companyCodes"
      label="Company Name"
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
