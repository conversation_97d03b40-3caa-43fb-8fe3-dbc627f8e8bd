import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useB2BManagerOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterB2BManager = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useB2BManagerOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="b2BManagerIds"
      label="Assessment Manager (B2B)"
      options={options}
      visibilityMode="always"
      basis="320"
    />
  );
};
