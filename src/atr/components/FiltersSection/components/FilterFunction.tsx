import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useFunctionOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterFunction = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useFunctionOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="functionIds"
      label="Function"
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
