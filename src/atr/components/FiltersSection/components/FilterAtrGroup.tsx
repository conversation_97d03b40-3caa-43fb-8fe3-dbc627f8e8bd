import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useAtrGroupOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterAtrGroup = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useAtrGroupOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="atrGroupIds"
      label="ATR Group"
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
