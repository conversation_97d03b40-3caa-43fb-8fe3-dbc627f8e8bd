import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useDivisionOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterDivision = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useDivisionOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="divisionIds"
      label="Division"
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
