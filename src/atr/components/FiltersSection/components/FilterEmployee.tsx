import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useEmployeeOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterEmployee = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useEmployeeOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="employeeIds"
      label="Employee"
      options={options}
      visibilityMode="always"
      basis="331"
    />
  );
};
