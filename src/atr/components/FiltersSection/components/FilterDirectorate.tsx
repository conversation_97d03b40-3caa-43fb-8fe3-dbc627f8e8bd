import { MultiselectFilter } from '@ot/onetalent-ui-kit';

import { useDirectorateOptions } from 'atr/domain';

import { useDebouncedSearch } from '../hooks';

export const FilterDirectorate = () => {
  const [search, setSearch] = useDebouncedSearch();

  const { data: options = [], isLoading } = useDirectorateOptions(search);

  return (
    <MultiselectFilter
      isLoading={isLoading}
      onSearch={setSearch}
      id="directorateIds"
      label="Directorate"
      options={options}
      visibilityMode="always"
      basis="242"
    />
  );
};
