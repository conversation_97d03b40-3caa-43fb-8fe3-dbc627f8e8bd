import { FC } from 'react';

import { Filters } from '@ot/onetalent-ui-kit';

import { FilterKeys, usePerformanceFormsContext } from 'atr/domain';

import { AdminFeatureFlag, FeatureFlag } from '@/shared/modules/featureFlags';

import {
  FilterAtrFormId,
  FilterAtrFormStatus,
  FilterAtrGroup,
  FilterAtrTemplateName,
  FilterB2BManager,
  FilterCompany,
  FilterDirectorate,
  FilterDivision,
  FilterDottedLineManager,
  FilterEmployee,
  FilterFunction,
  FilterLineManager
} from './components';

export const FiltersSection: FC = () => {
  const { onSetFilter, selectedFilters, onClearFilters, queryResult } =
    usePerformanceFormsContext();

  return (
    <Filters<FilterKeys>
      disabled={queryResult.isLoading}
      className="w-full"
      onClearFilters={onClearFilters}
      selectedFilters={selectedFilters}
      onFilterChanged={onSetFilter}
    >
      <FeatureFlag name={AdminFeatureFlag.AdminAtrFiltersByOrgUnit}>
        <FilterCompany />
        <FilterDirectorate />
        <FilterFunction />
        <FilterDivision />
      </FeatureFlag>

      <FeatureFlag name={AdminFeatureFlag.AdminAtrFiltersByAtrGroup}>
        <FilterAtrGroup />
      </FeatureFlag>

      <FilterAtrFormId />

      <FeatureFlag name={AdminFeatureFlag.AdminAtrFiltersByNameAndStatus}>
        <FilterAtrTemplateName />
        <FilterAtrFormStatus />
      </FeatureFlag>

      <FeatureFlag name={AdminFeatureFlag.AdminAtrFiltersByUsers}>
        <FilterEmployee />
        <FilterLineManager />
        <FilterB2BManager />
        <FilterDottedLineManager />
      </FeatureFlag>
    </Filters>
  );
};
