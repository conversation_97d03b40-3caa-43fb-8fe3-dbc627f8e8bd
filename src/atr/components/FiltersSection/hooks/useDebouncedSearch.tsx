import { useCallback } from 'react';

import { useDebounced } from 'atr/hooks';

export function useDebouncedSearch(): [string, (searchQuery: string) => void] {
  const { debouncedValue, setValue, setDebouncedValue } = useDebounced({
    initialValue: ''
  });
  const handleSearch = useCallback(
    (searchQuery: string) => {
      if (searchQuery) {
        setDebouncedValue(searchQuery);
      } else {
        setValue(searchQuery);
      }
    },
    [setValue, setDebouncedValue]
  );

  return [debouncedValue, handleSearch];
}
