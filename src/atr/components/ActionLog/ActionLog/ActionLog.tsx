import { BulkActionsCancelProcessModal } from '@/atr/components/BulkActions/BulkActionsCancelProcessModal';
import { useActions } from '@/atr/domain';
import { PerformanceDataTable, useDataSourceState } from '@/shared/components';
import { DEFAULT_PAGE_SIZE, DEFAULT_VISIBLE_COUNT } from '@/shared/constants';

import { Filters, NoRecordsFound } from './components';
import { useCancelModal, useColumns } from './hooks';

export const ActionLog = () => {
  const { dataSource, parameters: pagination } = useDataSourceState<string>({
    pageSize: DEFAULT_PAGE_SIZE,
    visibleCount: DEFAULT_VISIBLE_COUNT
  });

  const queryResult = useActions(pagination);
  const { isFetching, refetch } = queryResult;

  const {
    isCancelModalVisible,
    setCancelAction,
    cancelProcess,
    changeModalVisibility
  } = useCancelModal();

  const columns = useColumns(setCancelAction);

  return (
    <div data-attributes="ActionLog" className="flex flex-col gap-24">
      <Filters onReload={refetch} isFetching={isFetching} />
      <PerformanceDataTable
        dataAttributes="ActionLogTable"
        {...dataSource}
        queryResult={queryResult}
        columns={columns}
        renderNoResults={NoRecordsFound}
        loaderText="actions are being loaded"
        paginationSize="24"
      />
      <BulkActionsCancelProcessModal
        confirmCb={cancelProcess}
        isOpen={isCancelModalVisible}
        changeModalVisibility={changeModalVisibility}
        text="The export process will be stopped, and no file will be generated. If you proceed, any progress made in exporting the forms will be lost."
      />
    </div>
  );
};
