import { useCallback, useState } from 'react';

import {
  ActionModel,
  ActionType,
  useMyFileCancelProcessing
} from '@/atr/domain';

export function useCancelModal() {
  const [isCancelModalVisible, setIsCancelModalVisible] = useState(false);
  const [cancelProps, setCancelProps] = useState<{
    actionId: ActionModel['id'];
    type: ActionType;
  }>({ actionId: null, type: ActionType.Launch });

  const setCancelAction = useCallback(
    (actionId: ActionModel['id'], type: ActionType) => {
      setCancelProps({ actionId, type });
      setIsCancelModalVisible(true);
    },
    []
  );

  const changeModalVisibility = () => {
    setIsCancelModalVisible((state) => !state);
  };
  const { mutate: cancelProcessing } = useMyFileCancelProcessing({
    onSuccess: changeModalVisibility
  });
  const cancelProcess = () => {
    cancelProps.actionId &&
      cancelProcessing({
        actionId: cancelProps.actionId,
        type: cancelProps.type
      });
  };

  return {
    isCancelModalVisible,
    setCancelAction,
    cancelProcess,
    changeModalVisibility
  };
}
