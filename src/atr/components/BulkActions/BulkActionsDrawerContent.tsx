import { Tabs } from '@ot/onetalent-ui-kit';

import { ActionLogLink } from '@/atr/components/ActionLog';
import { ActionType } from '@/atr/domain';
import { useActionTypeQueryParam } from '@/atr/hooks/actionType';
import { MyFilesActionsProvider } from '@/atr/providers';

import { BulkActionsForm } from './BulkActionsForm';

const bulkActionsTabs = [
  { id: ActionType.Launch, label: 'ATR Forms Launch' },
  { id: ActionType.Transition, label: 'Bulk Transition' },
  { id: ActionType.ReAssign, label: 'Bulk Reassign' }
];

export const BulkActionsDrawerContent = () => {
  const { actionType, setActionType } = useActionTypeQueryParam();

  return (
    <div
      data-attributes="ExcelBulkActionsDrawerContent"
      className="flex grow flex-col"
    >
      <div className="flex justify-end">
        <ActionLogLink className="text-cta-2-medium" />
      </div>
      <Tabs
        items={bulkActionsTabs}
        activeTabId={actionType}
        onChange={setActionType}
      />
      <MyFilesActionsProvider actionType={actionType}>
        <BulkActionsForm />
      </MyFilesActionsProvider>
    </div>
  );
};
