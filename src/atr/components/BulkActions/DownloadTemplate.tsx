import React from 'react';

import { Link } from '@ot/onetalent-ui-kit';

import { useDownloadTemplateFile } from '@/atr/domain';
import { useActionTypeQueryParam } from '@/atr/hooks/actionType';

export const DownloadTemplate = () => {
  const { actionType } = useActionTypeQueryParam();
  const downloadTemplate = useDownloadTemplateFile(actionType);

  const handleDownloadClick = () => {
    downloadTemplate();
  };

  return (
    <Link
      data-attributes="DownloadTemplate"
      onClick={handleDownloadClick}
      leftIcon="Download"
    >
      Download Template File
    </Link>
  );
};
