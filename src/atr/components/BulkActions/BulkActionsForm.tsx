import React from 'react';

import { BulkActionsFileUploader } from '@/atr/components/BulkActions/BulkActionsFileUploader';
import { BulkActionsFormLoader } from '@/atr/components/BulkActions/BulkActionsFormLoader';
import { BulkActionsFormProcessor } from '@/atr/components/BulkActions/BulkActionsFormProcessor';
import { DownloadTemplate } from '@/atr/components/BulkActions/DownloadTemplate';
import { useMyFilesActions } from '@/atr/providers';

export const BulkActionsForm = () => {
  const { isPendingMyFile, isPendingUploadMyFile, isPendingDeleteMyFile } =
    useMyFilesActions();

  if (isPendingMyFile || isPendingUploadMyFile || isPendingDeleteMyFile) {
    return <BulkActionsFormLoader />;
  }

  return (
    <>
      <div className="flex justify-end py-24">
        <DownloadTemplate />
      </div>
      <BulkActionsFileUploader />
      <BulkActionsFormProcessor />
    </>
  );
};
