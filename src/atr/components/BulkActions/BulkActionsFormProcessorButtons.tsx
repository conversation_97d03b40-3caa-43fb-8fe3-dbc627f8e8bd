import React, { FC } from 'react';

import { Button, ButtonSize, ButtonVariant } from '@ot/onetalent-ui-kit';
import { UseMutateFunction } from '@tanstack/react-query';

import { ActionModel, ScheduleActionRequest } from '@/atr/domain';
import {
  isActionStatusCanceledOrFailed,
  isActionStatusCompleted,
  isActionStatusDraft
} from '@/atr/domain/files/utils';
import { useMyFilesActions } from '@/atr/providers';

interface BulkActionsFormProcessorButtonsProps {
  processMyFile: UseMutateFunction<ActionModel, unknown, ScheduleActionRequest>;
}

export const BulkActionsFormProcessorButtons: FC<
  BulkActionsFormProcessorButtonsProps
> = ({ processMyFile }) => {
  const { myFile, setMyLocalFile, deleteMyFile } = useMyFilesActions();

  const isValidationEnabled =
    !myFile?.id && !!myFile?.inputFileId && isActionStatusDraft(myFile?.status);

  const isReValidationEnabled =
    myFile?.id &&
    !!myFile?.inputFileId &&
    myFile.isValidation &&
    isActionStatusCanceledOrFailed(myFile?.status);

  const isValidationDisabled = !isValidationEnabled && !isReValidationEnabled;

  const isImportEnabled =
    myFile?.id &&
    myFile?.isValidation &&
    isActionStatusCompleted(myFile?.status);

  const isReImportEnabled =
    myFile?.id &&
    !myFile?.isValidation &&
    isActionStatusCanceledOrFailed(myFile?.status);

  const isImportDisabled = !isImportEnabled && !isReImportEnabled;

  const isHideValidationAndImportButtons =
    myFile?.id &&
    isActionStatusCompleted(myFile?.status) &&
    !myFile.isValidation;

  const onValidateHandler = () => {
    if (myFile)
      processMyFile({
        ...myFile,
        id: isValidationEnabled ? myFile?.id : undefined
      });
  };
  const onImportHandler = () => {
    if (myFile) processMyFile(myFile);
  };

  const onResetClickHandler = () => {
    setMyLocalFile(null);
    deleteMyFile();
  };
  return (
    <div
      data-attributes="BulkActionsFormControls"
      className="mt-24 flex flex-wrap justify-end gap-24 border-t border-solid border-divider-dark py-24"
    >
      {isHideValidationAndImportButtons ? (
        <Button
          size={ButtonSize.Medium}
          variant={ButtonVariant.Primary}
          tooltip={{
            title:
              'The uploaded file has already been validated and imported. Please click the button to upload a new file'
          }}
          onClick={onResetClickHandler}
        >
          Upload New File
        </Button>
      ) : (
        <>
          <Button
            variant={ButtonVariant.Secondary}
            size={ButtonSize.Medium}
            tooltip={{
              title: !myFile?.inputFileId
                ? 'Please upload the file first'
                : !isImportDisabled && 'The file has already been validated'
            }}
            disabled={isValidationDisabled}
            onClick={onValidateHandler}
          >
            Validate
          </Button>
          <Button
            size={ButtonSize.Medium}
            variant={ButtonVariant.Primary}
            tooltip={{
              title: !myFile?.inputFileId
                ? 'Please upload the file first'
                : isImportDisabled && 'Please validate the file first'
            }}
            disabled={isImportDisabled}
            onClick={onImportHandler}
          >
            Import
          </Button>
        </>
      )}
    </div>
  );
};
