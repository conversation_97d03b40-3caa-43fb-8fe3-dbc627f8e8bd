import { PerformanceFormModel } from '@/atr/domain';

/**
 * ATR Form Status Constants
 * Based on PerformanceFormStatuses.cs and PerformanceFormStep.cs
 */
export const ATR_FORM_MAJOR_STATUSES = {
  DRAFT: 'Draft',
  SELF_ASSESSMENT: 'Self Assessment',
  MANAGER_ASSESSMENT: 'Manager Assessment',
  DOTTED_LINE_MANAGER_ENDORSEMENT: 'Dotted Line Manager Endorsement',
  SECOND_LINE_MANAGER_ENDORSEMENT: 'Second Line Manager Endorsement',
  NORMALIZATION: 'Normalization',
  RATING_APPROVAL: 'Rating Approval',
  RATING_ANNOUNCEMENT: 'Rating Announcement',
  COMPLETED: 'Completed'
} as const;

/**
 * Statuses that allow deletion
 * Forms can be deleted up to and including Normalization step
 */
const DELETABLE_STATUSES = [
  ATR_FORM_MAJOR_STATUSES.DRAFT,
  ATR_FORM_MAJOR_STATUSES.SELF_ASSESSMENT,
  ATR_FORM_MAJOR_STATUSES.MANAGER_ASSESSMENT,
  ATR_FORM_MAJOR_STATUSES.DOTTED_LINE_MANAGER_ENDORSEMENT,
  ATR_FORM_MAJOR_STATUSES.SECOND_LINE_MANAGER_ENDORSEMENT,
  ATR_FORM_MAJOR_STATUSES.NORMALIZATION
] as const;

/**
 * Determines if an ATR form can be deleted based on its status
 * @param form - The performance form to check
 * @returns true if the form can be deleted, false otherwise
 */
export const canDeleteAtrForm = (form: PerformanceFormModel): boolean => {
  if (!form?.majorStatus) {
    return false;
  }

  return DELETABLE_STATUSES.includes(form.majorStatus as any);
};

/**
 * Gets the tooltip message for disabled delete action
 * @returns The tooltip message explaining why deletion is disabled
 */
export const getDeleteDisabledTooltip = (): string => {
  return 'You cannot delete a form that has passed the Normalization step';
};

/**
 * Checks if a form is in a specific major status
 * @param form - The performance form to check
 * @param status - The status to check against
 * @returns true if the form is in the specified status
 */
export const isFormInStatus = (
  form: PerformanceFormModel,
  status: string
): boolean => {
  return form?.majorStatus === status;
};
