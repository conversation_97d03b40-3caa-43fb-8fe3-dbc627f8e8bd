import { FC, useState, useCallback } from 'react';

import {
  Button,
  ButtonSize,
  ButtonVariant,
  Informer,
  InformerVariant,
  Modal,
  Textarea,
  Tooltip
} from '@ot/onetalent-ui-kit';

import { PerformanceFormModel } from '@/atr/domain';

export interface DeleteFormModalProps {
  isOpen: boolean;
  form: PerformanceFormModel | null;
  isLoading?: boolean;
  onClose: () => void;
  onConfirm: (reason: string) => void;
}

const MAX_REASON_LENGTH = 250;

export const DeleteFormModal: FC<DeleteFormModalProps> = ({
  isOpen,
  form,
  isLoading = false,
  onClose,
  onConfirm
}) => {
  const [reason, setReason] = useState('');

  const handleClose = useCallback(() => {
    setReason('');
    onClose();
  }, [onClose]);

  const handleConfirm = useCallback(() => {
    if (reason.trim()) {
      onConfirm(reason.trim());
    }
  }, [reason, onConfirm]);

  const isReasonEmpty = !reason.trim();
  const characterCount = reason.length;
  const isOverLimit = characterCount > MAX_REASON_LENGTH;

  const confirmButton = (
    <Button
      variant={ButtonVariant.Primary}
      size={ButtonSize.Medium}
      disabled={isReasonEmpty || isOverLimit || isLoading}
      onClick={handleConfirm}
    >
      Confirm
    </Button>
  );

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      showClose={true}
      header="Delete ATR Form"
      footer={
        <div className="flex gap-16 justify-end">
          <Button
            variant={ButtonVariant.Secondary}
            size={ButtonSize.Medium}
            onClick={handleClose}
            disabled={isLoading}
          >
            Discard
          </Button>
          {isReasonEmpty ? (
            <Tooltip content="Please specify the reason">
              {confirmButton}
            </Tooltip>
          ) : (
            confirmButton
          )}
        </div>
      }
    >
      <div className="flex flex-col gap-24">
        {/* Error Informer */}
        <Informer
          variant={InformerVariant.Error}
          title="Are you sure you want to delete the form?"
          description="You will not be able to restore the form after the deletion, but a new ATR form can be created if needed."
        />

        {/* Reason Textarea */}
        <Textarea
          label="Specify Reason"
          placeholder="Type here…"
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          required
          maxLength={MAX_REASON_LENGTH}
          rows={3}
          disabled={isLoading}
          supportingText={
            isOverLimit
              ? `Character limit exceeded (${characterCount}/${MAX_REASON_LENGTH})`
              : undefined
          }
          variant={isOverLimit ? 'Error' : undefined}
        />
      </div>
    </Modal>
  );
};
