import { FC, useState, useCallback } from 'react';

import { usePerformanceFormsContext, useDeletePerformanceForm, PerformanceFormModel } from 'atr/domain';

import { PerformanceDataTable } from '@/shared/components';
import { DEFAULT_PAGE_SIZES } from '@/shared/constants/pagination';

import { DeleteFormModal } from './components';
import { useColumnGroups, useColumns } from './hooks';
import { PerformanceFormsEmpty } from './PerformanceFormsEmpty';
import { PerformanceFormsError } from './PerformanceFormsError';

export interface PerformanceFormsTableProps {
  emptyScreenSubtitle?: string;
  className?: string;
}

export const PerformanceFormsTable: FC<PerformanceFormsTableProps> = ({
  className
}) => {
  const { queryResult, dataSource, onSetPageSize } =
    usePerformanceFormsContext();

  // Delete modal state
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedForm, setSelectedForm] = useState<PerformanceFormModel | null>(null);

  // Delete mutation
  const { mutate: deleteForm, isPending: isDeleting } = useDeletePerformanceForm({
    onSuccess: () => {
      setIsDeleteModalOpen(false);
      setSelectedForm(null);
    },
    onError: () => {
      // Modal stays open on error so user can retry
    }
  });

  // Handlers
  const handleDeleteForm = useCallback((form: PerformanceFormModel) => {
    setSelectedForm(form);
    setIsDeleteModalOpen(true);
  }, []);

  const handleCloseDeleteModal = useCallback(() => {
    setIsDeleteModalOpen(false);
    setSelectedForm(null);
  }, []);

  const handleConfirmDelete = useCallback((reason: string) => {
    if (selectedForm) {
      deleteForm({
        performanceFormId: selectedForm.id,
        reason
      });
    }
  }, [selectedForm, deleteForm]);

  const columns = useColumns({ onDeleteForm: handleDeleteForm });
  const columnGroups = useColumnGroups();

  return (
    <>
      <div data-attributes="PerformanceFormsTable" className={className}>
        <PerformanceDataTable
          dataAttributes="PerformanceFormsTable"
          {...dataSource}
          showContainer
          styles={{
            minHeight: !queryResult.data?.items.length ? 440 : 0
          }}
          queryResult={queryResult}
          columns={columns}
          columnGroups={columnGroups}
          pageSizes={DEFAULT_PAGE_SIZES}
          setPageSize={onSetPageSize}
          renderNoResults={PerformanceFormsEmpty}
          renderError={PerformanceFormsError}
          loaderText="ATR Forms are being loaded"
          paginationSize="24"
        />
      </div>

      {/* Delete Form Modal */}
      <DeleteFormModal
        isOpen={isDeleteModalOpen}
        form={selectedForm}
        isLoading={isDeleting}
        onClose={handleCloseDeleteModal}
        onConfirm={handleConfirmDelete}
      />
    </>
  );
};
