import { FC, ReactElement } from 'react';
import { BrowserRouter, Route, Routes } from 'react-router-dom';

import { createRoot } from 'react-dom/client';

import '@oh/components/dist/styles.css';

import { App } from './App';
import { unregister } from './service-worker';
import { AppModeContext } from './shared/contexts';
import { FederationAppProps } from './withApplication';

const props = {
  options: {},
  messageToHostFunction: () => null
} as unknown as FederationAppProps;

const Bootstrap: FC = (): ReactElement => (
  <AppModeContext.Provider value={{ isStandaloneMode: true }}>
    <BrowserRouter basename="/one-talent">
      <Routes>
        <Route path="/*" element={<App {...props} />} />
      </Routes>
    </BrowserRouter>
    <BrowserRouter basename="/admin">
      <Routes>
        <Route path="/*" element={<App {...props} />} />
      </Routes>
    </BrowserRouter>
  </AppModeContext.Provider>
);

const container: HTMLDivElement | null = document.querySelector('div');

if (!container) {
  throw new Error("The element wasn't found");
}

createRoot(container).render(<Bootstrap />);

unregister();
