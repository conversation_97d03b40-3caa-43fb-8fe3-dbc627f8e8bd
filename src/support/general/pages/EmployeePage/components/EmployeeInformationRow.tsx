import { FC, ReactNode } from 'react';

interface Props {
  fieldName: string;
  fieldValue: string | ReactNode;
}

export const EmployeeInformationRow: FC<Props> = ({
  fieldName,
  fieldValue
}) => (
  <div
    data-attributes="EmployeeInformationRow"
    className="my-12 flex justify-between"
  >
    <span className="typography--primary mb-4 text-[14px] font-medium leading-[18px] text-gray-400">
      {fieldName}
    </span>
    <span className="typography--primary mb-4 text-[14px] font-medium leading-[18px]">
      {fieldValue || ''}
    </span>
  </div>
);
