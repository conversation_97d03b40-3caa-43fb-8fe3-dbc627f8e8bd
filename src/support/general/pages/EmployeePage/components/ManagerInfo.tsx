import { FC } from 'react';

import clsx from 'clsx';

import { openEmployeeAtNewTab } from 'support/shared/utils/urls/urls';

import { ManagerDetails } from '@/api/generated';
import { UserInfoItem } from '@/support/shared/components/UserInfoItem';

import { EmployeeInformationRow } from './EmployeeInformationRow';

interface Props {
  managerData: ManagerDetails | undefined;
  fieldName: string;
}

export const ManagerInfo: FC<Props> = ({ managerData, fieldName }) => {
  const hasEmployeeIdOrEmail =
    !!managerData?.employeeId || !!managerData?.email;

  return (
    <div
      data-attributes="EmployeeDetailsInformation"
      onClick={() =>
        hasEmployeeIdOrEmail &&
        openEmployeeAtNewTab(
          managerData.employeeId || '',
          managerData.email || ''
        )
      }
      className={clsx({
        'cursor-pointer': hasEmployeeIdOrEmail
      })}
    >
      <EmployeeInformationRow
        fieldName={fieldName}
        fieldValue={
          hasEmployeeIdOrEmail ? (
            <UserInfoItem
              avatarSize={30}
              noPadding
              isOneLineDescription
              name={managerData.fullName}
              email={managerData.email || ''}
              id={managerData.employeeId}
              showEmail
              isRFI
              showActiveStatus={false}
            />
          ) : (
            <>-</>
          )
        }
      />
    </div>
  );
};
