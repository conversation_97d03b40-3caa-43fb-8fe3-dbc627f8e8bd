import { AdminService } from '@/api';

import { FilterPanel } from './components/FilterPanel';

import { TableFiltersType } from '../../../hooks/utils';

export type EmployeesDataFilterType = AdminService.SearchEmployeeRequest;

export type FilterProps = {
  initialFilters: TableFiltersType;
  visibleFilters: (keyof EmployeesDataFilterType)[] | undefined;
  onApply: (appliedFilters: TableFiltersType) => void;
};

export const EmployeesDataFilter = (props: FilterProps) => (
  <FilterPanel {...props} />
);
