import { FC } from 'react';

import { Button, ButtonSize, ButtonVariant } from '@ot/onetalent-ui-kit';

import { ActionBar } from '@/support/shared/components/ActionBar';

interface Props {
  onClearFilters: () => void;
  onApplyFilters: () => void;
}

export const FilterButtons: FC<Props> = ({
  onClearFilters,
  onApplyFilters
}) => (
  <ActionBar className="flex items-center justify-between gap-16 rounded-[12px] !py-0 shadow-oh_light">
    <Button
      variant={ButtonVariant.Link}
      size={ButtonSize.Medium}
      onClick={onClearFilters}
      className="text-blue-core hover:text-blue-core"
    >
      Clear all
    </Button>
    <Button
      variant={ButtonVariant.Primary}
      size={ButtonSize.Medium}
      onClick={onApplyFilters}
    >
      Apply
    </Button>
  </ActionBar>
);
