import { FC } from 'react';

import { Text } from '@/support/shared/components/Text';

const sharedTextStyles =
  'block cursor-pointer px-12 py-[13px] text-body-2-regular hover:bg-dropdown-dropdown-hover';

interface Props {
  onOpenEmployeeDetailsModal: () => void;
  onClose: (() => void) | undefined;
}

export const ActionsDropdownContent: FC<Props> = ({
  onOpenEmployeeDetailsModal,
  onClose
}) => (
  <>
    <Text
      onClick={() => {
        onOpenEmployeeDetailsModal();
        onClose?.();
      }}
      className={sharedTextStyles}
    >
      View Employee Details
    </Text>
  </>
);
