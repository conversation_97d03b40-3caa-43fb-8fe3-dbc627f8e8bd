import { FC } from 'react';

import { Loader } from '@ot/onetalent-ui-kit';

import { EmployeeFullInfoModel } from '@/api/generated';
import { EmptyStateScreen } from '@/support/shared/components/EmptyStateScreen';

import { EmployeeDetailContent } from './EmployeeDetailContent';

interface Props {
  isFetching: boolean;
  data: EmployeeFullInfoModel | undefined;
  isExpandedAll: boolean;
}

export const EmployeeDetailModalContent: FC<Props> = ({
  isFetching,
  data,
  isExpandedAll
}) => {
  if (isFetching) {
    return <Loader />;
  }

  if (!data) {
    return (
      <div
        data-attributes="EmployeeDetailModalContent"
        className="item-center flex justify-center"
      >
        <EmptyStateScreen
          title="Something went wrong!"
          subtitle="Error loading employee's details data. Please try again later."
        />
      </div>
    );
  }

  return (
    <EmployeeDetailContent employeeData={data} isExpandedAll={isExpandedAll} />
  );
};
