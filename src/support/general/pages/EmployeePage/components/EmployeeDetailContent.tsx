import { Stack } from 'support/shared/components/Stack';
import { UserInfoItem } from 'support/shared/components/UserInfoItem';

import { EmployeeFullInfoModel } from '@/api/generated';

import { EmployeeDetailsInformation } from './EmployeeDetailsInformation';

interface EmployeeDetailContentProps {
  employeeData: EmployeeFullInfoModel;
  isExpandedAll: boolean;
}

export const EmployeeDetailContent = ({
  employeeData,
  isExpandedAll
}: EmployeeDetailContentProps) => {
  const secondaryProfiles = employeeData.otherEmployeeProfiles;

  return (
    <>
      <Stack gap={12}>
        <div className="mb-10 rounded-xl bg-white">
          <UserInfoItem
            isOneLineDescription
            name={employeeData.primaryEmployeeInfo.fullName}
            email={employeeData.primaryEmployeeInfo.email}
            showEmail
            id={employeeData.primaryEmployeeInfo.employeeId}
            showActiveStatus
            isActive={!employeeData.primaryEmployeeInfo.isDeleted}
            isRFI
          />
        </div>
        <EmployeeDetailsInformation
          employeeData={employeeData.primaryEmployeeInfo}
          isExpandedAll={isExpandedAll}
          isPrimary
        />
        {secondaryProfiles?.map((secondaryProfile) => (
          <EmployeeDetailsInformation
            key={secondaryProfile.email}
            employeeData={secondaryProfile}
            isExpandedAll={isExpandedAll}
            isPrimary={false}
          />
        ))}
      </Stack>
    </>
  );
};
