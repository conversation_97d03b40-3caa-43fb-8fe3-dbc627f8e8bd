import { useQuery } from '@tanstack/react-query';
import { v4 as uuidv4 } from 'uuid';

import { AdminService } from '@/api';
import { SearchEmployee, SearchEmployeePagedListResult } from '@/api/generated';
import { useSupportAdminServiceApi } from '@/api/useAdminServiceApi';
import { PaginationData } from '@/shared/services/queryService/generated';
import { logger } from '@/support/shared/utils/logger';

export interface SearchEmployeeUniqueId extends SearchEmployee {
  employeeId: string;
}

type DataWithUniqueItems = {
  items: SearchEmployeeUniqueId[];
  paging: PaginationData;
};

const addUUIDField = (data: SearchEmployeePagedListResult) => {
  const itemsWithUUID = data.items.map((item) => ({
    ...item,
    id: uuidv4(),
    employeeId: item.id
  }));

  const output = {
    ...data,
    items: itemsWithUUID
  };
  return output;
};

export function useSearchEmployees(params: AdminService.SearchEmployeeRequest) {
  const { employeeViewsApi } = useSupportAdminServiceApi();

  return useQuery({
    queryKey: ['search', 'employeeView', params],
    queryFn: async () => {
      try {
        const data = await employeeViewsApi.supportAdminV1EmployeesSearchPost({
          searchEmployeeRequest: params
        });

        // api may return items with the same id and email but different name, so need to add id which will be unique
        const dataWithUniqueItems: DataWithUniqueItems = addUUIDField(data);
        return dataWithUniqueItems;
      } catch (error) {
        logger.error('Failed to load v1EmployeesSearchPost', error);

        throw error;
      }
    },
    enabled: params.search ? params.search.length >= 3 : false
  });
}
