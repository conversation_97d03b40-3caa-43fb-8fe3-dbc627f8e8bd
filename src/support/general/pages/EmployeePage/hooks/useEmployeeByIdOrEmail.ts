import {
  useQuery,
  useQueryClient,
  UseQueryResult
} from '@tanstack/react-query';

import { EmployeeFullInfoModel } from '@/api/generated';
import { useSupportAdminServiceApi } from '@/api/useAdminServiceApi';
import { logger } from '@/support/shared/utils/logger';

import { EMPLOYEE_REQUEST_KEYS } from './EmployeeRequestKeys';

export function useEmployeeByIdOrEmail(
  employeeEmail?: string,
  employeeId?: string,
  subHash?: string
) {
  const { employeeViewsApi } = useSupportAdminServiceApi();
  const queryClient = useQueryClient();

  const queryKey = employeeId
    ? EMPLOYEE_REQUEST_KEYS.getEmployeeById(employeeId, subHash)
    : employeeEmail
    ? EMPLOYEE_REQUEST_KEYS.getEmployeeByEmail(employeeEmail, subHash)
    : null;

  const cachedData = queryKey
    ? queryClient.getQueryData<EmployeeFullInfoModel>(queryKey)
    : undefined;

  const queryResults = useQuery<EmployeeFullInfoModel, Error>({
    queryKey: queryKey!,
    queryFn: async () => {
      try {
        if (employeeId) {
          return await employeeViewsApi.supportAdminV1EmployeesEmployeeIdFullDetailsGet(
            {
              employeeId
            }
          );
        }
        if (employeeEmail) {
          return await employeeViewsApi.supportAdminV1EmployeesDetailsPost({
            getEmployeeDetailsRequest: { email: employeeEmail }
          });
        }

        throw new Error('Either employeeId or employeeEmail must be provided');
      } catch (error) {
        logger.error('Failed to load employee details', error);
        throw error;
      }
    },
    enabled: !!queryKey && !cachedData
  });

  const data = cachedData || queryResults.data;

  return {
    ...queryResults,
    data
  } as UseQueryResult<EmployeeFullInfoModel, Error>;
}
