import { useCallback } from 'react';

import { useQuery } from '@tanstack/react-query';

import { useSupportAdminServiceApi } from '@/api/useAdminServiceApi';
import { logger } from '@/support/shared/utils/logger';

function useGetGroupCompaniesFiltersRaw(pageSize: number, pageNumber: number) {
  const { employeeViewsApi } = useSupportAdminServiceApi();

  return useCallback(async () => {
    try {
      const response = await employeeViewsApi.supportAdminV1CompaniesAllPost({
        getCompaniesRequest: {
          pageNumber,
          pageSize
        }
      });

      return response;
    } catch (error) {
      logger.error('Failed to load v1CompaniesAllPost', error);

      throw error;
    }
  }, [employeeViewsApi, pageSize, pageNumber]);
}

export function useGetGroupCompaniesFilters(
  pageSize: number,
  pageNumber: number
) {
  const getGroupCompaniesFiltersRaw = useGetGroupCompaniesFiltersRaw(
    pageSize,
    pageNumber
  );

  return useQuery({
    queryKey: ['employeeView', 'groupCompaniesFilters', pageSize, pageNumber],
    queryFn: getGroupCompaniesFiltersRaw
  });
}
