import { TextAreaConfig } from '../../general/types';

export enum OneTalentTab {
  SupportAdmin = 'support',
  ODObjects = 'od-objects'
}

export const NOT_FOUND_ROUTE = '/not-found';
export const NO_ACCESS_ROUTE = '/no-access';

export const DEFAULT_TAB = OneTalentTab.SupportAdmin;
export const ONE_TALENT_ROUTES = ['support'];

export const TEXT_SHORT_LENGTH = 4000;
export const TEXT_LONG_LENGTH = 65000;
export const INPUT_MAX_LENGTH = 200;

export const TEXT_AREA: TextAreaConfig = {
  shortTextLength: TEXT_SHORT_LENGTH,
  longTextLength: TEXT_LONG_LENGTH
};

export const TEXT_PLACEHOLDER = 'Type here…';
export const SUBMIT_BUTTON_TOOLTIP_TEXT =
  'Please select all required item(s) to enable the button';
export const NO_PERMISSIONS_BUTTON_TOOLTIP_TEXT =
  'You don’t have permissions to perform this action';
export const MAP_SKILLS_BUTTON_TOOLTIP_TEXT =
  'Please select at least one item to enable the button';
export const NOT_VALID_MESSAGE =
  'Please make sure that all fields are filled out correctly';
export const TOO_LONG_TEXT = 'Too long text';

export const TOOLTIP_AREA = 'tooltip-area';

export const S4_SOURCE = 'S4';

export enum Permission {
  CanCreateSkills = 'canCreateSkills',
  CanImportSkills = 'canImportSkills',
  CanReviewSkillSuggestions = 'canReviewSkillSuggestions',
  IsSuperAdmin = 'isSuperAdmin',
  IsSkillAdmin = 'isSkillAdmin',
  IsGCSkillAdmin = 'isGCSkillAdmin',
  HasUserProfile = 'hasUserProfile',
  HasAnyGroupAvailable = 'hasAnyGroupAvailable',
  JDCanViewJobTitle = 'jdCanViewJobTitle',
  JDCanViewPositionTitle = 'jdCanViewPositionTitle',
  JDCanViewStandardTitle = 'jdCanViewStandardTitle',
  JDCanViewStrictlyConfidential = 'jdCanViewStrictlyConfidential'
}

export const PROXY_USER_HEADER_NAME = 'X-Delegator-UserId';
