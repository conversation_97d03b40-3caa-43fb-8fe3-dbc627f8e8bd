import { format } from 'date-fns';

import { MOBILE_APP_VERSION_HEADER } from '@oh/constants';
import { LoggerPropertiesType } from '@oh/contracts';
import {
  AdnocError,
  AreaOptions,
  ITrackException
} from '@oh/services/logger/types';
import { checkIsLocalDevelopment, getMobileAppVersion } from '@oh/utils';

import { Area, LoggingLevel, LoggingSeverityLevel } from './constants';

import { apiUrl } from '../urls/urls';

import { getAppInsightsInstance } from '../../appInsights/appInsights';

export const buildAreaOptions = (
  message: string,
  ...checkers: string[]
): AreaOptions => ({
  message,
  checker: (endpointUrl: string) =>
    Math.max(
      ...checkers.map((checkerStr) => {
        if (endpointUrl.includes(checkerStr)) {
          return checkerStr.length;
        }

        return -1;
      })
    )
});

export const AreaChecksMap: Record<Area, AreaOptions> = {
  [Area.UserManagement]: buildAreaOptions(
    'User Management',
    apiUrl('/v1/users')
  ),
  [Area.SkillGroups]: buildAreaOptions(
    'Skill Groups',
    apiUrl('/v1/user-groups')
  ),
  [Area.AccessMapping]: buildAreaOptions(
    'Access Mapping',
    apiUrl('v1/user-group-permissions')
  ),
  [Area.SkillsManagement]: buildAreaOptions(
    'Skills Management',
    apiUrl('v1/skills/search')
  ),
  [Area.SkillsMapping]: buildAreaOptions(
    'Skills Mapping',
    apiUrl('v1/organizational-development/skill-mappings')
  ),
  [Area.JobDescription]: buildAreaOptions(
    'Job Description',
    apiUrl('v1/job-description/')
  ),
  [Area.ODObjects]: buildAreaOptions(
    'OD Objects',
    apiUrl('v1/organizational-development')
  ),
  [Area.AddSkillGroup]: buildAreaOptions(
    'Add Skill Group',
    apiUrl('v1/skill-groups')
  ),
  [Area.EditSkillGroup]: buildAreaOptions(
    'Edit Skill Group',
    apiUrl('v1/skill-groups')
  ),
  [Area.Unknown]: {
    message: 'Unknown Area',
    checker: () => 0
  }
};

export const getFullProperties = <T>(
  properties?: T,
  error?: AdnocError | string
): LoggerPropertiesType => {
  const modifiedProperties =
    (properties as LoggerPropertiesType | undefined) || {};

  if (typeof error === 'object') {
    if (error?.request?.responseURL) {
      const requestEndpoint = error.request.responseURL;
      modifiedProperties.requestEndpoint = requestEndpoint;
      modifiedProperties.affectedArea = getAffectedAreaMessage(requestEndpoint);
    }

    if (error?.status) {
      modifiedProperties.statusCode = error.status;
    }

    if (error?.serverMessage) {
      modifiedProperties.serverMessage = error.serverMessage;
    }
  }

  return {
    ...modifiedProperties,
    [MOBILE_APP_VERSION_HEADER]: getMobileAppVersion(),
    commit: process.env.COMMIT_HASH
  };
};

export const isLoggingOff = (severityLevel: number) => {
  const isLocalDevelopment = checkIsLocalDevelopment();

  const logLevel = LoggingLevel.All;

  const loggingLevel = Number(LoggingLevel[logLevel]);

  return loggingLevel > severityLevel || isLocalDevelopment;
};

export const getAffectedAreaMessage = (endpoint: string): string => {
  let maxWeight = -1;
  let currentMessage = '';
  Object.keys(AreaChecksMap).forEach((area: string) => {
    const typedArea = area as unknown as Area;

    if (AreaChecksMap[typedArea].checker(endpoint) > maxWeight) {
      maxWeight = AreaChecksMap[typedArea].checker(endpoint);
      currentMessage = AreaChecksMap[typedArea].message;
    }
  });

  return currentMessage;
};

export const trackException: ITrackException = (telemetry) => {
  const { properties, exception } = telemetry;

  return getAppInsightsInstance()?.trackException({
    ...telemetry,
    exception: typeof exception === 'string' ? new Error(exception) : exception,
    properties
  });
};

export const getLogMessageSystemInfo = (severity: LoggingSeverityLevel) =>
  `[${severity}] ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')} - `;
