import { FC } from 'react';

import { IllustrationMessage } from '@ot/onetalent-ui-kit';

import { Loader } from '@oh/components';

import { useInfiniteScrollSelect } from 'support/shared/hooks/useInfiniteScrollSelect';

import { UserInfoItem } from '../UserInfoItem';

import { SearchEmployeeUniqueId } from '../../../general/pages/EmployeePage/hooks';

interface Props {
  isFetching: boolean;
  employees: SearchEmployeeUniqueId[] | null | undefined;
  getOnEmployeeClick: (
    employee: SearchEmployeeUniqueId
  ) => (event: React.MouseEvent<HTMLDivElement>) => void;
  showEmail: boolean;
  fetchNextEmployeesBatch: () => void;
}

export const SearchDropdownMainContent: FC<Props> = ({
  isFetching,
  employees,
  getOnEmployeeClick,
  showEmail,
  fetchNextEmployeesBatch
}) => {
  const { refCallback } = useInfiniteScrollSelect({
    fetchNextPage: fetchNextEmployeesBatch
  });

  const isEmployeesEmpty = !employees || employees?.length === 0;

  if (isFetching && isEmployeesEmpty) {
    return (
      <div
        data-attributes="SearchDropdownMainContent"
        className="item-center flex h-[300px] justify-center"
      >
        <Loader />
      </div>
    );
  }

  if (isEmployeesEmpty) {
    return (
      <div
        data-attributes="SearchDropdownMainContent"
        className="item-center flex h-[300px] justify-center"
      >
        <IllustrationMessage
          className=""
          description={
            <p>
              You are all caught up!
              <br />
              No new items here!
            </p>
          }
          descriptionClassName=""
          illustrationVariant="NothingFound"
          subtitle=""
          subtitleClassName=""
          title="Sorry, no results found"
          titleClassName=""
        />
      </div>
    );
  }

  return employees?.map((employee, index) => {
    const isLastElement = employees.length === index + 1;

    return (
      <div
        data-attributes="SearchDropdown"
        onClick={getOnEmployeeClick(employee)}
        className="cursor-pointer border-b border-solid border-gray-200 p-8 last:border-b-0 hover:bg-[#F0F8FF]"
        key={index}
      >
        <UserInfoItem
          noPadding
          isOneLineDescription
          name={employee.fullName}
          email={employee.email || ''}
          showEmail={showEmail}
          id={employee.employeeId}
          positionTitle={employee.positionTitle}
          isRFI
          ref={isLastElement ? refCallback : null}
          showActiveStatus={false}
        />
      </div>
    );
  });
};
