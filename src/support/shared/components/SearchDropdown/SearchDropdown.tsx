import React, { memo, useState } from 'react';

import { ToastVariant, useToast } from '@ot/onetalent-ui-kit';

import { SearchDropdownMainContent } from './SearchDropdownMainContent';
import { TotalResults } from './TotalResults';

import { Search } from '../search/Search';
import { Dropdown } from '../SelectDropdown';

import { openEmployeeAtNewTab } from '../../utils/urls/urls';

import { SearchEmployeeUniqueId } from '../../../general/pages/EmployeePage/hooks';

export interface EmployeeBasicInfo {
  id?: string;
  fullName?: string | null;
  email?: string | null;
  jobTitle?: string | null;
  companyName?: string | null;
}

export interface SearchDropdownProps {
  onClick: (name: string, id: string, email: string) => void;
  value: string;
  handleClearSearch: () => void;
  handleSearchChange: (value: string) => void;
  onEnter?: () => void;
  isFetching: boolean;
  employees: SearchEmployeeUniqueId[] | null | undefined;
  showEmail: boolean;
  placeholderText?: string;
  totalResults?: number;
  onClickViewAllResults?: () => void;
  fetchNextEmployeesBatch: () => void;
}

export const SearchDropdown = memo(
  ({
    value,
    onClick,
    handleClearSearch,
    handleSearchChange,
    onEnter,
    isFetching,
    employees,
    showEmail,
    totalResults,
    onClickViewAllResults,
    placeholderText = 'Type here...',
    fetchNextEmployeesBatch
  }: SearchDropdownProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const { addToast } = useToast();

    const showDropdown = value.length > 2 && isOpen;

    const handleEmployeeClick =
      (employee: SearchEmployeeUniqueId) =>
      (event: React.MouseEvent<HTMLDivElement>) => {
        if (!!employee.email || !!employee.employeeId) {
          if (event.metaKey || event.ctrlKey) {
            openEmployeeAtNewTab(
              employee.employeeId || '',
              employee.email || ''
            );
          } else {
            onClick(
              employee.fullName || '',
              employee.employeeId || '',
              employee.email || ''
            );
            setIsOpen(false);
          }
        } else {
          addToast({
            variant: ToastVariant.Error,
            title:
              "Current employee doesn't have an email, you can't get detailed information."
          });
        }
      };

    return (
      <Dropdown<HTMLDivElement>
        isRenderTriggerInput
        containerClassName="text-left"
        renderTrigger={({ ref, onClick }) => (
          <div ref={ref} className="autocomplete-input relative">
            <Search
              placeholder={placeholderText}
              value={value}
              onClear={handleClearSearch}
              onChange={(e) => {
                handleSearchChange(e.target.value);
                setIsOpen(true);
              }}
              className="mb-12 ml-0 !h-48 w-full !bg-white"
              isSearchDropdown
              onClick={() => {
                setIsOpen(true);
                onClick();
              }}
              onEnter={() => {
                if (value.length > 2) {
                  onEnter?.();
                  setIsOpen(false);
                }
              }}
            />
          </div>
        )}
        renderContent={() =>
          showDropdown ? (
            <div
              data-attributes="SearchDropdown"
              className="mt-[-15px] flex max-h-[400px] flex-col rounded-xl bg-surface-grey_0 shadow-dropdown"
            >
              <div className="flex-1 overflow-auto px-12 py-8">
                <SearchDropdownMainContent
                  fetchNextEmployeesBatch={fetchNextEmployeesBatch}
                  isFetching={isFetching}
                  employees={employees}
                  getOnEmployeeClick={handleEmployeeClick}
                  showEmail={showEmail}
                />
              </div>

              <TotalResults
                totalResults={totalResults}
                onViewAllClick={() => {
                  onClickViewAllResults?.();
                  setIsOpen(false);
                }}
                hasEmployees={!!employees && employees.length > 0}
              />
            </div>
          ) : (
            <div />
          )
        }
      />
    );
  }
);

SearchDropdown.displayName = 'SearchDropdown';
