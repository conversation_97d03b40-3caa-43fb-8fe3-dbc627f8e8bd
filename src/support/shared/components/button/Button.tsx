import type { ButtonHTMLAttributes, FC, MouseEvent, ReactNode } from 'react';
import { memo } from 'react';

import clsx from 'clsx';

import {
  Button,
  ButtonSize,
  ButtonVariant as OHButtonVariant
} from '@oh/components';
import { TooltipVariant } from '@oh/constants';

import { TooltipWrapperButton } from '../Tooltip';

export enum OneTalentButtonVariant {
  Primary = 'primary',
  Secondary = 'secondary',
  Tertiary = 'tertiary',
  Positive = 'positive',
  Negative = 'negative',
  White = 'white',
  Link = 'link',
  PrimaryPurple = 'primaryPurple',
  SecondaryPurple = 'secondaryPurple',
  SecondaryPositive = 'secondaryPositive',
  SecondaryDefault = 'secondaryDefault'
}

const buttonVariantAdapter = (
  variant?: OneTalentButtonVariant
): OHButtonVariant | undefined => {
  switch (variant) {
    case OneTalentButtonVariant.Primary:
      return OHButtonVariant.Primary;
    case OneTalentButtonVariant.Positive:
      return OHButtonVariant.Positive;
    case OneTalentButtonVariant.Negative:
      return OHButtonVariant.Negative;
    default:
      return undefined;
  }
};

const getButtonVariantClasses = (
  active: boolean,
  disabled: boolean,
  variant?: OneTalentButtonVariant
): Parameters<typeof clsx> => {
  switch (variant) {
    case OneTalentButtonVariant.Primary: {
      if (disabled) {
        return [
          '!bg-button-primary-fill-disabled !text-button-primary-text-disabled'
        ];
      }
      if (active) {
        return [
          '!bg-button-primary-fill-pressed !text-button-primary-text-all'
        ];
      }
      return [
        '!bg-button-primary-fill-rested !text-button-primary-text-all',
        'supports-hover:hover:!bg-button-primary-fill-hover supports-hover:hover:!text-button-primary-text-all',
        'active:!bg-button-primary-fill-pressed active:!text-button-primary-text-all'
      ];
    }
    case OneTalentButtonVariant.Secondary:
      {
        if (disabled) {
          return [
            '!bg-button-secondary-default-fill-disabled !text-button-secondary-default-text-disabled'
          ];
        }

        if (active) {
          return [
            '!bg-button-secondary-default-fill-pressed !text-button-secondary-default-text-pressed'
          ];
        }
      }
      return [
        '!bg-button-secondary-default-fill-rested !text-button-secondary-default-text-rested',
        'supports-hover:hover:!bg-button-secondary-default-fill-hover supports-hover:hover:!text-button-secondary-default-text-hover',
        'active:!bg-button-secondary-default-fill-pressed active:!text-button-secondary-default-text-pressed'
      ];
    case OneTalentButtonVariant.Tertiary:
      return [
        active
          ? '!bg-button-tertiary-fill-pressed hover:!bg-button-tertiary-fill-pressed'
          : '!bg-button-tertiary-fill-rested !text-button-tertiary-text-rested',
        disabled
          ? 'disabled:!text-button-tertiary-text-disabled disabled:cursor-not-allowed'
          : 'hover:!bg-button-tertiary-fill-hover active:!bg-button-tertiary-fill-pressed'
      ];
    case OneTalentButtonVariant.PrimaryPurple:
      if (disabled) {
        return [
          '!bg-button-purple-fill-disabled !text-button-purple-text-disabled'
        ];
      } else if (active) {
        return ['!bg-button-purple-fill-pressed !text-button-purple-text-all'];
      } else {
        return [
          '!bg-button-purple-fill-rested !text-button-purple-text-all',
          'supports-hover:hover:!bg-button-purple-fill-hover supports-hover:hover:!text-button-purple-text-all',
          'active:!bg-button-purple-fill-pressed active:!text-button-purple-text-all',
          'disabled:!bg-button-purple-fill-disabled disabled:!text-button-purple-text-disabled'
        ];
      }
    case OneTalentButtonVariant.SecondaryPurple:
      if (disabled) {
        return [
          '!bg-button-secondary-purple-fill-disabled !text-button-secondary-purple-text-disabled'
        ];
      } else if (active) {
        return [
          '!bg-button-secondary-purple-fill-pressed !text-button-secondary-purple-text-pressed'
        ];
      } else {
        return [
          '!bg-button-secondary-purple-fill-rested !text-button-secondary-purple-text-rested',
          'supports-hover:hover:!bg-button-secondary-purple-fill-hover supports-hover:hover:!text-button-secondary-purple-text-hover',
          'active:!bg-button-secondary-purple-fill-pressed active:!text-button-secondary-purple-text-pressed',
          'disabled:!bg-button-secondary-purple-fill-disabled disabled:!text-button-secondary-purple-text-disabled'
        ];
      }
    case OneTalentButtonVariant.SecondaryPositive:
      if (disabled) {
        return [
          '!bg-button-secondary-positive-fill-disabled !text-button-secondary-positive-text-disabled'
        ];
      } else if (active) {
        return [
          '!bg-button-secondary-positive-fill-pressed !text-button-secondary-positive-text-pressed'
        ];
      } else {
        return [
          '!bg-button-secondary-positive-fill-rested !text-button-secondary-positive-text-rested',
          'supports-hover:hover:!bg-button-secondary-positive-fill-hover supports-hover:hover:!text-button-secondary-positive-text-hover',
          'active:!bg-button-secondary-positive-fill-pressed active:!text-button-secondary-positive-text-pressed',
          'disabled:!bg-button-secondary-positive-fill-disabled  disabled:!text-button-secondary-positive-text-disabled'
        ];
      }
    case OneTalentButtonVariant.Negative:
      if (disabled) {
        return [
          '!bg-button-secondary-negative-fill-disabled !text-button-secondary-negative-text-disabled'
        ];
      } else if (active) {
        return [
          '!bg-button-secondary-negative-fill-pressed !text-button-secondary-negative-text-pressed'
        ];
      } else {
        return [
          '!bg-button-secondary-negative-fill-rested !text-button-secondary-negative-text-rested',
          'supports-hover:hover:!bg-button-secondary-negative-fill-hover supports-hover:hover:!text-button-secondary-negative-text-hover',
          'active:!bg-button-secondary-negative-fill-pressed active:!text-button-secondary-negative-text-pressed',
          'disabled:!bg-button-secondary-negative-fill-disabled disabled:!text-button-secondary-negative-text-disabled'
        ];
      }
    case OneTalentButtonVariant.SecondaryDefault:
      if (disabled) {
        return [
          '!bg-button-secondary-default-fill-disabled !text-button-secondary-default-text-disabled'
        ];
      } else if (active) {
        return [
          '!bg-button-secondary-default-fill-pressed !text-button-secondary-default-text-pressed'
        ];
      } else {
        return [
          '!bg-button-secondary-default-fill-rested !text-button-secondary-default-text-rested',
          'supports-hover:hover:!bg-button-secondary-default-fill-hover supports-hover:hover:!text-button-secondary-default-text-hover',
          'active:!bg-button-secondary-default-fill-pressed active:!text-button-secondary-default-text-pressed',
          'disabled:!bg-button-secondary-default-fill-disabled disabled:!text-button-secondary-default-text-disabled'
        ];
      }
    case OneTalentButtonVariant.White:
      if (disabled) {
        return [
          '!bg-button-tertiary-fill-disabled !text-button-tertiary-text-disabled'
        ];
      } else if (active) {
        return [
          '!bg-modal-button-fill-pressed !text-button-tertiary-text-pressed'
        ];
      } else {
        return [
          '!bg-modal-button-fill-rested !text-button-tertiary-text-rested',
          'supports-hover:hover:!bg-modal-button-fill-hover supports-hover:hover:!text-button-tertiary-text-hover',
          'active:!bg-modal-button-fill-pressed active:!text-button-tertiary-text-pressed',
          'disabled:!bg-button-tertiary-fill-disabled disabled:!text-button-tertiary-text-disabled'
        ];
      }
    case OneTalentButtonVariant.Link:
      {
        if (disabled) {
          return ['!text-link-disabled'];
        }

        if (active) {
          return ['!text-link-pressed'];
        }
      }
      return [
        '!text-link-rested',
        'hover:!text-link-hover',
        'active:!text-link-pressed'
      ];
    default:
      return [];
  }
};

const getButtonSizeClasses = (size?: ButtonSize) => {
  switch (size) {
    case ButtonSize.Small:
      return 'text-cta-3-medium h-32';

    case ButtonSize.Medium:
      return 'text-cta-2-medium h-40';

    case ButtonSize.Large:
      return 'text-cta-1-medium h-48';

    default:
      return '';
  }
};

export const F: FC = () => (
  <div className="disabled:!bg-button-tertiary-fill-disabled disabled:!text-button-tertiary-text-disabled"></div>
);

export interface OneTalentButtonProps
  extends ButtonHTMLAttributes<HTMLButtonElement> {
  children?: ReactNode;
  className?: string;
  disabled?: boolean;
  size?: ButtonSize;
  tooltipClassName?: string;
  tooltipText?: ReactNode;
  tooltipVariant?: TooltipVariant;
  variant?: OneTalentButtonVariant;
  active?: boolean;
  onClick?: (e: MouseEvent<HTMLButtonElement>) => void;
  dataAttributes?: string;
}

export const OneTalentButton: FC<OneTalentButtonProps> = memo(
  ({
    children,
    className,
    disabled = false,
    size,
    tooltipClassName,
    tooltipText,
    tooltipVariant = TooltipVariant.Dark,
    variant,
    onClick,
    active = false,
    dataAttributes,
    ...rest
  }) => (
    <TooltipWrapperButton
      tooltipClassName={clsx('text-body-3-medium', tooltipClassName)}
      tooltipText={tooltipText}
      tooltipVariant={tooltipVariant}
    >
      {({ onTouchStart } = {}) => (
        <Button
          dataAttributes={dataAttributes}
          className={clsx(
            className,
            ...getButtonVariantClasses(active, disabled, variant),
            ...getButtonSizeClasses(size),
            // TODO: remove these styles when the functionality is fixed within the scope of this issue: https://dev.azure.com/DevOpsAD/OneTalent/_workitems/edit/218819
            {
              '!bg-transparent !text-link-disabled':
                variant === OneTalentButtonVariant.Link && disabled
            }
          )}
          disabled={disabled}
          size={size}
          variant={buttonVariantAdapter(variant)}
          onClick={onClick}
          onTouchStart={onTouchStart}
          {...rest}
        >
          {children}
        </Button>
      )}
    </TooltipWrapperButton>
  )
);
