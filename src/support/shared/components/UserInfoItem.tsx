import { forwardRef } from 'react';

import { Tag } from '@ot/onetalent-ui-kit';
import clsx from 'clsx';

import { TextMatchHighlighter } from '@oh/components';
import { Nullable } from '@oh/contracts';

import { Typography, TypographyVariant } from './Typography';
import { UserProfileAvatar } from './UserProfileAvatar';

export interface UserInfoItemProp {
  noPadding?: boolean;
  email: string;
  showEmail?: boolean;
  showId?: boolean;
  name?: Nullable<string>;
  positionTitle?: Nullable<string>;
  highlightText?: Nullable<string>;
  id?: Nullable<string>;
  isRFI?: boolean;
  isOneLineDescription?: boolean;
  avatarSize?: number;
  showActiveStatus?: boolean;
  isActive?: boolean;
}

export const UserInfoItem = forwardRef<HTMLDivElement, UserInfoItemProp>(
  (
    {
      showActiveStatus = false,
      isActive = false,
      noPadding,
      email,
      showEmail = true,
      showId = true,
      name,
      positionTitle,
      highlightText,
      id,
      isRFI = false,
      isOneLineDescription = false,
      avatarSize = 40
    },
    ref
  ) => (
    <div
      data-attributes="UserInfoItem"
      className={clsx('flex items-center', {
        'p-[12px]': !noPadding,
        'overflow-hidden': !isRFI
      })}
      ref={ref}
    >
      <UserProfileAvatar
        size={avatarSize}
        email={email}
        name={name}
        className="mr-8"
      />
      <div className="overflow-hidden">
        {name && (
          <Typography
            className="typography--primary mb-4"
            variant={TypographyVariant.Body2Medium}
          >
            <TextMatchHighlighter text={name} textToHighlight={highlightText} />
          </Typography>
        )}
        <div
          className={clsx({
            flex: isOneLineDescription,
            'items-center': isOneLineDescription
          })}
        >
          {showActiveStatus && (
            <Tag
              variant={isActive ? 'Green' : 'Red'}
              size="Small"
              type="Solid"
              style={isOneLineDescription ? { marginRight: 5 } : undefined}
            >
              {isActive ? 'Active' : 'Not Active'}
            </Tag>
          )}
          {showId && id && (
            <Typography
              className="typography--secondary"
              style={isOneLineDescription ? { paddingRight: 5 } : undefined}
              variant={TypographyVariant.DataTickRegular}
            >
              <TextMatchHighlighter
                text={`ID: ${id}`}
                textToHighlight={highlightText}
                isBoldHighlight={false}
              />
            </Typography>
          )}
          {positionTitle && (
            <Typography
              className="typography--secondary"
              style={isOneLineDescription ? { paddingRight: 5 } : undefined}
              variant={TypographyVariant.DataTickRegular}
            >
              <TextMatchHighlighter
                text={`${positionTitle}; `}
                textToHighlight={highlightText}
                isBoldHighlight={false}
              />
            </Typography>
          )}
          {showEmail && (
            <Typography
              className="typography--secondary truncate"
              variant={TypographyVariant.DataTickRegular}
            >
              <TextMatchHighlighter
                text={email}
                textToHighlight={highlightText}
              />
            </Typography>
          )}
        </div>
      </div>
    </div>
  )
);
