import './style.scss';

import {
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';

import { DataTable } from '@epam/uui';
import {
  DataColumnProps,
  DataRowProps,
  DataSourceState,
  useLazyDataSource
} from '@epam/uui-core';
import { UseQueryResult } from '@tanstack/react-query';

import { Loader } from '@oh/components';

import { ErrorScreen } from '../ErrorScreen';

type TreeItem<TId> = {
  id?: TId;
  parentId?: TId | null;
  childCount?: number;
  totalChildCount?: number;
  totalChildIds?: string[];
};

export interface TreeDataTableProps<TItem, TId> {
  queryResult: UseQueryResult<{ items: Array<TItem & TreeItem<TId>> }>;
  columns: DataColumnProps<TItem>[];
  renderNoResults?: () => ReactNode;
  onRowClick?: (id: TId) => void;
  search?: string;
  isSelectable?: boolean;
  onCheck?: (ids: TId[]) => void;
  getId?: (item: TItem) => TId;
  id?: string;
}

export function TreeDataTable<TItem, TId>({
  columns,
  onRowClick,
  queryResult,
  search,
  isSelectable = false,
  onCheck,
  renderNoResults,
  getId,
  id
}: TreeDataTableProps<TItem, TId>) {
  const {
    data: staleData,
    isFetching,
    isPending,
    isError,
    error
  } = queryResult;
  const data = isError || isFetching || isPending ? undefined : staleData;

  const onClick = useCallback(
    (props: DataRowProps<TItem & TreeItem<TId>, TId>) => onRowClick?.(props.id),
    [onRowClick]
  );

  const [dataSourceState, setDataSourceState] = useState<
    DataSourceState<unknown, TId>
  >({});

  const isItemFoldedByDefault = (item: TreeItem<TId>) => {
    if (!search || !data?.items) return true;
    return !data.items.some((dataItem) => dataItem.id === item.id);
  };

  const dataSource = useLazyDataSource<TItem & TreeItem<TId>, TId, unknown>(
    {
      api: (request, context) => {
        const from = request.range?.from ?? 0;
        const count = request.range?.count ?? 0;
        const items = data?.items
          .filter(
            ({ parentId, totalChildCount, totalChildIds }) =>
              parentId == context?.parentId &&
              totalChildCount !== 0 &&
              totalChildIds?.length !== 0
          )
          .slice(from, from + count);
        return Promise.resolve({ items: items ?? [] });
      },
      getRowOptions: () => ({
        onClick
      }),
      getParentId: (item) => item.parentId ?? undefined,
      getChildCount: (l) => l.childCount ?? 0,
      isFoldedByDefault: (item) => isItemFoldedByDefault(item),
      cascadeSelection: 'implicit',
      backgroundReload: true,
      rowOptions: {
        checkbox: { isVisible: isSelectable },
        isSelectable: true
      },
      ...(getId ? { getId } : {})
    },
    [data?.items]
  );

  const view = dataSource.useView(dataSourceState, setDataSourceState);

  useEffect(() => {
    onCheck?.(dataSourceState.checked ?? []);
  }, [dataSourceState.checked, onCheck]);

  const renderNoResultsBlock = useCallback(() => null, []);

  const handleTableValueChange = useCallback(
    (
      nextState:
        | DataSourceState<unknown, TId>
        | SetStateAction<DataSourceState<unknown, TId>>
    ) => {
      setDataSourceState((prevState) => {
        const newState =
          nextState instanceof Function ? nextState(prevState) : nextState;

        return newState;
      });
    },
    []
  );

  const stateNode = useMemo(() => {
    if (isFetching || isPending) {
      return <Loader diameter={40} className="!h-auto" />;
    }

    if (isError) {
      return <ErrorScreen error={error} className="!h-auto" />;
    }

    if (data?.items.length === 0) {
      return renderNoResults?.();
    }

    return null;
  }, [
    error,
    isError,
    isFetching,
    isPending,
    data?.items.length,
    renderNoResults
  ]);

  const isNoContent = Boolean(stateNode);

  return (
    <div
      className="generic-table tree-table relative z-[3] flex flex-col justify-between"
      data-attributes={id && `${id}Table`}
      style={{ height: 676 }}
    >
      <div className={isNoContent ? 'no-content' : ''} style={{ height: 876 }}>
        <DataTable
          {...view.getListProps()}
          getRows={view.getVisibleRows}
          columns={columns}
          value={dataSourceState}
          onValueChange={handleTableValueChange}
          renderNoResultsBlock={renderNoResultsBlock}
          headerSize="36"
          columnsGap="24"
        />
      </div>

      {stateNode && (
        <div className="absolute left-1/2 top-1/2 flex flex-grow -translate-x-1/2 -translate-y-1/2">
          {stateNode}
        </div>
      )}
    </div>
  );
}
