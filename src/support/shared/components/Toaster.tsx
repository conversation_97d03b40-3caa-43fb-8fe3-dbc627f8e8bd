import { FC } from 'react';

import { reactHotToast, toast, ToastSettings } from '@oh/components';

const { ToastBar, Toaster: FoundationToaster } = reactHotToast;

export const Toaster: FC = () => {
  const handleToastClick = ({
    isClickable
  }: ToastSettings & reactHotToast.Toast) => {
    if (isClickable) {
      toast.dismiss();
    }
  };

  const renderToast = (t: ToastSettings & reactHotToast.Toast) => (
    <div data-attributes="Toaster" onClick={() => handleToastClick(t)}>
      <ToastBar toast={t} />
    </div>
  );

  return (
    <FoundationToaster
      position="top-right"
      gutter={10}
      containerStyle={{ top: 16, zIndex: 100000000 }}
      toastOptions={{ duration: 5000 }}
      reverseOrder
    >
      {renderToast}
    </FoundationToaster>
  );
};
