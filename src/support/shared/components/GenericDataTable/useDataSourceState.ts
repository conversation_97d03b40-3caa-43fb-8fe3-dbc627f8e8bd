import {
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';
import { useSearchParams } from 'react-router-dom';

import { DataSourceState } from '@epam/uui-core';
import { useQuery } from '@tanstack/react-query';

import {
  isFiltersChanged,
  isSearchChanged,
  isSortingChanged,
  orderByToQueryParamValue,
  parseOrderByQueryParam,
  transformSorting,
  transformSortingOption
} from './utils';

interface OrderBy {
  field: string;
  isDescending?: boolean;
}

export interface GenericDataSourceState<TId, TFilters>
  extends DataSourceState<unknown, TId> {
  totalResults?: number;
  rawSearch?: string;
  rawFilters?: TFilters;
  expanded?: Record<string, boolean>;
}

export interface UseDataSourceStateProps<TId, TFilters> {
  pageSize?: number;
  checked?: TId[];
  search?: string;
  filters?: TFilters;
  initialOrderBy?: OrderBy;
  initialPageNumber?: number;
  initialTotalResults?: number;
  visibleCount?: number;
}

export function useDataSourceState<TId, TFilters = unknown>({
  pageSize = 12,
  checked,
  search = '',
  filters,
  initialOrderBy,
  initialPageNumber = 1,
  initialTotalResults = 0,
  visibleCount = 20
}: UseDataSourceStateProps<TId, TFilters>) {
  const { data: cachedFilters } = useQuery({
    queryKey: ['cachedFilters', filters],
    queryFn: () => filters
  });

  const [searchParams, setSearchParams] = useSearchParams();

  const [rawDataSourceState, setRawDataSourceState] = useState<
    GenericDataSourceState<TId, TFilters>
  >({
    page: Number(searchParams.get('pageNumber')) || initialPageNumber,
    pageSize,
    checked,
    rawSearch: search,
    rawFilters: cachedFilters,
    sorting:
      parseOrderByQueryParam(searchParams.get('orderBy')) ||
      transformSortingOption(initialOrderBy),
    totalResults: initialTotalResults,
    visibleCount
  });

  const setDataSourceState = useCallback(
    (
      nextState:
        | GenericDataSourceState<TId, TFilters>
        | SetStateAction<GenericDataSourceState<TId, TFilters>>
    ) => {
      setRawDataSourceState((prevState) => {
        const newState =
          nextState instanceof Function ? nextState(prevState) : nextState;
        if (
          isSortingChanged(prevState.sorting, newState.sorting) ||
          isSearchChanged(prevState.rawSearch, newState.rawSearch) ||
          isFiltersChanged(prevState.rawFilters, newState.rawFilters)
        ) {
          return { ...newState };
        }

        return newState;
      });
    },
    []
  );

  useEffect(() => {
    setDataSourceState((prevState) => ({ ...prevState, rawSearch: search }));
  }, [search, setDataSourceState]);

  useEffect(() => {
    setDataSourceState((prevState) => ({
      ...prevState,
      rawFilters: cachedFilters
    }));
  }, [setDataSourceState, cachedFilters]);

  const orderBy = useMemo(
    () => transformSorting(rawDataSourceState.sorting),
    [rawDataSourceState.sorting]
  );

  useEffect(() => {
    if (orderBy) {
      const orderByQueryParamValue = orderByToQueryParamValue(orderBy);
      setSearchParams((searchParams) => {
        searchParams.set('orderBy', `${orderByQueryParamValue}`);
        return searchParams;
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [orderBy]);

  const parameters = useMemo(
    () => ({
      pageNumber: rawDataSourceState.page,
      pageSize: rawDataSourceState.pageSize,
      orderBy
    }),
    [orderBy, rawDataSourceState.page, rawDataSourceState.pageSize]
  );

  const dataSource = useMemo(
    () => ({
      dataSourceState: rawDataSourceState,
      setDataSourceState
    }),
    [rawDataSourceState, setDataSourceState]
  );

  return useMemo(
    () => ({
      dataSource,
      parameters,
      searchParam: rawDataSourceState.rawSearch || '',
      filtersParam: rawDataSourceState.rawFilters
    }),
    [
      dataSource,
      parameters,
      rawDataSourceState.rawFilters,
      rawDataSourceState.rawSearch
    ]
  );
}
