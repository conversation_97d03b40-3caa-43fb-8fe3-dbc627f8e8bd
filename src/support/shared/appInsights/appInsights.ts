import {
  ApplicationInsights,
  type ITelemetryItem,
  type Snippet
} from '@microsoft/applicationinsights-web';

import type { Nullable } from '@oh/contracts';

let instance: Nullable<ApplicationInsights> = null;

export const createAppInsightsInstance = (
  instrumentationKey: string
): ApplicationInsights => {
  if (!instance) {
    const config = {
      instrumentationKey,
      connectionString: `InstrumentationKey=${instrumentationKey}`,
      disableFetchTracking: false,
      enableRequestHeaderTracking: false,
      enableResponseHeaderTracking: true,
      enableAjaxErrorStatusText: true,
      enableAjaxPerfTracking: true,
      isStorageUseDisabled: true,
      maxAjaxCallsPerView: 250,
      samplingPercentage: 100,
      enableDebugExceptions: true,
      enableCorsCorrelation: true,
      disableCorrelationHeaders: true,
      ignoreHeaders: [
        'Authorization',
        'X-API-Key',
        'WWW-Authenticate',
        'Content-Security-Policy',
        'X-Client-Id'
      ]
    };

    instance = new ApplicationInsights({ config } as Snippet);
  }

  return instance;
};

export const getAppInsightsInstance = (): Nullable<ApplicationInsights> =>
  instance;

export const getTelemetryInitializer =
  (
    isExternalTraffic: boolean,
    platform: string,
    appEnv: string,
    userEmail: string | undefined
  ) =>
  (item: ITelemetryItem) => {
    if (!item.tags) {
      return;
    }

    item.tags['ai.cloud.role'] = isExternalTraffic
      ? `${platform}-PUBLIC`
      : platform;

    item.tags['ai.cloud.roleInstance'] = appEnv;
    item.tags['ai.cloud.authenticatedUserId'] = userEmail;
    item.tags['ai.user.accountId'] = userEmail;
    item.tags['env'] = appEnv;
  };

export const getCorrelationHeaders = (appId: string) => {
  if (!appId) {
    // eslint-disable-next-line no-console
    console.warn(
      'App ID is not defined. tracestate header will be incomplete.'
    );
  }

  const headers: Record<string, string> = {
    tracestate: `appId=${appId}`
  };
  const appInsightsInstance = getAppInsightsInstance();
  const traceParent = appInsightsInstance?.context.telemetryTrace.traceID;

  if (traceParent) {
    headers['traceparent'] = traceParent;
  }
  return headers;
};
