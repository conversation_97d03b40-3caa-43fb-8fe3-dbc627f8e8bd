using OneTalent.AtrAdminService.Application.PerformanceFormHistory.Models;
using OneTalent.AtrAdminService.Application.PerformanceForms.Models;
using OneTalent.AtrAdminService.Contracts.Constants;
using OneTalent.AtrService.Client;
using OneTalent.Common.Extensions.ItemId;

namespace OneTalent.AtrAdminService.Infrastructure.Integrations.AtrService;

internal static class Mappers
{
    public static IEnumerable<PerformanceFormStatus> ToPerformanceFormStatuses(this IReadOnlyCollection<StatusDto> statuses) =>
        statuses.Select(x => new PerformanceFormStatus(
            Id: MapStatusEnum(x.Id),
            Name: x.Name)
        );

    public static PerformanceFormStatuses MapStatusEnum(Statuses status) =>
        status switch
        {
            Statuses.Draft => PerformanceFormStatuses.Draft,
            Statuses.SelfAssessmentNotStarted => PerformanceFormStatuses.SelfAssessmentNotStarted,
            Statuses.SelfAssessmentInProgress => PerformanceFormStatuses.SelfAssessmentInProgress,
            Statuses.ManagerAssessmentNotStarted => PerformanceFormStatuses.ManagerAssessmentNotStarted,
            Statuses.ManagerAssessmentInProgress => PerformanceFormStatuses.ManagerAssessmentInProgress,
            Statuses.ManagerAssessmentCompleted => PerformanceFormStatuses.ManagerAssessmentCompleted,
            Statuses.DottedLineManagerEndorsementNotStarted => PerformanceFormStatuses.DottedLineManagerEndorsementNotStarted,
            Statuses.SecondLineManagerEndorsementNotStarted => PerformanceFormStatuses.SecondLineManagerEndorsementNotStarted,
            Statuses.SecondLineManagerEndorsementCompleted => PerformanceFormStatuses.SecondLineManagerEndorsementCompleted,
            Statuses.NormalizationNotStarted => PerformanceFormStatuses.NormalizationNotStarted,
            Statuses.NormalizationInProgress => PerformanceFormStatuses.NormalizationInProgress,
            Statuses.NormalizationCompleted => PerformanceFormStatuses.NormalizationCompleted,
            Statuses.RatingApprovalPending => PerformanceFormStatuses.RatingApprovalPending,
            Statuses.RatingApprovalApproved => PerformanceFormStatuses.RatingApprovalApproved,
            Statuses.Completed => PerformanceFormStatuses.Completed,
            _ => throw new InvalidOperationException($"Unsupported Statuses value: {status}")
        };

    public static AssessmentSearchQuery ToPerformanceFormSearchQuery(this PerformanceFormSearchQuery searchQuery, string employeeId) =>
       new(
           employeeId: employeeId,
           formIds: searchQuery.FormIds,
           divisionIds: searchQuery.DivisionIds,
           companyCodes: searchQuery.CompanyCodes,
           directorateIds: searchQuery.DirectorateIds,
           functionIds: searchQuery.FunctionIds,
           templateIds: searchQuery.TemplateIds,
           permissionGroupIds: searchQuery.PermissionGroupIds,
           searchEmployeeIds: searchQuery.SearchEmployeeIds,
           lineManagerIds: searchQuery.LineManagerIds,
           b2BManagerIds: searchQuery.B2BManagerIds,
           dottetLineManagerIds: searchQuery.DottetLineManagerIds,
           selfAssessmentStatuses: searchQuery.SelfAssessmentStatuses,
           performanceFormStatuses: searchQuery.PerformanceFormStatuses,
           pageNumber: searchQuery.PageNumber,
           pageSize: searchQuery.PageSize,
           orderBy: searchQuery.OrderBy is not null ? new OrderBy((SortDirection)searchQuery.OrderBy.Direction, searchQuery.OrderBy.Field) : null
       );

    public static AssessmentForm ToAssessmentForm(this AdminPerformanceForm source) =>
        new () {
            Id = source.Id,
            TemplateId = source.PerformanceFormTemplateId,
            TemplateName = source.PerformanceFormTemplateName,
            PerformanceFormStatusId = source.PerformanceFormStatusId,
            EmployeeId = source.EmployeeId,
            EmployeeFullName = null,
            CompanyCode = source.CompanyCode,
            CompanyName = null,
            DirectorateId = source.DirectorateId,
            DirectorateName = null,
            FunctionId = source.FunctionId,
            FunctionName = null,
            DivisionId = source.DivisionId,
            DivisionName = null,
            AtrGroupName = source.PermissionGroupName,
            AssessmentLineManagerId = source.AssessmentLineManagerId,
            AssessmentLineManagerName = null,
            AssessmentB2BManagerId = source.AssessmentB2BManagerId,
            AssessmentB2BManagerName = null,
            DottedLineManagerId = source.DottedLineManagerId,
            DottedLineManagerName = null,
            LastUpdated = source.UpdatedDate,
            UpdatedBy = source.UpdatedBy
        };
    public static PerformanceFormHistoryRecord ToPerformanceFormHistory(this PerformanceFormHistoryDtoV1 history) =>
        new (
            new ItemId(history.PerformanceFormId),
            history.ActionDateTime,
            history.ActionType,
            history.ActionOwnerId,
            history.ActionOwnerName,
            history.MajorStatusTo,
            history.MinorStatusTo,
            history.MajorStatusFrom,
            history.MinorStatusFrom,
            history.ActionReason,
            history.ActionOwnerJobRole,
            history.GroupCompany,
            history.Directorate,
            history.Function,
            history.Division,
            history.PerformanceCycleName,
            history.AssessmentLineManagerName,
            history.AssessmentB2BManagerName,
            history.DottedLineManagerName,
            history.CurrentLineManager,
            history.ManagerRating,
            history.ManagerRatingName,
            history.ManagerNineBoxPotential,
            history.NineBoxPotentialRating,
            history.NormalizedRating,
            history.NormalizedRatingName,
            history.NormalizedNineBoxPotential,
            history.WorkflowId,
            history.WfAssigneeName);
}
