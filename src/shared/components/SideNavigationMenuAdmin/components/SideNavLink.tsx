import { PropsWithChildren } from 'react';

import { Button } from '@ot/onetalent-ui-kit';

export interface SideNavLinkProps {
  isActive: boolean;
  onClick: () => void;
}

export const SideNavLink = ({
  isActive,
  onClick,
  children
}: PropsWithChildren<SideNavLinkProps>) => (
  <Button
    className="w-full"
    size="Medium"
    variant={isActive ? 'Primary' : 'Link'}
    onClick={onClick}
  >
    {children}
  </Button>
);
