import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { SideNavMenuItem } from '@/shared/components/SideNavigationMenuAdmin/types';

export const useSectionsSideNavigation = (navItems: SideNavMenuItem[]) => {
  const navigate = useNavigate();

  const handleNavItemClick = useCallback(
    (item: SideNavMenuItem) => {
      navigate(`/support/${item.id}`);
    },
    [navigate]
  );

  return {
    navItems,
    handleNavItemClick
  };
};
