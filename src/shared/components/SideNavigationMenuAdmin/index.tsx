import { memo } from 'react';

import {
  <PERSON>y<PERSON>ontainer,
  <PERSON>NavContainer,
  SideNavContent
} from './components';
import { SideNavMenuItem, SideNavVariant } from './types';

interface SideNavigationMenuProps {
  items: SideNavMenuItem[];
  variant?: SideNavVariant;
  onNavItemClick: (item: SideNavMenuItem) => void;
}

export const SideNavigationMenu = memo(
  ({ items, onNavItemClick }: SideNavigationMenuProps) => (
    <StickyContainer>
      <SideNavContainer>
        <SideNavContent items={items} onNavItemClick={onNavItemClick} />
      </SideNavContainer>
    </StickyContainer>
  )
);

SideNavigationMenu.displayName = 'SideNavigationMenuAdmin';
