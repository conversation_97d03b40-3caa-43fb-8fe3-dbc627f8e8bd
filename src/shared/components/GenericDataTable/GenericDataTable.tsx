import './styles.base.scss';

import {
  Dispatch,
  ReactNode,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo
} from 'react';

import {
  DataTable,
  DataTableRow,
  DataTableRowMods,
  FlexSpacer
} from '@epam/uui';
import {
  DataColumnGroupProps,
  DataColumnProps,
  DataRowOptions,
  DataRowProps,
  DataSourceState,
  DataTableRowProps,
  useArrayDataSource
} from '@epam/uui-core';
import {
  DropdownOption,
  DropdownSingleSelect,
  DropdownSize,
  Loader
} from '@ot/onetalent-ui-kit';
import { UseQueryResult } from '@tanstack/react-query';
import clsx from 'clsx';
import isUndefined from 'lodash/isUndefined';

import { Tooltip } from '@oh/components';
import { TooltipVariant } from '@oh/constants';

import { DEFAULT_PAGE_SIZE } from '@/shared/constants';

import { GenericDataSourceState } from './useDataSourceState';
import { createNonSortableSource, NonSortable } from './utils';

import { ErrorScreen } from '../ErrorScreen';
import { Pagination } from '../Pagination';
import { TestIdWrapper } from '../qa-automation';

import { PageModel } from '../../core/domainModel';

export type TableLoadingStrategy = 'optimistic' | 'alwaysShowData' | 'default';

export type CustomRowExtraOptionsGetter<TItem> = (item: TItem) =>
  | {
      tooltipContent?: ReactNode;
      tooltipClassName?: string;
    }
  | undefined;

const LOADING_STRATEGIES_TO_KEEP_DATA: TableLoadingStrategy[] = [
  'optimistic',
  'alwaysShowData'
];

export type GenericTableProps<TItem, TId, TFilters = unknown> = {
  dataSourceState: GenericDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<GenericDataSourceState<TId, TFilters>>
  >;
  queryResult: UseQueryResult<PageModel<TItem>>;
  columns: DataColumnProps<TItem>[];
  columnGroups?: DataColumnGroupProps[];
  renderRowContent?: (item: TItem) => React.ReactNode;
  renderError?: () => ReactNode;
  renderNoResults?: () => ReactNode;
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  getRowOptions?: (
    item: TItem,
    index?: number
  ) => DataRowOptions<NonSortable<TItem>, TId>;
  isFoldedByDefault?(
    item: NonSortable<TItem>,
    state: DataSourceState<unknown, TId>
  ): boolean;
  isSelectable?: boolean;
  showPagination?: boolean;
  styles?: {
    minHeight?: number | string;
  };
  dataAttributes?: string;
  classNames?: string;
  paginationContainerClassName?: string;
  errorContainerClassName?: string;
  paginationSize?: '24' | '30';
  onVisibleRowsCountChanged?: (visibleRows: number | undefined) => void;
  loadingStrategy?: TableLoadingStrategy;
  loaderText?: string;
  setPageSize?: (pageSize: number) => void;
  pageSizes?: number[];
} & (
  | {
      withCustomRow: true;
      rowDataAttributes?: string;
      getCustomRowExtraOptions?: CustomRowExtraOptionsGetter<TItem>;
    }
  | {
      withCustomRow?: never;
      rowDataAttributes?: never;
      getCustomRowExtraOptions?: never;
    }
);

export function GenericDataTable<TItem, TId, TFilters>({
  dataSourceState,
  setDataSourceState,
  queryResult,
  columns,
  columnGroups,
  onRowClick,
  getRowOptions: customGetRowOptions,
  isFoldedByDefault: customIsFoldedByDefault,
  renderRowContent,
  renderError,
  renderNoResults,
  styles,
  isSelectable = false,
  showPagination = true,
  dataAttributes,
  rowDataAttributes,
  classNames,
  paginationContainerClassName,
  errorContainerClassName,
  onVisibleRowsCountChanged,
  paginationSize = '30',
  loadingStrategy = 'default',
  loaderText,
  pageSizes = [],
  setPageSize,
  withCustomRow,
  getCustomRowExtraOptions
}: GenericTableProps<TItem, TId, TFilters>) {
  const {
    data: staleData,
    isFetching,
    isPending,
    isError,
    error
  } = queryResult;

  const shouldHideDataOnFetching = LOADING_STRATEGIES_TO_KEEP_DATA.includes(
    loadingStrategy
  )
    ? !staleData?.items.length && isFetching
    : isFetching;

  const data =
    isError || shouldHideDataOnFetching || isPending ? undefined : staleData;

  const { items, sortBy } = useMemo(
    () => createNonSortableSource(data?.items || []),
    [data?.items]
  );

  const onClick = useCallback(
    (props: DataRowProps<TItem, TId>) => onRowClick?.(props.id, props),
    [onRowClick]
  );

  const defaultGetRowOptions = () => ({
    checkbox: { isVisible: isSelectable },
    isSelectable: true,
    onClick
  });

  const getRowOptions = customGetRowOptions || defaultGetRowOptions;

  const defaultIsFoldedByDefault = () => false;
  const isFoldedByDefault = customIsFoldedByDefault || defaultIsFoldedByDefault;

  const dataSource = useArrayDataSource<NonSortable<TItem>, TId, unknown>(
    {
      getRowOptions,
      isFoldedByDefault,
      cascadeSelection: 'explicit',
      items,
      sortBy
    },
    [items]
  );
  const view = dataSource.useView(dataSourceState, setDataSourceState, {});
  const props = view.getListProps();

  useEffect(() => {
    onVisibleRowsCountChanged?.(props.rowsCount);
  }, [onVisibleRowsCountChanged, props.rowsCount]);

  const setTotalResults = useCallback(
    (totalResults: number | undefined) => {
      setDataSourceState((state) =>
        state.totalResults !== totalResults ? { ...state, totalResults } : state
      );
    },
    [setDataSourceState]
  );

  useEffect(() => {
    if (!data) {
      if (isError) {
        setTotalResults(undefined);
      }
      return;
    }

    const totalResults = data.page?.totalResults || 0;
    setTotalResults(totalResults);
  }, [data, isError, setTotalResults]);

  const page = dataSourceState.page;
  const totalResults = data?.page?.totalResults || dataSourceState.totalResults;
  const pageSize = dataSourceState.pageSize;
  const totalPages =
    totalResults && pageSize ? Math.ceil(totalResults / pageSize) : 0;

  const onPageValueChange = useCallback(
    (newPage: number) => {
      setDataSourceState((state) => ({ ...state, page: newPage }));
    },
    [setDataSourceState]
  );

  useEffect(() => {
    if (page && page > 1 && totalPages < page) {
      onPageValueChange(page - 1);
    }
  }, [page, onPageValueChange, totalPages]);

  const stateNode = useMemo(() => {
    if (isFetching && !isPending && loadingStrategy === 'optimistic') {
      return null;
    }

    const loaderElement = (
      <TestIdWrapper testId={dataAttributes && `${dataAttributes}Loader`}>
        <div className="flex h-full min-h-[520px] w-full">
          <Loader
            size="Medium"
            className="!h-auto text-center"
            description={
              loaderText && (
                <>
                  Please wait,
                  <br />
                  {loaderText}
                </>
              )
            }
          />
        </div>
      </TestIdWrapper>
    );

    if (isFetching || isPending) {
      return loadingStrategy === 'alwaysShowData' ? (
        <div className="absolute inset-0 z-50 flex items-center justify-center bg-[#ffffff63] dark-mode:bg-[#00000063]">
          {loaderElement}
        </div>
      ) : (
        loaderElement
      );
    }

    if (isError) {
      if (renderError) {
        return renderError?.();
      }
      return (
        <ErrorScreen
          error={error}
          className={clsx('min-h-[520px]', errorContainerClassName)}
        />
      );
    }

    if (items.length === 0) {
      return renderNoResults?.();
    }

    return null;
  }, [
    isFetching,
    isPending,
    loadingStrategy,
    dataAttributes,
    loaderText,
    isError,
    items.length,
    renderError,
    error,
    errorContainerClassName,
    renderNoResults
  ]);

  const renderCustomRow = useCallback(
    (rowProps: DataRowProps<TItem, TId> & DataTableRowMods) => {
      const { tooltipContent, tooltipClassName } =
        rowProps.value && getCustomRowExtraOptions
          ? getCustomRowExtraOptions(rowProps.value) ?? {}
          : {};

      const row = (
        <DataTableRow
          key={rowProps.rowKey}
          {...rowProps}
          rawProps={{
            ...(rowDataAttributes && { 'data-attributes': rowDataAttributes }),
            ...(rowProps.isDisabled && { 'data-disabled': 'true' })
          }}
        />
      );

      return tooltipContent ? (
        <Tooltip
          title={tooltipContent}
          variant={TooltipVariant.Dark}
          className={clsx(
            '!rounded-[4px] !px-8 !py-4 !text-body-4-regular',
            tooltipClassName
          )}
        >
          <div>{row}</div>
        </Tooltip>
      ) : (
        row
      );
    },
    [rowDataAttributes, getCustomRowExtraOptions]
  );

  const renderRow = useCallback(
    (props: DataTableRowProps<TItem, TId>): React.ReactNode => {
      const isExpanded = dataSourceState?.expanded?.[props.id as string];
      const handleRowClick = (props: DataRowProps<TItem, TId>) =>
        setDataSourceState((current) => ({
          ...current,
          expanded: {
            ...current.expanded,
            [props.id as string]: !current.expanded?.[props.id as string]
          }
        }));

      return (
        <div data-attributes="tableExpandableRow" aria-expanded={isExpanded}>
          <DataTableRow
            key={props.rowKey}
            {...props}
            indent={(props.indent ?? 0) + 1}
            onClick={handleRowClick}
            isFoldable={true}
            isFolded={!isExpanded}
            onFold={handleRowClick}
            borderBottom={!(isExpanded || props.isLastChild)}
          />
          {isExpanded && renderRowContent && props.value && (
            <div
              className={clsx('row-content-wrapper', {
                last: props.isLastChild
              })}
            >
              {renderRowContent(props.value)}
            </div>
          )}
        </div>
      );
    },
    [dataSourceState?.expanded, setDataSourceState, renderRowContent]
  );

  const isNoContent = Boolean(stateNode);

  const renderNoResultsBlock = useCallback(() => null, []);

  const onPageSizeChange = useCallback(
    (option?: DropdownOption) => {
      setPageSize?.(Number(option?.id ?? DEFAULT_PAGE_SIZE));
    },
    [setPageSize]
  );

  const minHeight = isUndefined(styles?.minHeight) ? 676 : styles.minHeight;

  const pageSizeOptions = pageSizes.map<DropdownOption>((item) => ({
    id: item,
    title: String(item)
  }));

  const isPaginationShown =
    !isFetching &&
    !isPending &&
    !isError &&
    page !== undefined &&
    totalPages > 1 &&
    showPagination;
  const isPageSizeSelectorShown =
    !isFetching && !isPending && !isError && pageSizeOptions?.length > 0;

  const normalizedColumns = useMemo(
    () =>
      columns.map((column) => ({ ...column, renderTooltip: () => undefined })),
    [columns]
  );

  const normalizedColumnGroups = useMemo(
    () =>
      columnGroups?.map((columnGroup) => ({
        ...columnGroup,
        renderTooltip: () => undefined
      })),
    [columnGroups]
  );

  return (
    <div
      className={clsx(
        'generic-table tree-table relative z-0 flex flex-col justify-between',
        classNames,
        { 'table-with-custom-row': renderRowContent }
      )}
      data-attributes={dataAttributes}
      style={{ minHeight }}
    >
      <div
        className={clsx('relative', {
          'no-content': isNoContent,
          'h-full': !stateNode
        })}
      >
        <DataTable
          {...props}
          getRows={view.getVisibleRows}
          renderRow={
            withCustomRow ? renderCustomRow : renderRowContent && renderRow
          }
          columns={normalizedColumns}
          columnGroups={normalizedColumnGroups}
          value={dataSourceState}
          onValueChange={setDataSourceState}
          renderNoResultsBlock={renderNoResultsBlock}
          headerSize="36"
          columnsGap="24"
        />
      </div>
      {stateNode && (
        <div
          className={clsx(
            'flex flex-grow',
            (isError || !items.length) && 'items-center'
          )}
        >
          {stateNode}
        </div>
      )}

      <div
        className={clsx('flex items-center justify-between pb-12 pt-[22px]', {
          hidden: !isPaginationShown && !isPageSizeSelectorShown
        })}
      >
        {isPaginationShown && (
          <>
            <FlexSpacer />
            <Pagination
              className={clsx('flex-1', paginationContainerClassName)}
              totalPages={totalPages}
              currentPage={page}
              onPageValueChange={onPageValueChange}
              size={paginationSize}
            />
          </>
        )}

        {isPageSizeSelectorShown ? (
          <div className="flex-1">
            <DropdownSingleSelect
              label="items per page"
              className="ml-auto max-w-96"
              size={DropdownSize.Small}
              value={{
                id: pageSize ?? DEFAULT_PAGE_SIZE,
                title: String(pageSize ?? DEFAULT_PAGE_SIZE)
              }}
              options={pageSizeOptions}
              inputVariant="Primary"
              showClear={false}
              onChange={onPageSizeChange}
            />
          </div>
        ) : (
          <FlexSpacer />
        )}
      </div>
    </div>
  );
}
