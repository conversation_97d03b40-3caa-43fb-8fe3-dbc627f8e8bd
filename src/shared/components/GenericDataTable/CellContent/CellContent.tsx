import { FC, memo, PropsWithChildren } from 'react';

import clsx from 'clsx';

import { Tooltip, Typography, TypographyVariant } from '@oh/components';
import { TooltipProps } from '@oh/components/dist/types/components/tooltip/types';

import { useConditionalTooltipProps } from 'shared/hooks';

interface CellContentProps {
  tooltipProps?: Partial<TooltipProps>;
  classNames?: string;
  onClick?: React.MouseEventHandler<HTMLDivElement>;
}

export const CellContent: FC<PropsWithChildren<CellContentProps>> = memo(
  ({ children, tooltipProps, classNames = 'line-clamp-1', ...props }) => {
    const cellContent = (
      <Typography
        variant={TypographyVariant.Body2Regular}
        className={clsx('cell-content ml-4 w-full text-ellipsis', classNames)}
        as="div"
        {...props}
      >
        {children}
      </Typography>
    );

    const conditionalTooltipProps = useConditionalTooltipProps(tooltipProps);

    if (!tooltipProps) {
      return cellContent;
    }

    return (
      <Tooltip
        className={clsx(
          'text-body-4-regular text-tooltip-text',
          '[overflow-wrap:anywhere]',
          'shadow-tooltip',
          'rounded-md',
          '!max-w-[230px]',
          'px-8 py-4'
        )}
        arrowClassName="w-8 h-8 !bottom-[-3px]"
        {...conditionalTooltipProps}
      >
        {cellContent}
      </Tooltip>
    );
  }
);
