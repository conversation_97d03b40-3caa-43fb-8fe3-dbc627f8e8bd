import { FC, useCallback, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

import { Tabs } from '@ot/onetalent-ui-kit';

import { TABS } from './tabs';

export const AdminTabs: FC = () => {
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const currentTab = pathname;

  useEffect(() => {
    if (pathname === '/support' || pathname === '/support/') {
      navigate('/support/employee');
    }
  }, [pathname, navigate]);

  const onTabChange = useCallback(
    (tabId: string) => {
      navigate(`/${tabId}`);
    },
    [navigate]
  );

  return (
    <Tabs
      className="w-full"
      size="Large"
      activeTabId={currentTab}
      items={TABS}
      onChange={onTabChange}
    />
  );
};
