import { FC } from 'react';
import { Outlet, useLocation } from 'react-router-dom';

import { Layout } from '@ot/onetalent-ui-kit';

import { SideNavigationMenu } from '@/shared/components/SideNavigationMenuAdmin';
import { useSectionsSideNavigation } from '@/shared/components/SideNavigationMenuAdmin/useSectionsSideNavigation';
import { GeneralSideNavItems } from '@/shared/components/SideNavigationMenuAdmin/utils';
import { AdminLayout } from '@/shared/layouts/AdminCommonLayout/AdminLayout';
import { AdminTabs } from '@/shared/layouts/AdminCommonLayout/AdminTabs';

const AdminCommonLayout: FC = () => {
  const { pathname } = useLocation();
  const currentTab = pathname;

  const { navItems, handleNavItemClick } =
    useSectionsSideNavigation(GeneralSideNavItems);

  const isSupportAdmin = currentTab.startsWith('/support');

  return (
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    <Layout headerTitle="ADMIN" variant="L1" contentClassName="gap-24">
      <AdminTabs />
      <AdminLayout.Content
        sideNavMenu={
          isSupportAdmin && (
            <SideNavigationMenu
              items={navItems}
              onNavItemClick={handleNavItemClick}
            />
          )
        }
      >
        <Outlet />
      </AdminLayout.Content>
    </Layout>
  );
};

export default AdminCommonLayout;
