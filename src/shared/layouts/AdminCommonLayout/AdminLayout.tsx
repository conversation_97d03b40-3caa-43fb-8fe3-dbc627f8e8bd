import { PropsWithChildren, ReactNode } from 'react';

interface ContentProps extends PropsWithChildren {
  sideNavMenu?: ReactNode;
}

const Content = ({ children, sideNavMenu }: ContentProps) => (
  <div data-attributes="AdminLayout" className="flex w-full flex-auto flex-col">
    <div className="flex flex-auto gap-24">
      {sideNavMenu}
      <div className="flex w-9/12 flex-auto flex-col">
        <div className="flex-auto">{children}</div>
      </div>
    </div>
  </div>
);

export const AdminLayout = {
  Content
};
