import { FC, PropsWithChildren, ReactNode, useMemo } from 'react';

import { Loader } from '@ot/onetalent-ui-kit';
import { StatusCodes } from 'http-status-codes';

import { AdminService } from '@/api';
import { NoAccessPage } from '@/shared/components';
import { useBreakpointsContext } from '@/shared/contexts';

import { CurrentEmployeeContext, ICurrentUserContext } from '../contexts';
import { useCurrentEmployee } from '../hooks';
import { EmployeeModel } from '../models';

import { ErrorScreen } from '../../../components/ErrorScreen';

export const CurrentEmployeeProvider: FC<{ children: ReactNode }> = ({
  children
}) => {
  const { data, isPending, isError, error } = useCurrentEmployee();
  const { isMobile } = useBreakpointsContext();

  if (isPending) {
    return (
      <div
        data-attributes="CurrentEmployeeProvider"
        className="flex h-full w-full"
      >
        <Loader size={isMobile ? 'Small' : 'Medium'} />
      </div>
    );
  }

  if (
    error instanceof AdminService.ResponseError &&
    error.response.status === StatusCodes.FORBIDDEN
  ) {
    return <NoAccessPage />;
  }

  if (isError) {
    return <ErrorScreen />;
  }

  return (
    <CurrentEmployeeProviderWrapper employee={data}>
      {children}
    </CurrentEmployeeProviderWrapper>
  );
};

interface CurrentEmployeeProviderWrapperProps extends PropsWithChildren {
  employee: EmployeeModel;
}

const CurrentEmployeeProviderWrapper: FC<
  CurrentEmployeeProviderWrapperProps
> = ({ employee, children }) => {
  const contextValue = useMemo<ICurrentUserContext>(
    () => ({ employee }),
    [employee]
  );

  return (
    <CurrentEmployeeContext.Provider value={contextValue}>
      {children}
    </CurrentEmployeeContext.Provider>
  );
};
