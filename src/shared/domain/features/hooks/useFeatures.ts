import { Features } from '@ot/feature-flags';
import { logger } from '@services/logger';
import { useSuspenseQuery } from '@tanstack/react-query';

import { useSharedServiceApi } from '@/api';
import { AdminFeatureFlags } from '@/shared/modules/featureFlags';

import { FEATURES_REQUEST_KEYS } from '../featuresRequestKeys';

export function useFeatures() {
  const { sharedApi } = useSharedServiceApi();

  return useSuspenseQuery({
    queryKey: FEATURES_REQUEST_KEYS.features(),
    queryFn: async () => {
      try {
        const { features } = await sharedApi.v1SharedFeaturesGet();

        return Object.entries(features).reduce<Features<AdminFeatureFlags>>(
          (acc, [name, { enabled }]) => ({ ...acc, [name]: enabled }),
          {}
        );
      } catch (error) {
        logger.error('Failed to load v1SharedFeaturesGet', error);

        throw error;
      }
    }
  });
}
