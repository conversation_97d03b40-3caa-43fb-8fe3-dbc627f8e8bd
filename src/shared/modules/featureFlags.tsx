import { createFeatureFlagsManager } from '@ot/feature-flags';

export const enum AdminFeatureFlag {
  AdminAtrAuditLog = 'Admin.ATR.AuditLog',
  AdminAtrInsights = 'Admin.ATR.Insights',
  AdminAtrFiltersByNameAndStatus = 'Admin.ATR.FiltersByNameAndStatus',
  AdminAtrFiltersByUsers = 'Admin.ATR.FiltersByUsers',
  AdminAtrFiltersByAtrGroup = 'Admin.ATR.FiltersByAtrGroup',
  AdminAtrFiltersByOrgUnit = 'Admin.ATR.FiltersByOrgUnit'

  // TODO: add feature flag for support admin
  // SupportAdminFeature = 'Admin.Support.Feature'
}

export type AdminFeatureFlags = `${AdminFeatureFlag}`;

export const {
  FeatureFlagsProvider,
  FeatureFlag,
  useFeatureFlag,
  withFeatureFlag
} = createFeatureFlagsManager<AdminFeatureFlags>();
