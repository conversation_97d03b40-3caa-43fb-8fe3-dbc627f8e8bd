import { useEffect, useMemo, useState } from 'react';

import { appConfig } from './appConfig';
import { getOneHubEnvironment } from './oneHubConfig';

export interface AppConfigPending {
  isPending: true;
  isError: false;
  config: undefined;
}

export interface AppConfigError {
  isPending: false;
  isError: true;
  config: undefined;
}

export interface AppConfigLoaded {
  isPending: false;
  isError: false;
  config: AppConfig;
}

export type AppConfigData = AppConfigPending | AppConfigError | AppConfigLoaded;

const UAT2_ENV_NAME = 'uat2';

let configURL: URL | null = null;
let envName = getOneHubEnvironment();

if (document.currentScript && 'src' in document.currentScript) {
  overrideEnvName(document.currentScript.src);

  const scriptURL = new URL(document.currentScript.src);
  configURL = new URL('../config.js', scriptURL);
}

const getConfig = (): AppConfig | undefined => {
  const moduleConfigName = process.env.MODULE_CONFIG_NAME;
  const configName = `${moduleConfigName}_${envName}`;

  const windowRecord = window as unknown as Record<string, unknown>;

  if (configName in windowRecord) {
    return windowRecord[configName] as AppConfig;
  }

  return undefined;
};

const configPromise: Promise<AppConfig | undefined> = new Promise(
  (resolve, reject) => {
    const tryResolveConfig = () => {
      const config = getConfig();
      if (config) {
        resolve(config);
      } else {
        reject(new Error('Config is not defined'));
      }
    };

    if (configURL) {
      const script = document.createElement('script');
      script.src = configURL.href;
      script.async = true;
      script.onload = () => {
        tryResolveConfig();
      };
      script.onerror = (error) => {
        reject(error);
      };
      document.head.appendChild(script);
    } else {
      tryResolveConfig();
    }
  }
);

configPromise.catch(() => void 0);

export function useConfig() {
  const [isPending, setIsPending] = useState(true);
  const [isError, setIsError] = useState(false);
  const [config, setConfig] = useState<AppConfig>(appConfig);

  useEffect(() => {
    configPromise
      .then((config) => {
        if (config) {
          setConfig(config);
        }
      })
      .catch(() => {
        if (process.env.NODE_ENV !== 'development') {
          setIsError(true);
        }
      })
      .then(() => {
        setIsPending(false);
      });
  }, []);

  return useMemo((): AppConfigData => {
    if (isPending) {
      return { isPending: true, isError: false, config: undefined };
    }

    if (isError) {
      return { isPending: false, isError: true, config: undefined };
    }

    return { isPending: false, isError: false, config };
  }, [isPending, isError, config]);
}

function overrideEnvName(url: string): void {
  if (isUat2Env(url)) {
    envName = UAT2_ENV_NAME;
  }
}

function isUat2Env(url: string): boolean {
  return getEnvOverrideFromUrl(url) === UAT2_ENV_NAME;
}

function getEnvOverrideFromUrl(url: string): string {
  const matches = url.match(/^https?:\/\/[^/]+\/([^/]+)\//);

  return matches ? matches[1] : '';
}
