{
  id: 65490283500050016,
  requestor: {
    employeeId: 99905178,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Marketing Manager,
    positionName: Manager, Marketing
  },
  giver: {
    employeeId: 99905182,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON><PERSON>,
    email: <PERSON><PERSON><PERSON><PERSON>@adnoc.ae,
    jobTitle: Product Manager,
    positionName: Manager, Product Development
  },
  receiver: {
    employeeId: 99905178,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Marketing Manager,
    positionName: Manager, Marketing
  },
  topic: {
    id: taskAndMilestone,
    name: Task / Milestone
  },
  subject: {
    title: Gather and analyze data on industry trends, competitor activities, and potential customer segments.,
    id: 33750282300060001,
    subType: task
  },
  rating: 4,
  requestedDate: 2025-01-01T00:00:00+00:00,
  requestDetails: Please provide feedback on my recent client presentation.,
  completedDate: 2025-01-09T00:00:00+00:00,
  feedbackText: Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!,
  isVisibleToManager: true,
  isVisibleToReceiver: true,
  isImportant: true,
  isProvidedByLineManager: true,
  isRequestedByManager: false,
  status: {
    id: completed,
    name: Completed
  }
}