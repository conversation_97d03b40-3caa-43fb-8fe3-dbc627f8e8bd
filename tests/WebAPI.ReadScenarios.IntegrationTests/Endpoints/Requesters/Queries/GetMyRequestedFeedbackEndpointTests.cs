using Microsoft.AspNetCore.Http;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class GetMyRequestedFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyRequestedFeedback_SuccessFlow_ReturnsFeedbackAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/feedback/65490283500050001");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }

    [Fact]
    public async Task GetMyRequestedFeedback_NonExistedId_ReturnsNotFoundAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/feedback/99900000090000999");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetMyRequestedFeedback_CurrentUserIsNotRequester_ReturnsNotFoundAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/feedback/65490283500050007");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetMyRequestedFeedback_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.MJohnsonToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Get.Url("/v1/requesters/me/feedback/65490283500050015");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }

    [Fact]
    public async Task GetMyRequestedFeedback_TaskAndMilestone_ReturnsFeedbackAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/feedback/65490283500050016");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .DontScrubDateTimes();
    }
}
