using Microsoft.AspNetCore.Http;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class SearchMyRequestedFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task SearchMyRequestedFeedback_SuccessFlow_ReturnsFeedbackPagedListResultAsync()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new { pageNumber = 2, pageSize = 3 })
                .ToUrl("/v2/requesters/me/feedback/search");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync()).DontScrubDateTimes();
    }

    [Fact]
    public async Task SearchMyRequestedFeedback_NotEmployee_ReturnsFeedbackOverviewAsync()
    {
        // Arrange
        SetAuthToken(Auth.NotEmployeeToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Post
                .Json(new { pageNumber = 2, pageSize = 3 })
                .ToUrl("/v2/requesters/me/feedback/search");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }

    [Fact]
    public async Task SearchMyRequestedFeedback_RequestedForSubordinate_ReturnsFeedbackPagedListResultAsync()
    {
        // Arrange
        SetAuthToken(Auth.ManagerToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new { pageNumber = 1, pageSize = 10, receiverId = "99905180" })
                .ToUrl("/v2/requesters/me/feedback/search");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync()).DontScrubDateTimes();
    }
}
