namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class SearchMyGiversEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task SearchMyGivers_SuccessFlow_ReturnsEmployeesAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    Search = "wilSon"
                })
                .ToUrl("/v1/requesters/me/givers/search");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task SearchMyGivers_WithChosen_ReturnsChosenAtTopAsync()
    {
        // Arrange
        string[] chosenIds = ["1003", "1004"];
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new
                {
                    Search = "test",
                    ChosenIds = chosenIds
                })
                .ToUrl("/v1/requesters/me/givers/search");
        });

        // Assert
        await Verify<PERSON><PERSON>(await response.ReadAsTextAsync());
    }
}
