using OneTalent.FeedbackService.Contracts.Constants;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class EnhanceEmployeeRequestFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetEnhancedRequestFeedback_SuccessFlow_ReturnsEnhancedFeedbackAsync()
    {

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                Topic = FeedbackTopics.General, 
                Subject = "Performance Review",
                Details = "Detailed explanation of the request.",
                Action = EnhanceActionType.ChangeToneConstructive
            } ).ToUrl("/v1/requesters/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await Verify<PERSON><PERSON>(actualObject);
    }

    [Fact]
    public async Task GetEnhancedFeedback_WhenFieldsAreInvalid_ReturnsBadRequestWithValidationDetailAsync()
    {

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
                Topic = FeedbackTopics.General,
                Details = "Detailed explanation of the request.",
                Action = EnhanceActionType.CustomPrompt,
                Prompt = new string('x',4001)
            }).ToUrl("/v1/requesters/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .IgnoreMember("traceId")
            .IgnoreMember("exceptionType");
    }
    
    [Fact]
    public async Task GetEnhancedFeedback_WhenNoAction_ReturnsBadRequestWithValidationDetailAsync()
    {

        var response = await Host.Scenario(api =>
        {
            api.Post.Json(new
            {
            }).ToUrl("/v1/requesters/me/feedback/ai/enhance-description");
            api.StatusCodeShouldBe(HttpStatusCode.BadRequest);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .IgnoreMember("traceId")
            .IgnoreMember("exceptionType");
    }
}
