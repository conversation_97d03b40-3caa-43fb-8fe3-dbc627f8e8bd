namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class GetMyRequestedTopicsEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyRequestedTopics_SuccessFlow_ReturnsTopicsAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/topics");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
