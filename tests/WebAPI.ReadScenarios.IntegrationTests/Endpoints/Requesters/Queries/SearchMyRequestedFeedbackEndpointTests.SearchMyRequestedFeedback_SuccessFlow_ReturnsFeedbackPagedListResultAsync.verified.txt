{
  items: [
    {
      id: 65490283500050016,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON><PERSON>,
        email: a<PERSON><PERSON><PERSON>@adnoc.ae,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2025-01-01T00:00:00+00:00,
      topic: {
        id: taskAndMilestone,
        name: Task / Milestone
      },
      subject: {
        title: Gather and analyze data on industry trends, competitor activities, and potential customer segments.,
        id: 33750282300060001,
        subType: task
      },
      status: {
        id: completed,
        name: Completed
      },
      rating: 4,
      requestDetails: Please provide feedback on my recent client presentation.,
      feedbackText: Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!
    },
    {
      id: 65490283500050001,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON><PERSON>,
        email: a<PERSON><PERSON><PERSON>@adnoc.ae,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2025-01-01T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Requesting Feedback on Recent Client Presentation
      },
      status: {
        id: completed,
        name: Completed
      },
      rating: 4,
      requestDetails: Please provide feedback on my recent client presentation.,
      feedbackText: Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!
    }
  ],
  paging: {
    pageNumber: 2,
    pageSize: 3,
    count: 2,
    totalResults: 5
  }
}