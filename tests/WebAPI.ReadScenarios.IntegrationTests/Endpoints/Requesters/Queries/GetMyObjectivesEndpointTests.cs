using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Requesters.Queries;

public class GetMyObjectivesEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyObjectives_SuccessFlow_ReturnsObjectivesAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/requesters/me/objectives");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
