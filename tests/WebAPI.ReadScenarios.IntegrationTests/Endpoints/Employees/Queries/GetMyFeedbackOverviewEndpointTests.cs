using Microsoft.AspNetCore.Http;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Employees.Queries;

public class GetMyFeedbackOverviewEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyFeedbackOverview_SuccessFlow_ReturnsFeedbackOverviewAsync()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/feedback-overview");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyFeedbackOverview_NotEmployee_Returns403ForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.NotEmployeeToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Get.Url("/v1/employees/me/feedback-overview");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }

    [Fact]
    public async Task GetMyFeedbackOverview_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.MJohnsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/feedback-overview");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }
}
