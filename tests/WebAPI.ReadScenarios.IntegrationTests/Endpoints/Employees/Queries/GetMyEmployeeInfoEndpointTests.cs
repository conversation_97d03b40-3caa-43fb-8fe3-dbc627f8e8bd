using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests.Extensions;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Employees.Queries;

public class GetMyEmployeeInfoEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyEmployeeInfo_ICUserInApril_ReturnsInfoAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/employee-info");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyEmployeeInfo_ICUserInNovember_ReturnsInfoAsync()
    {
        // Arrange
        // Act
        var response = await Host.Scenario(api =>
        {
            api.WithDateHeader(2024, 11, 01);
            api.Get.Url("/v1/employees/me/employee-info");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyEmployeeInfo_OSUserInApril_ReturnsInfoAsync()
    {
        // Arrange
        SetAuthToken(Auth.IMartinezToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/employee-info");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyEmployeeInfo_OSUserInNovember_ReturnsInfoAsync()
    {
        // Arrange
        SetAuthToken(Auth.IMartinezToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.WithDateHeader(2024, 11, 01);
            api.Get.Url("/v1/employees/me/employee-info");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetMyEmployeeInfo_UserNotEmployee_ForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.NotEmployeeToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/employee-info");
            api.StatusCodeShouldBe(HttpStatusCode.Forbidden);
        });
    }

    [Fact]
    public async Task GetMyEmployeeInfo_ProxyFlow_ReturnsInfoAsync()
    {
        // Arrange
        SetAuthToken(Auth.IMartinezToken);
        
        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/employees/me/employee-info");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
