{
  id: 65490283500050028,
  requestor: {
    employeeId: 99905179,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Data Analyst,
    positionName: Analyst, Data Science
  },
  giver: {
    employeeId: 99905181,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: IT Support Specialist,
    positionName: Specialist, IT Support
  },
  receiver: {
    employeeId: 99905179,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Data Analyst,
    positionName: Analyst, Data Science
  },
  topic: {
    id: general,
    name: General
  },
  subject: {
    title: Project Time Management
  },
  requestedDate: 2025-03-01T00:00:00+00:00,
  requestDetails: What's your feedback on how I presented the new project idea during the meeting?,
  completedDate: 2025-04-09T00:00:00+00:00,
  feedbackText: You explained the idea well and used clear examples, but adding a bit more detail on the implementation plan could make it even stronger.,
  isVisibleToManager: false,
  isVisibleToReceiver: true,
  isImportant: true,
  isProvidedByLineManager: false,
  isRequestedByManager: false,
  status: {
    id: completed,
    name: Completed
  }
}