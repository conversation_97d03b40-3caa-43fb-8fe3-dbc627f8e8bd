{
  items: [
    {
      id: 65490283500050029,
      employee: {
        employeeId: 99905164,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Finance Manager,
        positionName: Manager, Finance Operations
      },
      date: DateTimeOffset_1,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Conflict Resolution
      },
      status: {
        id: completed,
        name: Completed
      },
      feedbackText: You managed it well by staying calm and ensuring everyone had a chance to speak
    },
    {
      id: 65490283500050028,
      employee: {
        employeeId: 99905181,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: IT Support Specialist,
        positionName: Specialist, IT Support
      },
      date: DateTimeOffset_2,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Project Time Management
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: What's your feedback on how I presented the new project idea during the meeting?,
      feedbackText: You explained the idea well and used clear examples, but adding a bit more detail on the implementation plan could make it even stronger.
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 2,
    count: 2,
    totalResults: 2
  }
}