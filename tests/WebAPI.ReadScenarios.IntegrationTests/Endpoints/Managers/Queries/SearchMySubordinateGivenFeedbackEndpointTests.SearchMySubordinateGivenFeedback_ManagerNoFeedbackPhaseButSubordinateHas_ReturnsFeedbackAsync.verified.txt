{
  items: [
    {
      id: 65490283500050023,
      employee: {
        employeeId: 99905163,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Supply Chain Analyst,
        positionName: Analyst, Supply Chain
      },
      date: 2025-05-09T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Conflict Resolution
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: Could I get your feedback on the way I handled conflict during the team discussion last week?,
      feedbackText: You showed excellent conflict resolution skills by staying calm and empathetic, which helped ease tensions.
    },
    {
      id: 65490283500050014,
      employee: {
        employeeId: 99905180,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Financial Analyst,
        positionName: Analyst, Financial Planning
      },
      date: 2025-04-10T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Pricing Negotiation in Recent Deal
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: Can you share your feedback on how I handled the pricing negotiations in the recent deal?,
      feedbackText: You approached the pricing negotiations confidently and showcased a solid understanding of the value proposition, which helped build trust with the client.
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 10,
    count: 2,
    totalResults: 2
  }
}