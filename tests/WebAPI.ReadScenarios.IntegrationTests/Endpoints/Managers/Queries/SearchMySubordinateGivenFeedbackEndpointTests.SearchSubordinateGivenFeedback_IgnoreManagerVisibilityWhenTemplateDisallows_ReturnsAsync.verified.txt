{
  items: [
    {
      id: 65490283500050028,
      employee: {
        employeeId: 99905179,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Data Analyst,
        positionName: Analyst, Data Science
      },
      date: DateTimeOffset_1,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Project Time Management
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: What's your feedback on how I presented the new project idea during the meeting?,
      feedbackText: You explained the idea well and used clear examples, but adding a bit more detail on the implementation plan could make it even stronger.
    },
    {
      id: 65490283500050013,
      employee: {
        employeeId: 99905180,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Financial Analyst,
        positionName: Analyst, Financial Planning
      },
      date: DateTimeOffset_2,
      topic: {
        id: taskAndMilestone,
        name: Task / Milestone
      },
      subject: {
        title: Completion of Leadership Training Program,
        id: 33750282300060009,
        tags: [
          Deleted
        ],
        subType: milestone
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: I’d appreciate your thoughts on my approach to managing follow-up emails for prospects. Are my communications effective, or could they be improved?,
      feedbackText: You've done a great job tailoring the message to each prospect, which makes it feel personalized.
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 2,
    count: 2,
    totalResults: 2
  }
}