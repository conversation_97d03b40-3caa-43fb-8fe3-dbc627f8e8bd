{
  items: [
    {
      id: 65490283500050025,
      employee: {
        employeeId: 99905178,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: hwi<PERSON>@adnoc.ae,
        jobTitle: Marketing Manager,
        positionName: Manager, Marketing
      },
      date: 2025-04-10T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Feedback request
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: I’d love to hear your feedback on how my subordinate handled pricing negotiations during the recent deal.,
      feedbackText: He handled the pricing discussions with confidence and agility, demonstrating strong negotiation skills.,
      tags: [
        Requested By You
      ]
    },
    {
      id: 65490283500050014,
      employee: {
        employeeId: 99905154,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Market Research Analyst,
        positionName: Analyst, Market Research & Strategy
      },
      date: 2025-04-10T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Pricing Negotiation in Recent Deal
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: Can you share your feedback on how I handled the pricing negotiations in the recent deal?,
      feedbackText: You approached the pricing negotiations confidently and showcased a solid understanding of the value proposition, which helped build trust with the client.
    },
    {
      id: 65490283500050024,
      employee: {
        employeeId: 99905179,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: Ivy Martinez,
        email: <EMAIL>,
        jobTitle: Data Analyst,
        positionName: Analyst, Data Science
      },
      date: 2025-03-09T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Feedback request
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: I'd appreciate your thoughts on how my subordinate managed the recent client issue related to delivery schedule concerns.,
      feedbackText: He tackled the client’s delivery timeline concern with efficiency and presented actionable solutions.,
      tags: [
        Requested By You
      ]
    },
    {
      id: 65490283500050013,
      employee: {
        employeeId: 99905181,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: Ethan Brown,
        email: <EMAIL>,
        jobTitle: IT Support Specialist,
        positionName: Specialist, IT Support
      },
      date: 2025-02-24T00:00:00+00:00,
      topic: {
        id: taskAndMilestone,
        name: Task / Milestone
      },
      subject: {
        title: Completion of Leadership Training Program,
        id: 33750282300060009,
        tags: [
          Deleted
        ],
        subType: milestone
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: I’d appreciate your thoughts on my approach to managing follow-up emails for prospects. Are my communications effective, or could they be improved?,
      feedbackText: You've done a great job tailoring the message to each prospect, which makes it feel personalized.
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 10,
    count: 4,
    totalResults: 4
  }
}