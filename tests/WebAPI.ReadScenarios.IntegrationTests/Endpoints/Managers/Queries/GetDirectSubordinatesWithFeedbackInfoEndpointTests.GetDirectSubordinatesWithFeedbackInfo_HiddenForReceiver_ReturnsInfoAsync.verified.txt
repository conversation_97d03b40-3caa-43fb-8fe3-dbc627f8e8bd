{
  items: [
    {
      employeeId: 99905180,
      companyCode: 1100,
      companyName: ADNOC HQ,
      fullNameEnglish: <PERSON>,
      email: <EMAIL>,
      jobTitle: Financial Analyst,
      positionName: Analyst, Financial Planning,
      feedbackInfo: {
        givenCount: 4,
        receivedCount: 4
      }
    },
    {
      employeeId: 99905181,
      companyCode: 1100,
      companyName: ADNOC HQ,
      fullNameEnglish: <PERSON>,
      email: <EMAIL>,
      jobTitle: IT Support Specialist,
      positionName: Specialist, IT Support,
      feedbackInfo: {
        givenCount: 2,
        receivedCount: 1
      }
    },
    {
      employeeId: 99905178,
      companyCode: 1100,
      companyName: ADNOC HQ,
      fullNameEnglish: <PERSON>,
      email: <EMAIL>,
      jobTitle: Marketing Manager,
      positionName: Manager, Marketing,
      feedbackInfo: {
        givenCount: 5,
        receivedCount: 5
      }
    },
    {
      employeeId: 99905179,
      companyCode: 1100,
      companyName: ADNOC HQ,
      fullNameEnglish: <PERSON>,
      email: <EMAIL>,
      jobTitle: Data Analyst,
      positionName: Analyst, Data Science,
      feedbackInfo: {
        givenCount: 1,
        receivedCount: 2
      }
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 10,
    count: 4,
    totalResults: 4
  }
}