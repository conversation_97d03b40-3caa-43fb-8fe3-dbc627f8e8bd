using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Receivers.Queries;

public class GetMyReceivedFeedbackEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetMyReceivedFeedback_SuccessFlow_ReturnsFeedbackAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/65490283500050002");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject);
    }
    
    [Fact]
    public async Task GetMyReceivedFeedback_NotVisibleToReceiver_ReturnsNotFoundAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/65490283500050003");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });
        
        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetMyReceivedFeedback_NonExistedId_ReturnsNotFoundAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/77700770077000977");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetMyReceivedFeedback_CurrentUserIsNotReceiver_ReturnsNotFoundAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/65490283500050008");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }

    [Fact]
    public async Task GetMyReceivedFeedback_TaskAndMilestone_ReturnsFeedbackAsync()
    {
        SetAuthToken(Auth.FMillerToken);

        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/65490283500050017");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .DontScrubDateTimes();
    }

    [Fact]
    public async Task GetMyReceivedFeedback_HiddenForReceiver_ReturnsNotFoundAsync()
    {
        // Arrange
        SetAuthToken(Auth.EBrownToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/receivers/me/feedback/65490283500050026");
            api.StatusCodeShouldBe(HttpStatusCode.NotFound);
        });

        // Assert
        var actualObject = await response.ReadAsTextAsync();

        await VerifyJson(actualObject)
            .AsProblemDetails();
    }
}
