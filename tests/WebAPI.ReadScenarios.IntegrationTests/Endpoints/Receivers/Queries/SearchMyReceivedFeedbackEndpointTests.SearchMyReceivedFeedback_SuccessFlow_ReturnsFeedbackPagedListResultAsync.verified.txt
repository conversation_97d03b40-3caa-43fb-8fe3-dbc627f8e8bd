{
  items: [
    {
      id: 65490283500050005,
      employee: {
        employeeId: 99905180,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON>,
        email: <EMAIL>,
        jobTitle: Financial Analyst,
        positionName: Analyst, Financial Planning
      },
      date: 2025-03-20T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Requesting Feedback on Managing Follow-Ups with Leads
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: Please provide feedback on how I address follow-ups with leads.,
      feedbackText: Your follow-up process is thorough and shows great persistence. It’s excellent for building trust with leads.
    },
    {
      id: 65490283500050002,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: <PERSON><PERSON>,
        email: ajo<PERSON><PERSON>@adnoc.ae,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2025-02-22T00:00:00+00:00,
      topic: {
        id: objective,
        name: Objective
      },
      subject: {
        title: Enhance Customer Service Skills,
        id: 43750282300060001,
        description: Focus on improving communication, responsiveness, and problem-solving skills to better address customer inquiries and issues.
      },
      status: {
        id: completed,
        name: Completed
      },
      requestDetails: Could you share how my coordination went for the feature release?,
      feedbackText: Your sales numbers exceeded expectations this quarter. Keep up the consistent performance!
    },
    {
      id: 65490283500050016,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: Alston Johnson,
        email: <EMAIL>,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2025-01-09T00:00:00+00:00,
      topic: {
        id: taskAndMilestone,
        name: Task / Milestone
      },
      subject: {
        title: Gather and analyze data on industry trends, competitor activities, and potential customer segments.,
        id: 33750282300060001,
        subType: task
      },
      status: {
        id: completed,
        name: Completed
      },
      rating: 4,
      requestDetails: Please provide feedback on my recent client presentation.,
      feedbackText: Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!
    },
    {
      id: 65490283500050001,
      employee: {
        employeeId: 99905182,
        companyCode: 1100,
        companyName: ADNOC HQ,
        fullNameEnglish: Alston Johnson,
        email: <EMAIL>,
        jobTitle: Product Manager,
        positionName: Manager, Product Development
      },
      date: 2025-01-09T00:00:00+00:00,
      topic: {
        id: general,
        name: General
      },
      subject: {
        title: Requesting Feedback on Recent Client Presentation
      },
      status: {
        id: completed,
        name: Completed
      },
      rating: 4,
      requestDetails: Please provide feedback on my recent client presentation.,
      feedbackText: Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!
    }
  ],
  paging: {
    pageNumber: 1,
    pageSize: 5,
    count: 4,
    totalResults: 4
  }
}