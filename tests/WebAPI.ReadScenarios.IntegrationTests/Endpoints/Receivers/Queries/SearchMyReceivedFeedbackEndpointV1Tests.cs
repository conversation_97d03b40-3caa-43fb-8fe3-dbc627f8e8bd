using Microsoft.AspNetCore.Http;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Receivers.Queries;

public class SearchMyReceivedFeedbackEndpointV1Tests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task SearchMyReceivedFeedback_SuccessFlow_ReturnsFeedbackPagedListResultAsync()
    {
        // Arrange
        SetAuthToken(Auth.HWilsonToken);

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Post
                .Json(new { pageNumber = 1, pageSize = 5 })
                .ToUrl("/v1/receivers/me/feedback/search");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync()).DontScrubDateTimes();
    }

    [Fact]
    public async Task SearchMyReceivedFeedback_NotEmployee_ReturnsFeedbackOverviewAsync()
    {
        // Arrange
        SetAuthToken(Auth.NotEmployeeToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Post
                .Json(new { pageNumber = 1, pageSize = 10 })
                .ToUrl("/v1/receivers/me/feedback/search");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }

    [Fact]
    public async Task SearchMyReceivedFeedback_UserDoesNotHaveFeedbackPhase_ReturnsForbiddenAsync()
    {
        // Arrange
        SetAuthToken(Auth.MJohnsonToken);

        var response = await Host.Scenario(api =>
        {
            // Act
            api.Post
                .Json(new { pageNumber = 1, pageSize = 10 })
                .ToUrl("/v1/receivers/me/feedback/search");

            // Assert
            api.StatusCodeShouldBe(StatusCodes.Status403Forbidden);
        });
    }
}
