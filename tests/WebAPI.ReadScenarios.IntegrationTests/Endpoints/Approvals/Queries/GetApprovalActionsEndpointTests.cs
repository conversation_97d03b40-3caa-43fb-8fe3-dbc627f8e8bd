namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Approvals.Queries;

public class GetApprovalActionsEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetApprovalActions_FeedbackStatusIsPending_ReturnsActions()
    {
        // Arrange
        var requestId = "4";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}/actions");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetApprovalActions_FeedbackStatusIsCompleted_ReturnsEmptyArray()
    {
        // Arrange
        var requestId = "1";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}/actions");
        });

        // Assert
        await Verify<PERSON>son(await response.ReadAsTextAsync());
    }
}
