namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Approvals.Queries;

public class GetApprovalFeedbackRequestsEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetApprovalFeedbackRequests_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var requestId = "1";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetApprovalFeedbackWithTaskAndMilstoneSubjectRequests_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var requestId = "3";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}");
        });

        // Assert
        await Verify<PERSON>son(await response.ReadAsTextAsync());
    }

    [Fact]
    public async Task GetApprovalFeedbackWithObjectiveRequests_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var requestId = "2";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
