namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Approvals.Queries;

public class GetApprovalChainEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetApprovalChain_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var requestId = "1";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}/approval-chain");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
