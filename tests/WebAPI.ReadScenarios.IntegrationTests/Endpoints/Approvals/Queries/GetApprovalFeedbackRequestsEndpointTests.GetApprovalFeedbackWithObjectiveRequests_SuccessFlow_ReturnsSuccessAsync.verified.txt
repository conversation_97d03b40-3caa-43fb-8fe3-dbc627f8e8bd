{
  id: 65490283500050002,
  requestor: {
    employeeId: 99905178,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Marketing Manager,
    positionName: Manager, Marketing
  },
  giver: {
    employeeId: 99905182,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON><PERSON>,
    email: <PERSON><PERSON><PERSON><PERSON>@adnoc.ae,
    jobTitle: Product Manager,
    positionName: Manager, Product Development
  },
  receiver: {
    employeeId: 99905178,
    companyCode: 1100,
    companyName: ADNOC HQ,
    fullNameEnglish: <PERSON>,
    email: <EMAIL>,
    jobTitle: Marketing Manager,
    positionName: Manager, Marketing
  },
  topic: {
    id: objective,
    name: Objective
  },
  subject: {
    title: Enhance Customer Service Skills,
    id: 43750282300060001,
    description: Focus on improving communication, responsiveness, and problem-solving skills to better address customer inquiries and issues.
  },
  requestedDate: DateTimeOffset_1,
  requestDetails: Could you share how my coordination went for the feature release?,
  completedDate: DateTimeOffset_2,
  feedbackText: Your sales numbers exceeded expectations this quarter. Keep up the consistent performance!,
  isVisibleToManager: true,
  isVisibleToReceiver: true,
  isImportant: true,
  isProvidedByLineManager: true,
  isRequestedByManager: false,
  status: {
    id: completed,
    name: Completed
  }
}