namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Approvals.Queries;

public class GetApprovalHistoryEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetApprovalHistory_SuccessFlow_ReturnsSuccessAsync()
    {
        // Arrange
        var requestId = "1";

        // Act
        var response = await Host.Scenario(api =>
        {
            api.Get.Url($"/v1/approvals/{requestId}/history");
        });

        // Assert
        await VerifyJson(await response.ReadAsTextAsync());
    }
}
