using System.Net;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.Settings.Queries;

public class GetAtrCyclesEndpointTests(WebAppFixture fixture) : WebAppContext(fixture)
{
    [Fact]
    public async Task GetAtrCycles_SuccessFlow_ReturnsCyclesAsync()
    {
        var response = await Host.Scenario(api =>
        {
            api.Get.Url("/v1/settings/atr/cycles");
            api.StatusCodeShouldBe(HttpStatusCode.OK);
        });

        var actualObject = await response.ReadAsTextAsync();

        await Verify<PERSON>son(actualObject);
    }
}
