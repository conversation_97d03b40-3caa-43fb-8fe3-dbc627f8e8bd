using FluentAssertions;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.Contracts.v1.Documents;
using OneTalent.FeedbackService.Contracts.v1.Interfaces;
using OneTalent.FeedbackService.Contracts.v1.Queries;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.ExposureService.Feedback;

public abstract class FeedbackSearchV1Tests(IExposureTestingContext context)
{
    [Fact]
    public async Task FeedbackSearchAsync_Success_ReturnFeedbackAsync()
    {
        var response = await context.GetSubject<IFeedbackSearchV1>().SearchAsync(
            new FeedbackSearchQueryV1(new ItemId(65490283500050001)),
            CancellationToken.None
        ).ToArrayAsync(cancellationToken: TestContext.Current.CancellationToken);

        response.Should().BeAssignableTo<FeedbackV1[]>();
        response.Should().HaveCount(1);
    }
    
    [Fact]
    public async Task FeedbackSearchAsync_WithTJFilter_ReturnFeedbackAsync()
    {
        var response = await context.GetSubject<IFeedbackSearchV1>().SearchAsync(
            new FeedbackSearchQueryV1(
                ReceiverId: 99905180,
                Statuses: [FeedbackStatuses.Completed]
            ), CancellationToken.None
        ).ToArrayAsync(TestContext.Current.CancellationToken);

        await Verify(response);
    }
  
    [Fact]
    public async Task FeedbackSearchAsync_HiddenForReceiver_ReturnsEmptyAsync()
    {
        // Arrange
        const int subordinateId = 99905181;
        const long feedbackId = 65490283500050026;

        // Act
        var response = await context.GetSubject<IFeedbackSearchV1>().SearchAsync(
            new FeedbackSearchQueryV1(
                Id: feedbackId,
                CurrentUserId: subordinateId,
                ReceiverId: subordinateId,
                Statuses: [FeedbackStatuses.Completed]
            ), CancellationToken.None
        ).ToArrayAsync(TestContext.Current.CancellationToken);

        // Assert
        await Verify(response);
    }

    [Fact]
    public async Task FeedbackSearchAsync_NotVisibleToManager_ReturnsEmptyAsync()
    {
        // Arrange
        const int managerId = 99905182;
        const int subordinateId = 99905154;
        const long feedbackId = 65490283500050022;

        // Act
        var response = await context.GetSubject<IFeedbackSearchV1>().SearchAsync(
            new FeedbackSearchQueryV1(
                Id: feedbackId,
                CurrentUserId: managerId,
                ReceiverId: subordinateId,
                Statuses: [FeedbackStatuses.Completed]
            ), CancellationToken.None
        ).ToArrayAsync(TestContext.Current.CancellationToken);

        // Assert
        await Verify(response);
    }
    
    [Fact]
    public async Task FeedbackSearchAsync_EmployeeNotAllowedToChangeVisibility_ReturnsAsync()
    {
        // Arrange
        const int managerId = 99905182;
        const int subordinateId = 99905179;

        // Act
        var response = await context.GetSubject<IFeedbackSearchV1>().SearchAsync(
            new FeedbackSearchQueryV1(
                CurrentUserId: managerId,
                ReceiverId: subordinateId,
                Statuses: [FeedbackStatuses.Completed]
            ), CancellationToken.None
        ).ToArrayAsync(TestContext.Current.CancellationToken);

        // Assert
        await Verify(response);
    }

    [Collection(nameof(WebAppCollection))]
    public class Service(WebAppFixture fixture) : FeedbackSearchV1Tests(IExposureTestingContext.CreateService(fixture));

    [Collection(nameof(WebAppCollection))]
    public class Client(WebAppFixture fixture) : FeedbackSearchV1Tests(IExposureTestingContext.CreateClient(fixture));
}

