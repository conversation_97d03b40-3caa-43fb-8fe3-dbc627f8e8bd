using FluentAssertions;
using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Contracts.v1.Documents;
using OneTalent.FeedbackService.Contracts.v1.Interfaces;
using OneTalent.FeedbackService.Contracts.v1.Queries;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.ExposureService.Feedback;

public abstract class EmployeeInfoClientV1Tests(IExposureTestingContext context)
{
    [Fact]
    public async Task EmployeeGetPermissionsAsync_UserIsNotEmployee_ReturnEmptyRights()
    {
        var response = await context.GetSubject<IEmployeeInfoV1>().GetPermissionsByEmployeeIdAsync(
            new ItemId(65490283500050001),
            CancellationToken.None
        );

        response.Should().BeAssignableTo<EmployeePermissionsV1>();
        response.Rights.Should().BeEmpty();
    }

    [Fact]
    public async Task EmployeeGetPermissionsAsync_UserIsEmployee_ReturnRights()
    {
        var response = await context.GetSubject<IEmployeeInfoV1>().GetPermissionsByEmployeeIdAsync(
            new ItemId(1001),
            CancellationToken.None
        );

        response.Should().BeAssignableTo<EmployeePermissionsV1>();
        response.Rights.Should().HaveCountGreaterThan(0);
    }

    [Collection(nameof(WebAppCollection))]
    public class Service(WebAppFixture fixture) : EmployeeInfoClientV1Tests(IExposureTestingContext.CreateService(fixture));

    [Collection(nameof(WebAppCollection))]
    public class Client(WebAppFixture fixture) : EmployeeInfoClientV1Tests(IExposureTestingContext.CreateClient(fixture));
}

