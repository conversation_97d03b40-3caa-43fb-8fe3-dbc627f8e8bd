using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OneTalent.FeedbackService.WebAPI.Client.Extensions;
using OneTalent.FeedbackService.WebAPI.Common.IntegrationTests;
using System.Net.Http.Headers;

namespace OneTalent.FeedbackService.WebAPI.ReadScenarios.IntegrationTests.Endpoints.ExposureService.Feedback;

public abstract class IExposureTestingContext
{
    private IServiceProvider? ServiceProvider { get; init; }
    private IServiceScope? ServiceScope { get; init; }

    IExposureTestingContext() { }

    public static IExposureTestingContext CreateClient(WebAppFixture fixture) => new ClientContext(fixture);
    public static IExposureTestingContext CreateService(WebAppFixture fixture) => new ServiceContext(fixture);
    public T GetSubject<T>() where T : class
    {
        return ServiceScope?.ServiceProvider.GetRequiredService<T>()
               ?? throw new InvalidOperationException("Service Scope not initialized");
    }

    private class ClientContext : IExposureTestingContext
    {
        public ClientContext(WebAppFixture fixture)
        {
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.IntegrationTest-ReadScenarios.json", false, true)
                .AddEnvironmentVariables()
                .Build();

            fixture.Services
                .AddSingleton<IConfiguration>(configuration)
                .RegisterFeedbackServiceClient(CreateHttpClient(fixture));

            ServiceProvider = fixture.Services.BuildServiceProvider();
            ServiceScope = ServiceProvider.GetRequiredService<IServiceScopeFactory>().CreateScope();
        }

        private static HttpClient CreateHttpClient(WebAppFixture fixture)
        {
            var httpClient = fixture.AlbaHost.Server.CreateClient();
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", Auth.EBrownToken);
            return httpClient;
        }
    }

    private class ServiceContext : IExposureTestingContext
    {
        public ServiceContext(WebAppFixture fixture)
        {
            ServiceProvider = fixture.AlbaHost.Services;
            ServiceScope = ServiceProvider.GetRequiredService<IServiceScopeFactory>().CreateScope();
        }
    }
}
