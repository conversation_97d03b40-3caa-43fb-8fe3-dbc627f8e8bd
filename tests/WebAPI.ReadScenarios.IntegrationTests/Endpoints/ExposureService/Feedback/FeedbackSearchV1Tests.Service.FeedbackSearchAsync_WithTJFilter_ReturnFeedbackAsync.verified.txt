[
  {
    Id: {},
    GiverId: {},
    ReceiverId: {},
    RequestorId: {},
    AtrCycleId: {},
    TaskAndMilestoneId: {},
    TopicId: TaskAndMilestone,
    RequestedDate: DateTimeOffset_1,
    RequestDetails: I’d appreciate your thoughts on my approach to managing follow-up emails for prospects. Are my communications effective, or could they be improved?,
    CompletedDate: DateTimeOffset_2,
    FeedbackText: You've done a great job tailoring the message to each prospect, which makes it feel personalized.,
    IsVisibleToManager: true,
    IsVisibleToReceiver: true,
    IsImportant: false,
    IsProvidedByLineManager: false,
    IsRequestedByManager: false,
    StatusId: Completed,
    CreatedById: {},
    LastModifiedById: {},
    LastModifiedDate: DateTimeOffset_3,
    CreatedDate: DateTimeOffset_3
  },
  {
    Id: {},
    GiverId: {},
    ReceiverId: {},
    RequestorId: {},
    AtrCycleId: {},
    TopicId: General,
    Subject: Pricing Negotiation in Recent Deal,
    RequestedDate: DateTimeOffset_4,
    RequestDetails: Can you share your feedback on how I handled the pricing negotiations in the recent deal?,
    CompletedDate: DateTimeOffset_5,
    FeedbackText: You approached the pricing negotiations confidently and showcased a solid understanding of the value proposition, which helped build trust with the client.,
    IsVisibleToManager: true,
    IsVisibleToReceiver: true,
    IsImportant: false,
    IsProvidedByLineManager: false,
    IsRequestedByManager: false,
    StatusId: Completed,
    CreatedById: {},
    LastModifiedById: {},
    LastModifiedDate: DateTimeOffset_3,
    CreatedDate: DateTimeOffset_3
  },
  {
    Id: {},
    GiverId: {},
    ReceiverId: {},
    RequestorId: {},
    AtrCycleId: {},
    TopicId: General,
    Subject: Feedback request,
    RequestedDate: DateTimeOffset_6,
    RequestDetails: I'd appreciate your thoughts on how my subordinate managed the recent client issue related to delivery schedule concerns.,
    CompletedDate: DateTimeOffset_7,
    FeedbackText: He tackled the client’s delivery timeline concern with efficiency and presented actionable solutions.,
    IsVisibleToManager: true,
    IsVisibleToReceiver: false,
    IsImportant: false,
    IsProvidedByLineManager: false,
    IsRequestedByManager: true,
    StatusId: Completed,
    CreatedById: {},
    LastModifiedById: {},
    LastModifiedDate: DateTimeOffset_3,
    CreatedDate: DateTimeOffset_3
  },
  {
    Id: {},
    GiverId: {},
    ReceiverId: {},
    RequestorId: {},
    AtrCycleId: {},
    TopicId: General,
    Subject: Feedback request,
    RequestedDate: DateTimeOffset_4,
    RequestDetails: I’d love to hear your feedback on how my subordinate handled pricing negotiations during the recent deal.,
    CompletedDate: DateTimeOffset_5,
    FeedbackText: He handled the pricing discussions with confidence and agility, demonstrating strong negotiation skills.,
    IsVisibleToManager: true,
    IsVisibleToReceiver: false,
    IsImportant: true,
    IsProvidedByLineManager: false,
    IsRequestedByManager: true,
    StatusId: Completed,
    CreatedById: {},
    LastModifiedById: {},
    LastModifiedDate: DateTimeOffset_3,
    CreatedDate: DateTimeOffset_3
  }
]