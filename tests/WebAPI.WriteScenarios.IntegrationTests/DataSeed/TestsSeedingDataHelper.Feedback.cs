using OneTalent.Common.Extensions.ItemId;
using OneTalent.FeedbackService.Contracts.Constants;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer;
using OneTalent.FeedbackService.Infrastructure.DataAccessLayer.Entities.Feedback;

namespace OneTalent.FeedbackService.WebAPI.WriteScenarios.IntegrationTests.DataSeed;

internal static partial class TestsSeedingDataHelper
{
    internal class Feedback
    {
        internal static readonly List<FeedbackEntity> FeedbackEntities =
        [
            // For <NAME_EMAIL>
            new()
            {
                Id = 65490283500050001,
                GiverId = 99905182,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 04, 01), TimeSpan.Zero),
                RequestDetails = "Please provide feedback on my recent client presentation.",
                FeedbackText = "Your presentation was clear and persuasive, effectively addressing the client’s key concerns. Great work!",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 04, 09), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Recent Client Presentation",
                StatusId = FeedbackStatuses.Completed,
                Rating = 4,
                CreatedById = 99905178,
                LastModifiedById = 99905182
            },
            new()
            {
                Id = 65490283500050002,
                GiverId = 99905180,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 01, 01), TimeSpan.Zero),
                RequestDetails = "Could you share how my coordination went for the feature release?",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 01, 22), TimeSpan.Zero),
                FeedbackText = "Your sales numbers exceeded expectations this quarter. Keep up the consistent performance!",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Feature Release Coordination Efforts",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905178,
                LastModifiedById = 99905180
            },
            new()
            {
                Id = 65490283500050003,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050004,
                GiverId = 99905182,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 03, 17), TimeSpan.Zero),
                RequestDetails = "Could you provide feedback on my product demonstration skills?",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 04, 01), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = false,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Product Demonstration Skills",
                StatusId = FeedbackStatuses.Pending,
                DeclineReasonId = FeedbackDeclineReasons.UnclearExpectations,
                DeclineComment = "If you could provide more context—such as aspects like clarity, engagement, " +
                    "or technical accuracy—I’d be happy to offer more detailed and constructive insights.",
                CreatedById = 99905178,
                LastModifiedById = 99905183
            },
            new()
            {
                Id = 65490283500050005,
                GiverId = 99905184,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 03, 15), TimeSpan.Zero),
                RequestDetails = "Please provide feedback on how I address follow-ups with leads.",
                FeedbackText = "Your follow-up process is thorough and shows great persistence. It’s excellent for building trust with leads.",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 03, 10), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = false,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Managing Follow-Ups with Leads",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905178,
                LastModifiedById = 99905184
            },
            new()
            {
                Id = 65490283500050006,
                GiverId = 99905182,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 03, 15), TimeSpan.Zero),
                RequestDetails = "Please provide feedback on how I solved issue",
                FeedbackText = "It’s excellent that you managed to solve this issue.",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 03, 10), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905178,
                LastModifiedById = 99905182
            },
            new()
            {
                Id = 65490283500050007,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 20), TimeSpan.Zero),
                RequestDetails = "Please provide feedback on my recent presentation.",
                FeedbackText = "Your presentation was great. Great work!",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 03, 15), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                IsRequestedByManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905182,
                LastModifiedById = 99905181
            },

            // For <NAME_EMAIL>
            new()
            {
                Id = 65490283500050101,
                GiverId = 99905201,
                ReceiverId = 99905182,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2026, 04, 06), TimeSpan.Zero),
                RequestDetails = "Can you share feedback on my performance during the last week?",
                FeedbackText = "Your delivery was confident, and you highlighted key points that resonated with the client. Great job maintaining engagement!",
                CompletedDate = new DateTimeOffset(new DateTime(2026, 04, 10), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Last Week's Performance",
                StatusId = FeedbackStatuses.Completed,
                Rating = 4,
                CreatedById = 99905182,
                LastModifiedById = 99905201
            },
            new()
            {
                Id = 65490283500050102,
                GiverId = 99905201,
                ReceiverId = 99905182,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 01, 01), TimeSpan.Zero),
                RequestDetails = "Could you provide feedback on my handling of upselling opportunities with existing clients?",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 01, 15), TimeSpan.Zero),
                FeedbackText = "You did a great job identifying upselling opportunities, but focusing more on client goals could make your pitches even stronger.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = true,
                TopicId = FeedbackTopics.General,
                Subject = "Seeking Feedback on Upselling Strategies with Existing Clients",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905182,
                LastModifiedById = 99905201
            },
            new()
            {
                Id = 65490283500050103,
                GiverId = 99905151,
                ReceiverId = 99905182,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 12), TimeSpan.Zero),
                RequestDetails = "Please provide feedback on how I negotiated pricing during the recent deal.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Please provide feedback on how I negotiated pricing during the recent deal.",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905182,
                LastModifiedById = 99905151
            },
            new()
            {
                Id = 65490283500050104,
                GiverId = 99905152,
                ReceiverId = 99905182,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 03, 02), TimeSpan.Zero),
                RequestDetails = "Can you review my performance in managing follow-up emails to prospects?",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 03, 24), TimeSpan.Zero),
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = false,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Feedback on Follow-Up Email Management with Prospects",
                StatusId = FeedbackStatuses.Declined,
                DeclineReasonId = FeedbackDeclineReasons.UnclearExpectations,
                DeclineComment = "If you can specify the aspects you'd like assessed, such as timing, tone, or structure, I'd be happy to provide more targeted feedback.",
                CreatedById = 99905182,
                LastModifiedById = 99905152
            },
            new()
            {
                Id = 65490283500050105,
                GiverId = 99905153,
                ReceiverId = 99905182,
                RequestorId = 99905182,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 03, 07), TimeSpan.Zero),
                RequestDetails = "Please share feedback on how I resolved the recent client concern about delivery timelines.",
                FeedbackText = "You addressed the client concern promptly and offered practical solutions, which helped rebuild their trust.",
                CompletedDate = new DateTimeOffset(new DateTime(2025, 03, 22), TimeSpan.Zero),
                Subject = "Feedback on Resolving Client Concern Regarding Delivery Timelines",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = false,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905182,
                LastModifiedById = 99905153

            },
            new()
            {
                Id = 65490283500050106,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050107,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050108,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050109,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Pending,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050110,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Withdrawn,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050111,
                GiverId = 99905181,
                ReceiverId = 99905178,
                RequestorId = 99905178,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Withdrawn,
                CreatedById = 99905178,
                LastModifiedById = 99905181
            },
            new()
            {
                Id = 65490283500050112,
                GiverId = 99905178,
                ReceiverId = 99905179,
                RequestorId = 99905179,
                AtrCycleId = 1,
                RequestedDate = new DateTimeOffset(new DateTime(2025, 02, 11), TimeSpan.Zero),
                RequestDetails = "I would appreciate feedback on how I handled customer objections.",
                IsVisibleToManager = true,
                IsVisibleToReceiver = true,
                IsImportant = true,
                IsProvidedByLineManager = false,
                TopicId = FeedbackTopics.General,
                Subject = "Requesting Feedback on Handling Customer Objections",
                StatusId = FeedbackStatuses.Completed,
                CreatedById = 99905179,
                LastModifiedById = 99905181
            }
        ];

        internal static readonly List<FeedbackToApprovalsEntity> FeedbackToApprovalsEntities =
        [
            new(65490283500050106, "1", Guid.CreateVersion7(), FeedbackStatuses.Pending),
            new(65490283500050107, "2", Guid.CreateVersion7(), FeedbackStatuses.Pending),
            new(65490283500050108, "3", Guid.CreateVersion7(), FeedbackStatuses.Pending),
            new(65490283500050111, "4", Guid.CreateVersion7(), FeedbackStatuses.Pending),
        ];
    }

    internal static void SeedFeedbackData(FeedbackDbContext dbContext)
    {
        dbContext.Feedback.AddRange(Feedback.FeedbackEntities);
        dbContext.FeedbackToApprovals.AddRange(Feedback.FeedbackToApprovalsEntities);

        dbContext.SaveChanges();
    }
}
