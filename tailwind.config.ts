import { Config } from 'tailwindcss';
import plugin from 'tailwindcss/plugin';

import { DARK_THEME_NAME, theme } from './tailwind';

export default {
  content: ['./lib/**/*.{js,jsx,ts,tsx}', './.storybook/**/*.{js,jsx,ts,tsx}'],
  safelist: [],
  theme: {
    extend: {
      boxShadow: {
        ...theme?.boxShadow
      },
      colors: {
        ...theme?.colors
      },
      screens: {
        ...theme?.screens
      },
      containers: {
        ...theme?.containers
      },
      fontSize: {
        ...theme?.typography
      },
      keyframes: {
        'fade-in': {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' }
        },
        chase: {
          '0%, 80%, 100%': { backgroundColor: 'var(--surface-grey_50)' },
          '40%': { backgroundColor: 'var(--surface-grey_30)' }
        },
        pulse: {
          '50%': { opacity: '0.5' }
        }
      },
      animation: {
        'fade-in': 'fade-in 1s forwards',
        chase: 'chase 1.5s infinite ease-in-out',
        pulse: 'pulse 2s cubic-bezier(.4,0,.6,1) infinite'
      }
    },
    spacing: {
      ...theme?.spacing
    },
    fontFamily: {
      ...theme?.fontFamily
    },
    plugins: []
  },
  plugins: [
    plugin(({ addVariant }) => {
      addVariant('dark-mode', `[data-theme="${DARK_THEME_NAME}"] &`);
    }),
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('tailwind-scrollbar'),
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    require('@tailwindcss/container-queries')
  ]
} satisfies Config;
