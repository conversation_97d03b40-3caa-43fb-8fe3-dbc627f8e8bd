'use strict';

import ReactRefreshWebpackPlugin from '@pmmmwh/react-refresh-webpack-plugin';
import CircularDependencyPlugin from 'circular-dependency-plugin';
import CompressionWebpackPlugin from 'compression-webpack-plugin';
import CopyPlugin from 'copy-webpack-plugin';
import dotenv from 'dotenv';
import Dotenv from 'dotenv-webpack';
import HtmlWebpackPlugin from 'html-webpack-plugin';
import MiniCssExtractPlugin from 'mini-css-extract-plugin';
import path from 'node:path';
import TerserPlugin from 'terser-webpack-plugin';
import TsconfigPathsPlugin from 'tsconfig-paths-webpack-plugin';
import {
  container,
  DefinePlugin,
  HotModuleReplacementPlugin,
  NoEmitOnErrorsPlugin,
  NormalModuleReplacementPlugin,
  optimize,
  SourceMapDevToolPlugin
} from 'webpack';
import WebpackAssetsManifest from 'webpack-assets-manifest';
import WebpackPwaManifest from 'webpack-pwa-manifest';
import { InjectManifest } from 'workbox-webpack-plugin';
// import componentsPackageJSON from '@oh/components/package.json';
import type { Configuration as DevServerConfiguration } from 'webpack-dev-server';

import { dependencies } from './package.json';
import pwaManifest from './public/manifest.json';

const { ModuleConcatenationPlugin } = optimize;
const { ModuleFederationPlugin } = container;

const abs = (str: string) => path.resolve(__dirname, str);

const envFile = process.env.ENV_FILE || '.env';
const isServe = process.env.WEBPACK_SERVE === 'true';

dotenv.config({
  path: abs(envFile)
});

const appName = 'OneTalent';
const mode = process.env.NODE_ENV || 'development';
const isProduction = mode === 'production';
const useSourceMap = !isProduction;
const isModuleFederation = process.env.MODULE_FEDERATION === 'on';
const host = process.env.HOST || 'localhost';
const port = parseInt(process.env.FE_APP_PORT ?? '3006', 10);

// const appURL = process.env.FE_APP_URL;

const patterns = {
  name: '[name]',
  nameExt: '[name][ext]',
  nameFullhash: '[name]-[fullhash]',
  nameContenthash: '[name]-[contenthash]',
  id: '[id]',
  idContenthash: '[id]-[contenthash]',
  hashExtQuery: '[hash][ext][query]',
  hashBase64: '[hash:base64]',
  localHashBase64: '[local]--[hash:base64]',
  localHashBase645: '[local]--[hash:base64:5]'
} as const;

const paths = {
  public: 'auto',
  asset: isProduction
    ? `assets/${patterns.hashExtQuery}`
    : `assets/${patterns.nameExt}`,
  css: isProduction
    ? `css/${patterns.nameContenthash}.css`
    : `css/${patterns.name}.css`,
  cssChunk: isProduction
    ? `css/${patterns.idContenthash}.css`
    : `css/${patterns.id}.css`,
  js: isProduction
    ? `js/${patterns.nameFullhash}.js`
    : `js/${patterns.name}.js`,
  serverJs: isProduction ? `${patterns.nameFullhash}.js` : `${patterns.name}.js`
};

const resolveExts = ['.js', '.jsx', '.tsx', '.ts', '.json', '.scss'];

const loaders = [
  {
    test: /\.tsx?$/,
    exclude: [/node_modules[\\/]?!@oh/],
    use: [
      {
        loader: 'babel-loader',
        options: {
          presets: [
            [
              '@babel/preset-env',
              {
                useBuiltIns: 'entry',
                forceAllTransforms: true,
                loose: true,
                modules: false,
                corejs: 3,
                targets: {
                  node: 'current'
                }
              }
            ],
            '@babel/preset-react'
          ],
          plugins: [
            !isProduction &&
              !isModuleFederation &&
              require.resolve('react-refresh/babel'),
            [
              '@babel/plugin-transform-runtime',
              {
                corejs: false
              }
            ]
          ].filter(Boolean)
        }
      },
      {
        loader: 'ts-loader',
        options: {
          transpileOnly: true
        }
      }
    ]
  },
  {
    test: /\.css$/,
    sideEffects: true,
    use: [
      isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
      {
        loader: 'css-loader'
      }
    ]
  },
  {
    test: /\.scss$/,
    use: [
      isProduction ? MiniCssExtractPlugin.loader : 'style-loader',
      {
        loader: 'css-loader',
        options: {
          sourceMap: useSourceMap
        }
      },
      {
        loader: 'sass-loader',
        options: {
          sourceMap: useSourceMap
        }
      },
      {
        loader: 'postcss-loader',
        options: {
          sourceMap: useSourceMap,
          postcssOptions: {
            plugins: {
              'postcss-import': {},
              'tailwindcss/nesting': 'postcss-nesting',
              tailwindcss: {},
              'postcss-preset-env': {
                features: { 'nesting-rules': false }
              }
            }
          }
        }
      }
    ]
  },
  {
    test: /\.(jpe?g|png|gif|webp|ttf|eot|woff?2)$/,
    type: 'asset/resource'
  },
  {
    resourceQuery: /raw/,
    type: 'asset/source'
  },
  {
    test: /\.svg/i,
    exclude: [/node_modules[\\/]?!@oh/],
    issuer: /\.[jt]sx?$/,
    use: [
      {
        loader: require.resolve('@svgr/webpack'),
        options: {
          ref: true,
          svgoConfig: {
            plugins: [
              {
                name: 'preset-default',
                params: {
                  overrides: { removeViewBox: false, cleanupIds: false }
                }
              }
            ]
          }
        }
      }
    ]
  }
];

const htmlWebpackPlugin: HtmlWebpackPlugin.Options = {
  inject: true,
  hash: true,
  minify: isProduction,
  title: appName,
  filename: 'index.html',
  favicon: abs('public/favicon.ico'),
  template: abs('template/index.ejs'),
  isProduction,
  publicPath: '/'
  // customConfigScript: `<script src="${appURL}/config.js"></script>`
};

const plugins = [
  new NormalModuleReplacementPlugin(
    /\/event-emitter$/,
    abs('replacing/stubs/event-emitter.ts')
  ),
  isServe && new HtmlWebpackPlugin(htmlWebpackPlugin),
  new CopyPlugin({
    patterns: [{ from: abs('public/robots.txt'), to: patterns.nameExt }]
  }),
  new WebpackPwaManifest(pwaManifest as WebpackPwaManifest.ManifestOptions),
  isProduction &&
    new WebpackAssetsManifest({
      entrypoints: true,
      publicPath: paths.public,
      output: 'assets-manifest.json',
      customize: (entry) => {
        if (entry.key.toLowerCase().endsWith('.map')) {
          return false;
        }
        return entry;
      }
    }),
  isModuleFederation &&
    new ModuleFederationPlugin({
      name: 'oneTalentAdmin',
      filename: 'remoteEntry.js',
      library: { type: 'var', name: 'oneTalentAdmin' },
      exposes: {
        './Admin': abs('src/AppSpa')
      },
      shared: {
        react: {
          singleton: true,
          requiredVersion: dependencies.react,
          strictVersion: false
        },
        'react-dom': {
          singleton: true,
          requiredVersion: dependencies['react-dom'],
          strictVersion: false
        },
        'react-router-dom': {
          singleton: true,
          requiredVersion: dependencies['react-router-dom'],
          strictVersion: false
        }
        // '@oh/components': {
        //   singleton: true,
        //   requiredVersion: componentsPackageJSON.version,
        //   strictVersion: false
        // }
      }
    }),
  isProduction &&
    new InjectManifest({
      swSrc: abs('src/service-worker.ts'),
      dontCacheBustURLsMatching: /\.[0-9a-f]{8}\./,
      exclude: [/asset-manifest\.json$/],
      maximumFileSizeToCacheInBytes: 5 * 1024 * 1024
    }),
  !isProduction && !isModuleFederation && new HotModuleReplacementPlugin(),
  !isProduction &&
    !isModuleFederation &&
    new ReactRefreshWebpackPlugin({
      // disabling react overlay error messages to repeat state as in the production
      overlay: false
    }),
  !isProduction && new NoEmitOnErrorsPlugin(),
  useSourceMap &&
    new SourceMapDevToolPlugin({
      filename: '[file].map[query]'
    }),
  new Dotenv({
    path: abs(envFile),
    safe: true
  }),
  new DefinePlugin({
    'process.env.NODE_ENV': JSON.stringify(mode),
    'process.env.MODULE_FEDERATION': JSON.stringify(
      process.env.MODULE_FEDERATION
    ),
    'process.env.MODULE_CONFIG_NAME': JSON.stringify(
      'oneTalentAdmin_APP_CONFIG'
    )
  }),
  isProduction &&
    new MiniCssExtractPlugin({
      filename: paths.css,
      chunkFilename: paths.cssChunk
    }),
  isProduction && new ModuleConcatenationPlugin(),
  !isProduction &&
    new CircularDependencyPlugin({
      exclude: /node_modules/,
      failOnError: true
    }),
  isProduction &&
    new CompressionWebpackPlugin({
      filename: '[file].br',
      exclude: /node_modules/,
      test: /\.(js)$/,
      algorithm: 'brotliCompress'
    }),
  isProduction &&
    new CompressionWebpackPlugin({
      filename: '[file].gz',
      exclude: /node_modules/,
      test: /\.(js)$/,
      algorithm: 'gzip'
    })
].filter(Boolean);

const devServer: DevServerConfiguration = {
  static: {
    directory: path.resolve(__dirname, 'public'),
    publicPath: paths.public
  },

  client: {
    // disabling react overlay error messages to repeat state as in the production
    overlay: false
  },

  compress: true,
  host,
  port,
  historyApiFallback: true,
  allowedHosts: 'all',

  headers: {
    'Access-Control-Allow-Origin': '*'
  },
  open: {
    target: ['/one-talent/']
  }
};

const optimization = {
  concatenateModules: isProduction,
  usedExports: true,
  minimize: isProduction,
  minimizer: [
    new TerserPlugin({
      minify: TerserPlugin.uglifyJsMinify,
      terserOptions: {
        compress: {
          drop_console: true,
          booleans: true,
          comparisons: true,
          if_return: true,
          loops: true,
          reduce_vars: true,
          unused: true
        },
        mangle: {
          keep_fnames: false
        },
        output: {
          comments: false,
          beautify: false
        }
      },
      extractComments: false,
      parallel: true
    })
  ],
  ...(!isModuleFederation && {
    splitChunks: {
      chunks: 'all'
    },
    runtimeChunk: {
      name: ({ name }: { name: string }) => `runtime-${name}`
    }
  })
};

const config = {
  performance: {
    hints: 'error',
    maxEntrypointSize: 0.5 * 1048576, // 0.5 MiB
    maxAssetSize: 20 * 1048576 // 20 MiB
  },
  experiments: {
    asyncWebAssembly: true
  },
  mode,
  name: 'client',
  target: 'web',
  entry: abs('src'),
  output: {
    uniqueName: 'oneTalentAdmin',
    path: abs('build'),
    filename: paths.js,
    publicPath: paths.public,
    assetModuleFilename: paths.asset,
    clean: true
  },
  resolve: {
    alias: {
      imgs: abs('src/assets/imgs'),
      'html-diff-ts': abs('node_modules/html-diff-ts/lib/esm/Diff.js')
    },
    fallback: {
      path: require.resolve('path-browserify')
    },
    extensions: resolveExts,
    plugins: [new TsconfigPathsPlugin()]
  },
  module: {
    rules: [...loaders]
  },
  plugins,
  devServer,
  optimization,
  context: __dirname,
  devtool: useSourceMap ? 'source-map' : false,
  cache: true
};

export default config;
