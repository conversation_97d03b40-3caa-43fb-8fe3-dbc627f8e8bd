<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
  </PropertyGroup>
  <ItemGroup>
    <PackageVersion Include="AspNetCore.HealthChecks.SqlServer" Version="9.0.0" />
    <PackageVersion Include="Bogus" Version="35.6.3" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Hosting" Version="9.0.5" />
    <PackageVersion Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.5" />
    <PackageVersion Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.Testing" Version="9.4.0" />
    <PackageVersion Include="Microsoft.Extensions.TimeProvider.Testing" Version="9.4.0" />
    <PackageVersion Include="MassTransit.EntityFrameworkCore" Version="8.5.2" />
    <PackageVersion Include="Moq" Version="4.20.72" />
    <PackageVersion Include="OneTalent.AtrService.WebAPI.Client" Version="1.0.37-dev" />
    <PackageVersion Include="OneTalent.Azure.Extensions" Version="1.0.1" />
    <PackageVersion Include="OneTalent.BlobStorage" Version="1.2.0-exception.1" />
    <PackageVersion Include="OneTalent.ServiceBus" Version="1.7.3" />
    <PackageVersion Include="OneTalent.Common.Extensions" Version="1.4.4" />
    <PackageVersion Include="OneTalent.Hangfire.Extensions" Version="1.0.2" />
    <PackageVersion Include="OneTalent.Sql.Extensions" Version="1.1.0" />
    <PackageVersion Include="OneTalent.WebAPI.Extensions" Version="1.15.7" />
    <PackageVersion Include="OneTalent.DataSyncService.WebAPI.Client" Version="1.13.2" />
    <PackageVersion Include="Polly" Version="8.6.2" />
    <PackageVersion Include="Refit" Version="8.0.0" />
    <PackageVersion Include="Refit.HttpClientFactory" Version="8.0.0" />
    <PackageVersion Include="Serilog.Expressions" Version="5.0.0" />
    <PackageVersion Include="System.CommandLine" Version="2.0.0-beta4.22272.1" />
    <PackageVersion Include="System.Linq.Async" Version="6.0.3" />
    <PackageVersion Include="FluentAssertions" Version="7.2.0" />
    <PackageVersion Include="Alba" Version="8.2.0" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.14.1" />
    <PackageVersion Include="Verify.XunitV3" Version="30.0.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="3.1.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageVersion>
    <PackageVersion Include="Testcontainers" Version="4.4.0" />
    <PackageVersion Include="Testcontainers.MsSql" Version="4.4.0" />
    <PackageVersion Include="xunit.v3" Version="3.0.0" />
  </ItemGroup>
</Project>