/** Please add you custom styles here */

@layer base {
  body {
    font-family: 'ADNOC Sans';
  }

  * {
    transition:
      opacity 0.2s ease,
      background-color 0.2s ease,
      fill 0.2s ease,
      border-color 0.2s ease;
  }

  .overflow-y-auto {
    @apply scrollbar-thin scrollbar-track-transparent scrollbar-thumb-scroll_bar-bar-rested hover:scrollbar-thumb-scroll_bar-bar-hover;
  }

  .ot-uikit-loader {
    animation: rotation 1s linear infinite;
  }

  .dot {
    @apply h-12 w-12 rounded-full bg-gray-300;
  }

  .gradient-surface-gray {
    background: linear-gradient(
      180deg,
      var(--surface-grey_10) 0%,
      var(--surface-grey_0) 100%
    );
  }

  @keyframes rotation {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes swipeOutLeft {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    100% {
      transform: translateX(-100vw);
      opacity: 0;
    }
  }

  @keyframes swipeOutRight {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    100% {
      transform: translateX(100vw);
      opacity: 0;
    }
  }

  @keyframes swipeReturn {
    0% {
      transform: translateX(var(--swipe-distance, 0));
      opacity: var(--swipe-opacity, 1);
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .ot-uikit-toast-swipe-container {
    @media (hover: none) and (pointer: coarse) {
      touch-action: pan-y;
      user-select: none;
    }
  }
}
