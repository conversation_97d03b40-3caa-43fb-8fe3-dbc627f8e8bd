import { FC, memo, ReactNode, useMemo } from 'react';

import { cn } from '@/utils';

import { DropdownContext } from './Dropdown.context';
import {
  useDropdownFloating,
  UseDropdownFloatingProps
} from './Dropdown.hooks';

import { DataAttributesProps } from '../types';

export type DropdownProps = DataAttributesProps &
  Pick<React.HTMLAttributes<HTMLDivElement>, 'style'> &
  Pick<DropdownContext, 'isOpen' | 'setIsOpen' | 'title'> &
  Pick<UseDropdownFloatingProps, 'fallbackPlacements' | 'placement'> & {
    renderTrigger: ({
      getTriggerProps
    }: {
      getTriggerProps: (
        userProps?: React.HTMLProps<HTMLElement>
      ) => Record<string, unknown>;
    }) => ReactNode;
    renderDropdown: (
      props: Pick<
        DropdownContext,
        'getFloatingProps' | 'refs' | 'floatingStyles'
      >
    ) => ReactNode;
    className?: string;
    triggerClassName?: string;
    autoWidth?: boolean;
    dropdownOptions?: {
      width?: string;
    };
  };

export const Dropdown: FC<DropdownProps> = memo(
  ({
    dataAttributes = 'Dropdown',
    renderTrigger,
    renderDropdown,
    className,
    triggerClassName,
    style,
    ...props
  }) => {
    const floating = useDropdownFloating(props);
    const contextData = useMemo(
      () => ({
        ...floating,
        ...props
      }),
      [floating, props]
    );
    return (
      <DropdownContext.Provider value={contextData}>
        <div
          style={style}
          data-attributes={dataAttributes}
          className={cn('group relative flex w-full flex-col', className)}
          ref={floating.refs.setReference}
        >
          <div
            data-attributes="DropdownTrigger"
            className={cn(
              {
                'active group': props.isOpen
              },
              triggerClassName
            )}
          >
            {renderTrigger({ getTriggerProps: floating.getReferenceProps })}
          </div>

          {renderDropdown(floating)}
        </div>
      </DropdownContext.Provider>
    );
  }
);
