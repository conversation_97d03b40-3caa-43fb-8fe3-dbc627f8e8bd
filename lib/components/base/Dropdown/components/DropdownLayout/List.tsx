import { ComponentProps, forwardRef, ReactNode, useMemo, useRef } from 'react';

import { useVirtualizer } from '@tanstack/react-virtual';

import { generateId } from '@/utils';

import { ListSkeleton } from './ListSkeleton';

import { DropdownNoResultsFound } from '../../DropdownNoResultsFound';

import { DataAttributesProps } from '../../../types';

type ListProps = ComponentProps<'ul'> &
  DataAttributesProps & {
    showNoResultsFound?: boolean;
    isLoading?: boolean;
    count?: number;
    renderItem: (
      index: number,
      itemProps: Pick<ComponentProps<'li'>, 'style'>
    ) => ReactNode;
  };

export const List = forwardRef<HTMLUListElement, ListProps>(
  (
    {
      dataAttributes = 'DropdownList',
      showNoResultsFound,
      isLoading,
      count = 0,
      renderItem,
      ...props
    },
    ref
  ) => {
    const id = useMemo(() => generateId(dataAttributes), [dataAttributes]);

    const parentRef = useRef(null);

    const rowVirtualizer = useVirtualizer({
      enabled: !!count,
      count,
      getScrollElement: () => parentRef.current,
      estimateSize: () => 45,
      overscan: 5
    });

    if (isLoading) {
      return <ListSkeleton />;
    }

    if (showNoResultsFound) {
      return <DropdownNoResultsFound />;
    }

    return (
      <div
        data-attributes={dataAttributes}
        ref={parentRef}
        className="overflow-y-auto md:!border-b md:!border-solid md:!border-divider-mid"
      >
        <ul
          ref={ref}
          style={{
            height: `${rowVirtualizer.getTotalSize()}px`,
            position: 'relative'
          }}
          id={id}
          className="mr-8"
          {...props}
        >
          {rowVirtualizer.getVirtualItems().map((virtualRow) => {
            const itemProps: Pick<ComponentProps<'li'>, 'style'> = {
              style: {
                position: 'absolute',
                top: 0,
                left: 0,
                width: '100%',
                height: `${virtualRow.size}px`,
                transform: `translateY(${virtualRow.start}px)`
              }
            };
            return renderItem(virtualRow.index, itemProps);
          })}
        </ul>
      </div>
    );
  }
);
