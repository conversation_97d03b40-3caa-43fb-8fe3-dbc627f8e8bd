import { useCallback, useContext, useEffect, useState } from 'react';

import {
  autoUpdate,
  flip,
  offset,
  Placement,
  shift,
  useClick,
  useDismiss,
  useFloating,
  useInteractions
} from '@floating-ui/react';

import { DropdownContext } from './Dropdown.context';

import {
  isTooltipDrawerOpen,
  subscribeToTooltipDrawerState
} from '../Tooltip/TooltipDrawerGlobalState';

import { useIsMobile } from '../../../hooks';

export type UseDropdownFloatingProps = {
  placement?: Placement;
  fallbackPlacements?: Placement[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  dropdownOptions?: {
    width?: string;
  };
  autoWidth?: boolean;
};

export const useDropdownFloating = ({
  placement = 'bottom',
  fallbackPlacements = ['bottom', 'top'],
  isOpen,
  setIsOpen,
  dropdownOptions,
  autoWidth = false
}: UseDropdownFloatingProps) => {
  const [tooltipDrawerOpen, setTooltipDrawerOpenState] = useState(
    isTooltipDrawerOpen()
  );

  useEffect(() => {
    const unsubscribe = subscribeToTooltipDrawerState((isOpen) => {
      setTooltipDrawerOpenState(isOpen);
    });
    return unsubscribe;
  }, []);

  const isMobile = useIsMobile();
  const { refs, floatingStyles, context } = useFloating({
    open: isOpen,
    onOpenChange: setIsOpen,
    placement,
    middleware: [
      offset(8),
      flip({ fallbackPlacements }),
      shift({ padding: 0 })
    ],
    whileElementsMounted: autoUpdate
  });

  const click = useClick(context, {
    enabled: !tooltipDrawerOpen
  });
  const dismiss = useDismiss(context, {
    enabled: !tooltipDrawerOpen
  });

  const { getReferenceProps, getFloatingProps } = useInteractions([
    click,
    dismiss
  ]);

  const dimensions =
    (!isMobile && dropdownOptions) ||
    refs.reference.current?.getBoundingClientRect();

  return {
    refs,
    floatingStyles: autoWidth
      ? floatingStyles
      : { ...floatingStyles, width: dimensions?.width },
    getFloatingProps,
    getReferenceProps
  };
};

export const useDropdownContext = () => {
  const ctx = useContext(DropdownContext);
  if (!ctx) throw new Error('Must be used within <Dropdown />');
  return ctx;
};

export const useDropdownScrollParent = (selector?: string) => {
  return useCallback((): HTMLElement | null => {
    const element = document.querySelector(
      selector || '[data-federated-wrapper="scroll"]'
    );
    const parent =
      (element instanceof HTMLElement ? element : null) || document.body;

    return parent;
  }, [selector]);
};
