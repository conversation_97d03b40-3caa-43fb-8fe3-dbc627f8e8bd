import { useState } from 'react';

import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Layout, LayoutProps, LayoutVariant } from './Layout';

import { Button, ButtonSize, ButtonVariant } from '../Button';
import { Drawer } from '../Drawer';
import { TabItem, Tabs } from '../Tabs';

import { useIsMobile } from '../../../hooks';

const meta = {
  title: 'Design System/Components/Layout/Layout',
  component: Layout,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component:
          'Layout component that provides different layout variants for various use cases including eService, L1, Approval, and Tab layouts.'
      }
    }
  },
  args: {
    className: 'bg-surface-grey_20',
    children: (
      <div className="flex min-h-[784px] w-full flex-auto flex-col items-center justify-center rounded-xl bg-surface-grey_30 p-24 text-header-3-regular">
        Content
      </div>
    ),
    headerTitle: 'Title',
    onGoBack() {},
    onClose: undefined,
    onPin: undefined,
    variant: LayoutVariant.OneHubEService,
    containerClassName: undefined,
    buttonTooltip: 'Go back',
    isPinned: false,
    paddingLeft: 144,
    paddingRight: 40,
    contentMaxWidth: 1600,
    dataAttributes: 'Layout'
  },
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: Object.values(LayoutVariant).filter(
        (v) => v !== LayoutVariant.eService && v !== LayoutVariant.L1
      ),
      description: 'The layout variant to use',
      table: {
        type: { summary: 'LayoutVariant' },
        defaultValue: { summary: LayoutVariant.OneHubEService }
      }
    },
    headerTitle: {
      control: { type: 'text' },
      description: 'The title displayed in the header',
      table: {
        type: { summary: 'ReactNode' }
      }
    },
    className: {
      control: { type: 'text' },
      description: 'Additional CSS classes for the layout container',
      table: {
        type: { summary: 'string' }
      }
    },
    headerClassName: {
      control: { type: 'text' },
      description: 'Additional CSS classes for the header',
      table: {
        type: { summary: 'string' }
      }
    },
    contentClassName: {
      control: { type: 'text' },
      description: 'Additional CSS classes for the content area',
      table: {
        type: { summary: 'string' }
      }
    },
    children: {
      control: false,
      description: 'The content to be rendered inside the layout',
      table: {
        type: { summary: 'ReactNode' }
      }
    },
    onClose: {
      action: 'closed',
      description: 'Callback function when close button is clicked',
      table: {
        type: { summary: '() => void' }
      }
    },
    onGoBack: {
      action: 'went back',
      description: 'Callback function when back button is clicked',
      table: {
        type: { summary: '() => void' }
      }
    },
    onPin: {
      action: 'pinned/unpinned',
      description: 'Callback function when pin/unpin button is clicked',
      table: {
        type: { summary: '() => void' }
      }
    },
    buttonTooltip: {
      control: { type: 'text' },
      description: 'Tooltip text for the back/close buttons',
      table: {
        type: { summary: 'string' }
      }
    },
    isPinned: {
      control: { type: 'boolean' },
      description: 'Whether the layout is pinned to homepage',
      table: {
        type: { summary: 'boolean' }
      }
    },
    paddingLeft: {
      control: { type: 'number' },
      description: 'Left padding for the layout content'
    },
    paddingRight: {
      control: { type: 'number' },
      description: 'Right padding for the layout content'
    },
    contentMaxWidth: {
      control: { type: 'number' },
      description: 'Maximum width for the layout content'
    },
    dataAttributes: {
      control: { type: 'text' },
      description: 'Data attributes for testing and accessibility',
      table: {
        type: { summary: 'string' },
        defaultValue: { summary: 'Layout' }
      }
    },
    containerClassName: {
      control: { type: 'text' },
      description: 'Additional CSS classes for the header container',
      table: {
        type: { summary: 'string' }
      }
    }
  }
} satisfies Meta<typeof Layout>;

export default meta;
type Story = StoryObj<typeof meta>;

export const OneHubEServiceLayout: Story = {
  name: 'OneHub eService Layout',
  args: {
    headerTitle: 'Objectives',
    variant: LayoutVariant.OneHubEService
  }
};

export const ExternalEServiceLayout: Story = {
  name: 'External eService Layout',
  args: {
    headerTitle: 'Onboarding',
    variant: LayoutVariant.ExternalEService,
    onGoBack: undefined
  }
};

export const L1PageLayout: Story = {
  name: 'L1 Page Layout',
  args: {
    headerTitle: 'Talent Journey',
    variant: LayoutVariant.L1Page,
    onGoBack: undefined
  }
};

export const L1PageLayoutWithBackButton: Story = {
  name: 'L1 Page Layout with Back Button',
  args: {
    headerTitle: 'Talent Card',
    variant: LayoutVariant.L1Page
  }
};

export const ApprovalLayout: Story = {
  name: 'Approval Layout',
  args: {
    headerTitle: undefined,
    onGoBack: undefined,
    variant: LayoutVariant.Approval
  },
  render: (props) => {
    return (
      <Layout
        headerTitle="Approvals"
        variant="L1"
        className="bg-surface-grey_20"
      >
        <div className="flex-fill flex w-full">
          <div className="flex-fill flex min-h-[900px] min-w-[423px] flex-col items-center justify-center rounded-lg bg-surface-grey_30 text-header-3-regular"></div>
          <div className="flex-fill flex w-full flex-col px-40 pt-40">
            <Layout {...props} />
          </div>
        </div>
      </Layout>
    );
  }
};

const TabLayoutTemplate = (props: LayoutProps) => {
  const handleTabClick = (id: string) => {
    setActiveTabId(id);
  };

  const TABS: TabItem<string>[] = [
    {
      id: '1',
      label: 'Overview'
    },
    {
      id: '2',
      label: 'My Shorts'
    },
    {
      id: '3',
      label: 'Talent Journey'
    }
  ];

  const [activeTabId, setActiveTabId] = useState(TABS[2].id);

  return (
    <Layout
      headerTitle="My Profile"
      variant="L1"
      className="bg-surface-grey_20"
    >
      <div className="flex w-full flex-auto flex-col">
        <div className="flex-fill mb-20 flex min-h-[354px] items-center justify-center rounded-lg bg-surface-grey_30 text-header-3-regular"></div>

        <Tabs
          items={TABS}
          activeTabId={activeTabId}
          onChange={handleTabClick}
        ></Tabs>
        <Layout {...props} />
      </div>
    </Layout>
  );
};

export const TabLayout: Story = {
  name: 'Tab Layout',
  args: {
    headerTitle: undefined,
    onGoBack: undefined,
    variant: LayoutVariant.Tab
  },
  render: TabLayoutTemplate
};

const LayoutWithDrawerTemplate = (props: LayoutProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExpanded, setExpanded] = useState(true);
  const isMobile = useIsMobile();

  return (
    <Layout {...props}>
      <div className="fixed left-0 top-0 flex h-full w-[124px] justify-center py-12 pl-24 pr-20 3xl:left-[initial] 3xl:right-[calc(1476px+((100%-1600px)/2))]">
        <div className="flex h-full max-h-[900px] w-[80px] rounded-xl bg-surface-grey_0"></div>
      </div>
      <div className="flex min-h-[784px] w-full flex-auto flex-col items-center justify-center rounded-xl bg-surface-grey_30 p-24 text-header-3-regular">
        <Button onClick={() => setIsOpen(true)}>Open Drawer</Button>
        <Drawer
          isOpen={isOpen}
          isExpanded={isExpanded}
          header={'Header'}
          onBack={() => setIsOpen(false)}
          onClose={() => setIsOpen(false)}
          onToggleExpand={setExpanded}
          footer={
            <>
              <Button
                variant={ButtonVariant.Secondary}
                size={
                  isMobile || isExpanded ? ButtonSize.Medium : ButtonSize.Large
                }
                className="flex-auto"
                onClick={() => setIsOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant={ButtonVariant.Primary}
                size={
                  isMobile || isExpanded ? ButtonSize.Medium : ButtonSize.Large
                }
                className="flex-auto"
                onClick={() => setIsOpen(false)}
              >
                Save
              </Button>
            </>
          }
        />
      </div>
    </Layout>
  );
};

export const LayoutWithDrawer: Story = {
  name: 'Layout Drawer',
  args: {
    headerTitle: 'Objectives',
    variant: LayoutVariant.eService
  },
  render: LayoutWithDrawerTemplate
};

export const LayoutWithPin: Story = {
  name: 'Layout with Pin',
  args: {
    headerTitle: 'Pinnable Layout',
    variant: LayoutVariant.eService,
    isPinned: true,
    onPin() {}
  }
};
