import { FC } from 'react';

import {
  LAYOUT_DATA_ATRIBUTES,
  LAYOUT_MAX_WIDTH,
  LAYOUT_PADDING_LEFT,
  LAYOUT_PADDING_RIGHT,
  LayoutProps,
  LayoutVariant
} from './Layout';

import { Header, HeaderVariant } from '../Header';

import { useIsMobile } from '../../../hooks';
import { cn } from '../../../utils';

const getHeaderVariant = (variant?: LayoutVariant) => {
  switch (variant) {
    case LayoutVariant.eService:
    case LayoutVariant.OneHubEService:
      return HeaderVariant.OneHubEService;
    case LayoutVariant.ExternalEService:
      return HeaderVariant.ExternalEService;
    case LayoutVariant.L1:
    case LayoutVariant.L1Page:
      return HeaderVariant.L1Page;
    default:
      return HeaderVariant.eService;
  }
};

export const EServiceLayout: FC<LayoutProps> = ({
  children,
  className,
  variant,
  headerClassName,
  contentClassName,
  dataAttributes = LAYOUT_DATA_ATRIBUTES,
  paddingLeft = LAYOUT_PADDING_LEFT,
  paddingRight = LAYOUT_PADDING_RIGHT,
  contentMaxWidth = LAYOUT_MAX_WIDTH,
  ...headerProps
}) => {
  const isMobile = useIsMobile();
  const headerVariant = getHeaderVariant(variant);

  return (
    <div
      data-attributes={dataAttributes}
      className={cn('mx-auto flex w-full flex-auto flex-col', className)}
    >
      <Header
        className={headerClassName}
        paddingLeft={paddingLeft}
        paddingRight={paddingRight}
        contentMaxWidth={contentMaxWidth}
        headerVariant={headerVariant}
        {...headerProps}
      />

      <div
        style={
          !isMobile
            ? {
                paddingLeft,
                paddingRight,
                maxWidth: contentMaxWidth
              }
            : undefined
        }
        className={cn(
          'm-auto flex w-full flex-col items-center',
          !isMobile && 'px-20',
          !isMobile &&
            variant === LayoutVariant.ExternalEService &&
            '-mt-[96px]',
          contentClassName
        )}
      >
        {children}
      </div>
    </div>
  );
};
