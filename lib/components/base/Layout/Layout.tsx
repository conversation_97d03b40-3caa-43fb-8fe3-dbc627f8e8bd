import { FC, PropsWithChildren } from 'react';

import { ApprovalLayout } from './ApprovalLayout';
import { EServiceLayout } from './eServiceLayout';
import { TabLayout } from './TabLayout';

import { HeaderProps } from '../Header';

export const LAYOUT_PADDING_LEFT = 144;
export const LAYOUT_PADDING_RIGHT = 40;
export const LAYOUT_MAX_WIDTH = 1600;
export const LAYOUT_DATA_ATRIBUTES = 'Layout';

export const LayoutVariant = {
  // @deprecated use OneHubEService instead
  eService: 'eService',
  // @deprecated use L1Page instead
  L1: 'L1',

  OneHubEService: 'OneHubEService',
  ExternalEService: 'ExternalEService',
  L1Page: 'L1Page',
  Approval: 'Approval',
  Tab: 'Tab'
} as const;

export type LayoutVariant = (typeof LayoutVariant)[keyof typeof LayoutVariant];

export type LayoutProps = Omit<HeaderProps, 'headerVariant'> &
  PropsWithChildren & {
    headerClassName?: string;
    contentClassName?: string;
    variant?: LayoutVariant;
  };

export const Layout: FC<LayoutProps> = ({ variant, ...props }) => {
  switch (variant) {
    case LayoutVariant.eService:
    case LayoutVariant.L1:
    case LayoutVariant.OneHubEService:
    case LayoutVariant.ExternalEService:
    case LayoutVariant.L1Page:
      return <EServiceLayout {...props} variant={variant} />;
    case LayoutVariant.Approval:
      return <ApprovalLayout {...props} variant={variant} />;
    case LayoutVariant.Tab:
      return <TabLayout {...props} variant={variant} />;
    default:
      return <EServiceLayout {...props} variant={variant} />;
  }
};
