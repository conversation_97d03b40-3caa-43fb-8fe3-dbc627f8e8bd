import type { Meta, StoryObj } from '@storybook/react';

import { OnetalentUIKitProvider } from './UIKitProvider';

const meta = {
  title: 'Design System/Providers/OnetalentUIKitProvider',
  component: OnetalentUIKitProvider,
  tags: ['autodocs', 'Stable'],
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'The OnetalentUIKitProvider is the root provider component that wraps your application and provides context for various UI kit features including portal management, toast notifications, and avatar loading. It should be placed at the root of your application to enable all UI kit functionality.'
      }
    }
  },
  args: {
    children: undefined,
    portalClassName: undefined,
    loadAvatar: undefined,
    toastContext: undefined
  },
  argTypes: {
    children: {
      control: false,
      description: 'The child components to be wrapped by the provider'
    },
    portalClassName: {
      control: 'text',
      description: 'CSS class name for portal elements (modals, tooltips, etc.)'
    },
    loadAvatar: {
      control: false,
      description: 'Function to load avatar images by ID'
    },
    toastContext: {
      control: false,
      description: 'Custom toast context for managing toast notifications'
    }
  }
} satisfies Meta<typeof OnetalentUIKitProvider>;

export default meta;

type Story = StoryObj<typeof OnetalentUIKitProvider>;

export const Default: Story = {};
