import { memo } from 'react';

import { DropdownOption } from '../Dropdown';
import { DropdownSingleSelect } from '../DropdownSingleSelect';

import { cn } from '../../../utils';

export interface TableSelectCellProps {
  value?: DropdownOption;
  options: DropdownOption[];
  placeholder?: string;
  disabled?: boolean;
  onChange?: (option?: DropdownOption) => void;
  className?: string;
  dataAttributes?: string;
  size?: 'Small' | 'Medium' | 'Large';
  showClear?: boolean;
}

export const TableSelectCell = memo<TableSelectCellProps>(
  ({
    value,
    options,
    placeholder = 'Select...',
    disabled = false,
    onChange,
    className,
    dataAttributes,
    size = 'Small',
    showClear = true
  }) => {
    return (
      <div
        className={cn('flex h-full w-full items-center', className)}
        data-attributes={dataAttributes}
      >
        <DropdownSingleSelect
          value={value}
          options={options}
          placeholder={placeholder}
          disabled={disabled}
          onChange={onChange}
          size={size}
          showClear={showClear}
          label=""
          className="focus-within:border-primary-default h-full w-full rounded-none border border-transparent hover:border-divider-mid"
        />
      </div>
    );
  }
);

TableSelectCell.displayName = 'TableSelectCell';
