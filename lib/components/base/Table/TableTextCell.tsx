import { FC } from 'react';

import { Text } from '../Text';
import { DataAttributesProps } from '../types';

type TableTextCellProps = React.ComponentProps<'div'> &
  DataAttributesProps & {
    classNames?: string;
    lineClamp?: number;
  };

export const TableTextCell: FC<TableTextCellProps> = ({
  children,
  classNames,
  dataAttributes = 'TableTextCell',
  lineClamp = 2,
  ...props
}) => {
  const Comp = 'span';
  return (
    <Comp
      data-slot="span"
      className={classNames}
      data-attributes={dataAttributes}
      {...props}
    >
      <Text
        className={classNames}
        lineClamp={lineClamp}
        tooltipMode={'truncated'}
      >
        {children}
      </Text>
    </Comp>
  );
};
