import type { Meta, StoryObj } from '@storybook/react';

import { TableInputCell } from './TableInputCell';

const meta: Meta<typeof TableInputCell> = {
  title: 'Design System/Components/Table/TableInputCell',
  component: TableInputCell,
  parameters: {
    layout: 'centered'
  },
  argTypes: {
    type: {
      control: { type: 'select' },
      options: ['text', 'number', 'email', 'password']
    },
    size: {
      control: { type: 'select' },
      options: ['Small', 'Medium', 'Large']
    },
    disabled: {
      control: { type: 'boolean' }
    },
    readonly: {
      control: { type: 'boolean' }
    },
    error: {
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    placeholder: 'Enter text...',
    type: 'text'
  }
};

export const WithValue: Story = {
  args: {
    value: 'Sample text',
    placeholder: 'Enter text...',
    type: 'text'
  }
};

export const Disabled: Story = {
  args: {
    value: 'Disabled input',
    placeholder: 'Enter text...',
    type: 'text',
    disabled: true
  }
};

export const ReadOnly: Story = {
  args: {
    value: 'Read-only input',
    placeholder: 'Enter text...',
    type: 'text',
    readonly: true
  }
};

export const NumberInput: Story = {
  args: {
    placeholder: 'Enter number...',
    type: 'number'
  }
};

export const EmailInput: Story = {
  args: {
    placeholder: 'Enter email...',
    type: 'email'
  }
};
