import React, {
  Dispatch,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState
} from 'react';

import { cn } from '@/utils';

import {
  CustomRowExtraOptionsGetter,
  DataColumnGroupProps,
  DataColumnProps,
  DataRowOptions,
  DataRowProps,
  DataSourceState,
  NonSortable,
  StickyButtonProps,
  TableDataSourceState,
  TableProps
} from './types';

import { Icon } from '../Icon';
import { IllustrationMessage } from '../Illustration';
import { Loader } from '../Loader';

// Table Header Component
const TableHeader = <TItem, TId, TFilters>({
  columns,
  height,
  columnGroups,
  dataSourceState,
  setDataSourceState,
  showColumnsConfig: _showColumnsConfig,
  onColumnsConfig: _onColumnsConfig,
  stickyButton
}: {
  columns: DataColumnProps<TItem, TId>[];
  height?: number;
  columnGroups?: DataColumnGroupProps[];
  dataSourceState: TableDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<TableDataSourceState<TId, TFilters>>
  >;
  showColumnsConfig?: boolean;
  onColumnsConfig?: () => void;
  stickyButton?: StickyButtonProps<TItem>;
}) => {
  const handleSort = (columnKey: string) => {
    const column = columns.find((col) => col.key === columnKey);
    if (!column?.sortable) return;

    setDataSourceState((prev) => ({
      ...prev,
      sortBy: columnKey,
      sortDirection:
        prev.sortBy === columnKey && prev.sortDirection === 'asc'
          ? 'desc'
          : 'asc'
    }));
  };

  return (
    <div data-attributes="TableHeader">
      <div className="flex flex-row justify-between">
        <div
          className={cn(
            'flex w-full flex-col bg-surface-grey_20 scrollbar-none',
            stickyButton ? 'rounded-l-md' : 'rounded-md'
          )}
        >
          {/* Column Groups Row */}
          {columnGroups && (
            <div className="flex">
              {columnGroups.map((group) => {
                const groupColumns = columns.filter((col) =>
                  group.columns.includes(col.key)
                );
                const groupGrow = groupColumns.find((col) => col.grow)?.grow;
                const totalWidth = groupColumns.reduce((sum, col) => {
                  return sum + (col.width || 200);
                }, 0);

                return (
                  <div
                    key={group.title}
                    data-attributes={`column-group-${group.title}`}
                    className={cn(
                      'flex items-center justify-start border-b border-r border-divider-mid bg-surface-grey_20 px-16 text-center text-label-m1-regular text-text-heading last:border-r-0',
                      group.className
                    )}
                    style={{
                      minWidth: totalWidth,
                      ...(groupGrow
                        ? { flexGrow: groupGrow, width: totalWidth }
                        : {}),
                      height
                    }}
                  >
                    {group.title}
                  </div>
                );
              })}
              {columns.some(
                (col) =>
                  !columnGroups.some((group) => group.columns.includes(col.key))
              ) && (
                <div
                  className="flex-1"
                  style={{
                    minWidth: columns
                      .filter(
                        (col) =>
                          !columnGroups.some((group) =>
                            group.columns.includes(col.key)
                          )
                      )
                      .reduce((sum, col) => sum + (col.width || 200), 0)
                  }}
                />
              )}
            </div>
          )}
          {/* Columns Row */}
          <div className="flex h-40 min-w-max">
            {columns.map((column) => (
              <div
                key={column.key}
                className={cn(
                  'flex items-center whitespace-nowrap border-r border-divider-mid bg-surface-grey_20 px-16 text-label-m1-regular text-text-heading last:border-r-0',
                  !column.width && !column.grow && 'flex-1',
                  column.sortable && 'cursor-pointer',
                  column.headerClassName
                )}
                style={{
                  ...(column.width
                    ? {
                        width: column.width,
                        minWidth: column.minWidth || column.width,
                        flexGrow: column.grow || 0
                      }
                    : {
                        minWidth: column.minWidth || '200px',
                        flexGrow: column.grow || 1
                      })
                }}
                onClick={() => handleSort(column.key)}
              >
                <div className="flex items-center">
                  {column.caption || column.title}
                  {column.sortable && (
                    <span className="ml-4 flex-shrink-0">
                      {dataSourceState.sortBy === column.key ? (
                        dataSourceState.sortDirection === 'asc' ? (
                          <Icon
                            name="Sort_up_light"
                            className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                          />
                        ) : (
                          <Icon
                            name="Sort_down_light"
                            className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                          />
                        )
                      ) : (
                        <Icon
                          name="Sort_list_light"
                          className="h-20 min-h-20 w-20 min-w-20 stroke-[0.5px]"
                        />
                      )}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Table Row Component
const TableRow = <TItem, TId>({
  item,
  index,
  columns,
  height = 67,
  rowOptions,
  onRowClick,
  renderRowContent,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions
}: {
  item: TItem;
  index: number;
  columns: DataColumnProps<TItem, TId>[];
  height?: number;
  rowOptions?: DataRowOptions<NonSortable<TItem>, TId>;
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  renderRowContent?: (item: TItem, index: number) => React.ReactNode;
  withCustomRow?: boolean;
  rowDataAttributes?: string;
  getCustomRowExtraOptions?: CustomRowExtraOptionsGetter<TItem>;
  stickyButton?: StickyButtonProps<TItem>;
  hasContentOverflow?: boolean;
}) => {
  const handleClick = () => {
    if (onRowClick) {
      const rowProps: DataRowProps<TItem, TId> = {
        item,
        index,
        id: (item as Record<string, unknown>).id as TId
      };
      onRowClick((item as Record<string, unknown>).id as TId, rowProps);
    }
  };

  const customOptions =
    withCustomRow && getCustomRowExtraOptions
      ? getCustomRowExtraOptions(item, index)
      : {};

  return (
    <div
      className={cn(
        'flex flex-row justify-between',
        rowOptions?.disabled && 'cursor-not-allowed opacity-50',
        rowOptions?.className
      )}
      style={{ height: '67px' }} // 67px content + 1px border
      onClick={!rowOptions?.disabled ? handleClick : undefined}
      data-attributes={rowDataAttributes}
      {...customOptions}
    >
      <div className="flex flex-1">
        {renderRowContent ? (
          <div
            className="py-3 flex flex-1 items-center pl-4 pr-4"
            style={{ height }}
          >
            {renderRowContent(item, index)}
          </div>
        ) : (
          columns.map((column, index) => (
            <div
              key={column.key}
              className={cn(
                'flex min-w-full items-center border-b border-divider-mid',
                index > 0 ? 'px-12' : 'px-8',
                'text-body-2-regular text-input-text-filled',
                column.grow && `flex-grow-${column.grow}`,
                !column.width && !column.grow && 'flex-1',
                column.className
              )}
              style={{
                height,
                ...(column.width
                  ? {
                      width: column.width,
                      minWidth: column.minWidth || column.width,
                      flexGrow: column.grow || 0
                    }
                  : {
                      minWidth: column.minWidth || '120px',
                      flexGrow: column.grow || 1
                    })
              }}
            >
              {column.render
                ? column.render(item, index)
                : String((item as Record<string, unknown>)[column.key])}
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// Main Table Component
export const Table = <TItem, TId, TFilters = unknown>({
  dataSourceState,
  setDataSourceState,
  getRows,
  columns,
  headerSize = 40,
  _columnsGap = 24,
  columnGroups,
  renderRowContent,
  renderError,
  renderNoResultsBlock,
  onRowClick,
  getRowOptions,
  isFoldedByDefault,
  isLoading = false,
  isError = false,
  _error,
  styles,
  dataAttributes,
  classNames,
  errorContainerClassName,
  onVisibleRowsCountChanged,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions,
  stickyButton,
  scrollbarDisabled = false,
  loadingStrategy = 'default',
  loaderText,
  _isSelectable = false,
  _showLoader,
  _customLoader,
  _tableContainerClassName
}: TableProps<TItem, TId, TFilters>) => {
  // Get visible items from the getRows function
  const allItems = getRows();

  // Simple loading/error states without complex query logic
  const isFetching = isLoading;
  const isPending = isLoading;

  const LOADING_STRATEGIES_TO_KEEP_DATA: (typeof loadingStrategy)[] = [
    'optimistic',
    'alwaysShowData'
  ];

  const shouldHideDataOnFetching = LOADING_STRATEGIES_TO_KEEP_DATA.includes(
    loadingStrategy
  )
    ? !allItems.length && isFetching
    : isFetching;

  const pageModel =
    isError || shouldHideDataOnFetching || isPending
      ? undefined
      : { items: allItems };
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [hasContentOverflow, setHasContentOverflow] = useState(false);

  // Simple overflow check - find the actual scrollable container
  const checkContentOverflow = useCallback(() => {
    if (!stickyButton) return setHasContentOverflow(false);

    const scrollable = scrollContainerRef.current?.parentElement; // The scrollable div
    if (!scrollable) return setHasContentOverflow(false);

    const hasOverflow = scrollable.scrollWidth > scrollable.clientWidth;
    const isAtEnd =
      scrollable.scrollLeft + scrollable.clientWidth >=
      scrollable.scrollWidth - 1;
    setHasContentOverflow(hasOverflow && !isAtEnd);
  }, [stickyButton]);

  // Set up scroll and resize listeners
  useEffect(() => {
    if (!stickyButton) return;

    const scrollable = scrollContainerRef.current?.parentElement;
    if (!scrollable) return;

    checkContentOverflow();
    scrollable.addEventListener('scroll', checkContentOverflow);
    window.addEventListener('resize', checkContentOverflow);

    return () => {
      scrollable.removeEventListener('scroll', checkContentOverflow);
      window.removeEventListener('resize', checkContentOverflow);
    };
  }, [checkContentOverflow, stickyButton]);

  const visibleItems = useMemo(() => {
    if (!pageModel?.items) return [];
    return pageModel.items.filter((item: TItem) => {
      if (isFoldedByDefault) {
        return !isFoldedByDefault(
          item,
          dataSourceState as DataSourceState<TFilters, TId>
        );
      }
      return true;
    });
  }, [pageModel?.items, isFoldedByDefault, dataSourceState]);

  useEffect(() => {
    onVisibleRowsCountChanged?.(visibleItems.length);
  }, [visibleItems.length, onVisibleRowsCountChanged]);

  const scrollbarClassNames =
    'scrollbar scrollbar-thumb-scroll_bar-bar-rested [&::-webkit-scrollbar-thumb]:rounded-xl [&::-webkit-scrollbar-thumb]:scrollbar-thumb-scroll_bar-bar-rested [&::-webkit-scrollbar]:h-4';

  const showScroll = !scrollbarDisabled || !isLoading || !isError;

  return (
    <div
      className={cn('rounded-t-xl bg-surface-grey_0', classNames)}
      style={{
        ...styles,
        overflow: 'hidden'
      }}
      data-attributes={dataAttributes}
    >
      <div className="flex flex-row">
        <div
          className={cn(
            'w-full flex-col overflow-x-auto pb-12',
            !showScroll
              ? scrollbarClassNames
              : 'overflow-x-hidden scrollbar-none'
          )}
        >
          <TableHeader
            height={
              typeof headerSize === 'number'
                ? headerSize
                : parseInt(String(headerSize)) || 40
            }
            columns={columns}
            columnGroups={columnGroups}
            dataSourceState={dataSourceState}
            setDataSourceState={setDataSourceState}
            stickyButton={stickyButton}
          />

          <div ref={scrollContainerRef} className={'flex w-full flex-col'}>
            {(isFetching || isPending) && loadingStrategy !== 'optimistic' ? (
              loadingStrategy === 'alwaysShowData' ? (
                <div className="absolute inset-0 z-50 flex items-center justify-center bg-[#ffffff63]">
                  <Loader
                    className="flex min-h-[566px] w-full items-center justify-center"
                    description={
                      loaderText ? `Please wait, ${loaderText}` : undefined
                    }
                  />
                </div>
              ) : (
                <Loader
                  className="flex min-h-[566px] w-full items-center justify-center"
                  description={
                    loaderText ? `Please wait, ${loaderText}` : undefined
                  }
                />
              )
            ) : isError ? (
              <div
                className={cn(
                  'flex min-h-[566px] w-full items-center justify-center',
                  errorContainerClassName
                )}
                data-attributes="TableError"
              >
                {renderError ? (
                  renderError()
                ) : (
                  <IllustrationMessage
                    illustrationVariant={'Error'}
                    title="Sorry, an error has occurred"
                    description="We can't seem to find the page you are looking for"
                  />
                )}
              </div>
            ) : !visibleItems.length ? (
              <div className="flex min-h-[566px] w-full items-center justify-center">
                {renderNoResultsBlock ? (
                  renderNoResultsBlock()
                ) : (
                  <IllustrationMessage
                    illustrationVariant="NothingFound"
                    description={
                      <p>
                        You are all caught up!
                        <br />
                        No new items here!
                      </p>
                    }
                    title="Sorry, no results found"
                  />
                )}
              </div>
            ) : (
              visibleItems.map((item: TItem, index: number) => {
                const itemId = (item as Record<string, unknown>).id as TId;
                const rowOptions = getRowOptions?.(item, index);

                return (
                  <TableRow
                    key={`${itemId}-${index}`}
                    item={item}
                    index={index}
                    columns={columns}
                    rowOptions={rowOptions}
                    onRowClick={onRowClick}
                    renderRowContent={renderRowContent}
                    withCustomRow={withCustomRow}
                    rowDataAttributes={rowDataAttributes}
                    getCustomRowExtraOptions={getCustomRowExtraOptions}
                    stickyButton={stickyButton}
                  />
                );
              })
            )}
          </div>
        </div>
        {/* Sticky Button Column Header */}
        {stickyButton && !isLoading && !isError && (
          <div
            className="flex h-full flex-col"
            style={{
              ...(hasContentOverflow && {
                boxShadow: '-16px 0px 16px 0px #00000014'
              })
            }}
          >
            <div
              className="right-0 z-10 flex h-full rounded-tr-xl bg-surface-grey_20"
              style={{
                height: columnGroups
                  ? (typeof headerSize === 'number'
                      ? headerSize
                      : parseInt(String(headerSize)) || 40) * 2
                  : typeof headerSize === 'number'
                    ? headerSize
                    : parseInt(String(headerSize)) || 40,
                // 56px is width of container with paddings if 40px button is used
                width: stickyButton.width || '56px',
                minWidth: stickyButton.width || '56px'
              }}
            ></div>
            {/* Sticky Button Column */}
            <div
              key={'sticky-button'}
              className={cn(
                'flex flex-1 flex-col',
                stickyButton?.backgroundColor
              )}
            >
              {visibleItems.map((item: TItem, index: number) => (
                <div
                  key={`sticky-button-${index}`}
                  className="flex min-h-[67px] items-center border-b border-divider-mid"
                >
                  {stickyButton.render(item, index)}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
