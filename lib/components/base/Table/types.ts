import { Dispatch, ReactNode, SetStateAction } from 'react';

export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status:
    | 'active'
    | 'inactive'
    | 'pending'
    | {
        id: string;
        name: string;
      };
  joinDate: string;
  department: string;
  // Extended properties for mockColumns compatibility
  employee?: {
    email?: string;
    fullNameEnglish?: string;
    companyName?: string;
    positionName?: string;
  };
  tags?: string[];
  date?: string;
  topic?: string;
  subject?: {
    title?: string;
  };
  rating?: number;
  feedbackText?: string;
  requestDetails?: string;
}

export enum ColumnGroup {
  AtrForm = 'AtrForm',
  AtrTemplate = 'AtrTemplate',
  AtrFormStatus = 'AtrFormStatus',
  Employee = 'Employee',
  ATRGroup = 'ATRGroup',
  AssessmentManagerLM = 'AssessmentManagerLM',
  LastUpdated = 'LastUpdated',
  UpdatedBy = 'UpdatedBy'
}

export enum Column {
  AtrFormId = 'AtrFormId',
  AtrTemplateName = 'AtrTemplateName',
  AtrFormMajorStatus = 'AtrFormMajorStatus',
  AtrFormMinorStatus = 'AtrFormMinorStatus',
  EmployeeID = 'EmployeeID',
  EmployeeFullName = 'EmployeeFullName',
  EmployeeCompanyName = 'EmployeeCompanyName',
  ATRGroupName = 'ATRGroupName',
  AssessmentLineManagerID = 'AssessmentLineManagerID',
  AssessmentLineManagerFullName = 'AssessmentLineManagerFullName',
  LastUpdated = 'LastUpdated',
  UpdatedBy = 'UpdatedBy'
}

// Column Groups Example
export interface SampleData {
  id: number;
  name: string;
  email: string;
  department: string;
  active: boolean;
  status: 'Active' | 'Inactive' | 'Pending';
}

// Types for React Query (simplified)
export interface UseQueryResult<TData> {
  data?: TData;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
}

// Types
export interface PageModel<T> {
  items: T[];
  totalCount: number;
  pageNumber: number;
  pageSize: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface TableDataSourceState<_TId, TFilters = unknown> {
  pageNumber: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: TFilters;
}

export interface DataSourceState<TFilters, _TId> {
  pageNumber: number;
  pageSize: number;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  filters?: TFilters;
}

export interface DataColumnProps<TItem, _TId = unknown> {
  key: string;
  title?: string;
  caption?: string;
  sortable?: boolean;
  width?: number;
  minWidth?: string | number;
  grow?: number;
  render?: (item: TItem, index: number) => ReactNode;
  className?: string;
  headerClassName?: string;
  canCopy?: boolean;
  fixed?: 'left' | 'right';
}

export interface StickyButtonProps<TItem> {
  render: (item: TItem, index: number) => ReactNode;
  backgroundColor?: string;
  width: number;
  minWidth?: string | number;
}

export interface DataColumnGroupProps {
  title: string;
  columns: string[];
  className?: string;
}

export interface DataRowProps<TItem, TId> {
  item: TItem;
  index: number;
  id: TId;
}

export interface DataRowOptions<_TItem, _TId> {
  className?: string;
  disabled?: boolean;
  folded?: boolean;
  customData?: Record<string, unknown>;
}

export type NonSortable<T> = T;

export type CustomRowExtraOptionsGetter<TItem> = (
  item: TItem,
  index?: number
) =>
  | {
      tooltipContent?: ReactNode;
      tooltipClassName?: string;
    }
  | undefined;

export type TableLoadingStrategy = 'optimistic' | 'alwaysShowData' | 'default';

// Additional interfaces for DataTableCore
export interface IEditable<T> {
  value: T;
  onValueChange: (value: T) => void;
}

export interface IHasCX {
  cx?: string;
}

export interface DataSourceListProps {
  [key: string]: unknown;
}

export interface DataTableColumnsConfigOptions {
  [key: string]: unknown;
}

export interface DataTableState {
  // Add table state properties
  visibleColumnsIds?: string[];
  columnsConfig?: Record<string, unknown>;
}

export interface DataTableRowProps<TItem, TId> {
  item: TItem;
  id: TId;
  index: number;
  columns: DataColumnProps<TItem, TId>[];
}

export interface TableFiltersConfig<TFilter> {
  columnKey: string;
  filter: TFilter;
}

export interface DataTableSelectedCellData<TItem, TId, TFilter> {
  item: TItem;
  id: TId;
  columnKey: string;
  value: unknown;
  filter?: TFilter;
}

export interface ColumnsConfigurationModalProps<TItem, TId, _TFilter> {
  columns: DataColumnProps<TItem, TId>[];
  onClose: () => void;
  onApply: (config: unknown) => void;
}

export interface DataTableFocusManager<TId> {
  focusedId?: TId;
  setFocusedId: (id: TId) => void;
}

export interface DataTableCoreProps<TItem, TId, TFilter = unknown>
  extends IEditable<DataTableState>,
    IHasCX,
    DataSourceListProps,
    DataTableColumnsConfigOptions {
  getRows?(): DataRowProps<TItem, TId>[];
  rows?: DataRowProps<TItem, TId>[];
  columnGroups?: DataColumnGroupProps[];
  columns: DataColumnProps<TItem, TId>[];
  renderRow?(props: DataTableRowProps<TItem, TId>): React.ReactNode;
  renderNoResultsBlock?(): React.ReactNode;
  showColumnsConfig?: boolean;
  filters?: TableFiltersConfig<TFilter>[];
  onCopy?: (
    copyFrom: DataTableSelectedCellData<TItem, TId, TFilter>,
    selectedCells: DataTableSelectedCellData<TItem, TId, TFilter>[]
  ) => void;
  renderColumnsConfigurationModal?: (
    props: ColumnsConfigurationModalProps<TItem, TId, TFilter>
  ) => React.ReactNode;
  dataTableFocusManager?: DataTableFocusManager<TId>;
  showFoldAll?: boolean;
}

export type TableProps<TItem, TId, TFilters = unknown> = {
  /** Current state of the data source including pagination and sorting */
  dataSourceState: TableDataSourceState<TId, TFilters>;
  /** Function to update the data source state */
  setDataSourceState: Dispatch<
    SetStateAction<TableDataSourceState<TId, TFilters>>
  >;
  /** Function to get visible rows data */
  getRows: () => TItem[];
  /** Column configurations for the table */
  columns: DataColumnProps<TItem, TId>[];
  /** Optional column groups for organizing headers */
  columnGroups?: DataColumnGroupProps[];
  /** Custom render function for individual rows */
  renderRowContent?: (item: TItem, index: number) => React.ReactNode;
  /** Custom error state render function */
  renderError?: () => ReactNode;
  /** Custom no results state render function */
  renderNoResultsBlock?: () => ReactNode;
  /** Callback function when a row is clicked */
  onRowClick?: (id: TId, props: DataRowProps<TItem, TId>) => void;
  /** Function to get row-specific options like disabled state, className, etc. */
  getRowOptions?: (
    item: TItem,
    index?: number
  ) => DataRowOptions<NonSortable<TItem>, TId>;
  /** Function to determine if rows should be folded by default */
  isFoldedByDefault?(
    item: NonSortable<TItem>,
    state: DataSourceState<TFilters, TId>
  ): boolean;
  /** Header height */
  headerSize?: string | number;
  /** Gap between columns */
  _columnsGap?: string | number;
  /** Loading state */
  isLoading?: boolean;
  /** Error state */
  isError?: boolean;
  /** Error object */
  _error?: unknown;
  _isSelectable?: boolean;
  showPagination?: boolean;
  _showLoader?: boolean;
  _customLoader?: React.ReactNode;
  _tableContainerClassName?: string;
  styles?: {
    minHeight?: number | string;
    maxWidth?: number | string;
  };
  dataAttributes?: string;
  classNames?: string;
  paginationContainerClassName?: string;
  errorContainerClassName?: string;
  paginationSize?: '24' | '30';
  onVisibleRowsCountChanged?: (visibleRows: number | undefined) => void;
  loadingStrategy?: TableLoadingStrategy;
  loaderText?: string;
  setPageSize?: (pageSize: number) => void;
  pageSizes?: number[];
  stickyButton?: StickyButtonProps<TItem>;
  scrollbarDisabled?: boolean;
} & (
  | {
      withCustomRow: true;
      rowDataAttributes?: string;
      getCustomRowExtraOptions?: CustomRowExtraOptionsGetter<TItem>;
    }
  | {
      withCustomRow?: never;
      rowDataAttributes?: never;
      getCustomRowExtraOptions?: never;
    }
);
