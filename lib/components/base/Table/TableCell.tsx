import { FC, PropsWithChildren } from 'react';

import { DataAttributesProps } from '../types';

import { cn } from '../../../utils';

type TableCellProps = PropsWithChildren &
  DataAttributesProps & {
    classNames?: string;
  };

export const TableCell: FC<TableCellProps> = ({
  children,
  classNames,
  dataAttributes = 'TableCell'
}) => {
  return (
    <span
      data-attributes={dataAttributes}
      className={cn(
        'flex flex-auto items-center justify-start pl-4 text-body-2-regular text-input-text-filled',
        classNames
      )}
    >
      {children}
    </span>
  );
};
