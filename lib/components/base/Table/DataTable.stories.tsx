import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import { DataTable } from './DataTable';
import {
  groups,
  mockColumns,
  mockUsers,
  performanceFormsTablecolumns
} from './mockTables';
import {
  PerformanceFormData,
  performanceFormsData
} from './performanceFormsData';
import { TableDataSourceState, User } from './types';
import { useColumnGroups } from './useColumnGroups';

import { ButtonSize, ButtonVariant, IconButton } from '../Button';

const meta: Meta<typeof DataTable> = {
  title: 'Design System/Components/DataTable/DataTable',
  component: DataTable,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A complete data table component with built-in pagination and page size selection. Extends the base Table component with pagination controls.'
      }
    }
  },
  argTypes: {
    showPagination: {
      description: 'Whether to show pagination controls',
      control: { type: 'boolean' }
    },
    showPageSizeSelector: {
      description: 'Whether to show page size selector dropdown',
      control: { type: 'boolean' }
    },
    pageSizes: {
      description: 'Available page sizes as an array of numbers',
      control: { type: 'object' }
    },
    containerClassName: {
      description: 'CSS classes for the container',
      control: { type: 'text' }
    }
  },
  args: {
    showPagination: true,
    showPageSizeSelector: true,
    containerClassName: ''
  },
  tags: ['autodocs']
};

export default meta;
type Story = StoryObj<typeof meta>;

interface StoryArgs {
  showPagination: boolean;
  showPageSizeSelector: boolean;
  containerClassName: string;
  isLoading?: boolean;
  isError?: boolean;
}

const DefaultDataTable = (args: StoryArgs) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, undefined>
  >({
    pageNumber: 1,
    pageSize: 10, // Use default from DEFAULT_PAGE_SIZE_OPTIONS
    sortBy: undefined,
    sortDirection: 'asc',
    filters: undefined
  });

  const isLoading = args.isLoading ?? false;
  const isError = args.isError ?? false;

  const queryResult = {
    data: isError
      ? undefined
      : {
          items: mockUsers.slice(
            (dataSourceState.pageNumber - 1) * dataSourceState.pageSize,
            dataSourceState.pageNumber * dataSourceState.pageSize
          ),
          totalCount: mockUsers.length,
          pageNumber: dataSourceState.pageNumber,
          pageSize: dataSourceState.pageSize,
          hasNextPage:
            dataSourceState.pageNumber * dataSourceState.pageSize <
            mockUsers.length,
          hasPreviousPage: dataSourceState.pageNumber > 1
        },
    isLoading,
    isError,
    error: isError ? ({ message: 'Failed to load data' } as Error) : null
  };

  return (
    <DataTable<User, string, undefined>
      queryResult={queryResult}
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      columns={mockColumns}
      pageSizes={[10, 25, 50]}
      showPagination={args.showPagination}
      showPageSizeSelector={args.showPageSizeSelector}
      containerClassName={args.containerClassName}
    />
  );
};

const CustomPageSizeDataTable = (args: StoryArgs) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, undefined>
  >({
    pageNumber: 1,
    pageSize: 5, // This will be corrected to match pageSizes={[5, 15, 30]}
    sortBy: undefined,
    sortDirection: 'asc',
    filters: undefined
  });

  const isLoading = args.isLoading ?? false;
  const isError = args.isError ?? false;

  const queryResult = {
    data: isError
      ? undefined
      : {
          items: mockUsers.slice(
            (dataSourceState.pageNumber - 1) * dataSourceState.pageSize,
            dataSourceState.pageNumber * dataSourceState.pageSize
          ),
          totalCount: mockUsers.length,
          pageNumber: dataSourceState.pageNumber,
          pageSize: dataSourceState.pageSize,
          hasNextPage:
            dataSourceState.pageNumber * dataSourceState.pageSize <
            mockUsers.length,
          hasPreviousPage: dataSourceState.pageNumber > 1
        },
    isLoading,
    isError,
    error: isError ? ({ message: 'Failed to load data' } as Error) : null
  };

  return (
    <DataTable<User, string, undefined>
      queryResult={queryResult}
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      columns={mockColumns}
      pageSizes={[5, 15, 30]}
      showPagination={args.showPagination}
      showPageSizeSelector={args.showPageSizeSelector}
      containerClassName={args.containerClassName}
    />
  );
};

export const Default: Story = {
  render: (args) => <DefaultDataTable {...(args as StoryArgs)} />
};

export const WithCustomPageSizeOptions: Story = {
  render: (args) => <CustomPageSizeDataTable {...(args as StoryArgs)} />,
  parameters: {
    docs: {
      description: {
        story:
          'DataTable with custom page size options (1, 2, 3 items per page) to demonstrate how pagination works with different page sizes.'
      }
    }
  }
};

export const WithoutPagination: Story = {
  render: (args) => <DefaultDataTable {...(args as StoryArgs)} />,
  args: {
    showPagination: false
  },
  parameters: {
    docs: {
      description: {
        story:
          'DataTable without pagination controls, showing all data in a single view.'
      }
    }
  }
};

export const WithoutPageSizeSelector: Story = {
  render: (args) => <DefaultDataTable {...(args as StoryArgs)} />,
  args: {
    showPageSizeSelector: false
  },
  parameters: {
    docs: {
      description: {
        story:
          'DataTable with pagination but without the page size selector dropdown.'
      }
    }
  }
};

export const Loading: Story = {
  render: (args) => <DefaultDataTable {...(args as StoryArgs)} />,
  args: {
    isLoading: true
  },
  parameters: {
    docs: {
      description: {
        story:
          'DataTable in loading state, demonstrating how the component handles async data loading.'
      }
    }
  }
};

export const Error: Story = {
  render: (args) => <DefaultDataTable {...(args as StoryArgs)} />,
  args: {
    isError: true
  },
  parameters: {
    docs: {
      description: {
        story:
          'DataTable in error state, demonstrating how the component handles data loading errors.'
      }
    }
  }
};

const ATRPerformanceFormsDataTable = (args: StoryArgs) => {
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<string, undefined>
  >({
    pageNumber: 1,
    pageSize: 3,
    sortBy: undefined,
    sortDirection: 'asc',
    filters: undefined
  });

  const { columnGroups } = useColumnGroups({
    columns: performanceFormsTablecolumns,
    groups: groups
  });

  const isLoading = args.isLoading ?? false;
  const isError = args.isError ?? false;

  const queryResult = {
    data: isError
      ? undefined
      : {
          items: performanceFormsData.items.slice(
            (dataSourceState.pageNumber - 1) * dataSourceState.pageSize,
            dataSourceState.pageNumber * dataSourceState.pageSize
          ),
          totalCount: performanceFormsData.items.length,
          pageNumber: dataSourceState.pageNumber,
          pageSize: dataSourceState.pageSize,
          hasNextPage:
            dataSourceState.pageNumber * dataSourceState.pageSize <
            performanceFormsData.items.length,
          hasPreviousPage: dataSourceState.pageNumber > 1
        },
    isLoading,
    isError,
    error: isError ? ({ message: 'Failed to load data' } as Error) : null
  };

  return (
    <DataTable<PerformanceFormData, string, undefined>
      queryResult={queryResult}
      dataSourceState={dataSourceState}
      setDataSourceState={setDataSourceState}
      columns={performanceFormsTablecolumns}
      columnGroups={columnGroups}
      pageSizes={[3, 5, 10, 15]}
      showPagination={args.showPagination}
      showPageSizeSelector={args.showPageSizeSelector}
      containerClassName={args.containerClassName}
      stickyButton={{
        width: 56,
        render: (item) => (
          <div className="flex w-full flex-row items-center justify-center gap-8">
            <IconButton
              icon="Setting_line"
              variant={ButtonVariant.Tertiary}
              size={ButtonSize.Medium}
              onClick={() => {
                const performanceItem = item as {
                  employee: { fullNameEnglish: string };
                };
                alert(
                  `Settings for: ${performanceItem.employee.fullNameEnglish}`
                );
              }}
              tooltip={{ title: 'Employee settings' }}
            />
          </div>
        )
      }}
    />
  );
};

export const ATRPerformanceFormsWithPagination: Story = {
  name: 'ATR Performance Forms with Pagination',
  render: (args) => <ATRPerformanceFormsDataTable {...(args as StoryArgs)} />,
  args: {
    showPagination: true,
    showPageSizeSelector: true
  },
  parameters: {
    docs: {
      description: {
        story:
          'Real-world example showing ATR Performance Forms data with full pagination controls, page size dropdown, and sticky action buttons. Demonstrates complex table with column groups, comprehensive data structure, pagination functionality, and row-level actions.'
      }
    }
  }
};
