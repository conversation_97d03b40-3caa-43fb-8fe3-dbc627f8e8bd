import type { Meta, StoryObj } from '@storybook/react';

import { TableSelectCell } from './TableSelectCell';

const meta: Meta<typeof TableSelectCell> = {
  title: 'Design System/Components/Table/TableSelectCell',
  component: TableSelectCell,
  parameters: {
    layout: 'centered'
  },
  argTypes: {
    size: {
      control: { type: 'select' },
      options: ['Small', 'Medium', 'Large']
    },
    disabled: {
      control: { type: 'boolean' }
    },
    showClear: {
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

const sampleOptions = [
  { id: 1, title: 'Option 1' },
  { id: 2, title: 'Option 2' },
  { id: 3, title: 'Option 3' }
];

export const Default: Story = {
  args: {
    options: sampleOptions,
    placeholder: 'Select an option...'
  }
};

export const WithValue: Story = {
  args: {
    options: sampleOptions,
    value: sampleOptions[0],
    placeholder: 'Select an option...'
  }
};

export const Disabled: Story = {
  args: {
    options: sampleOptions,
    placeholder: 'Select an option...',
    disabled: true
  }
};

export const WithoutClear: Story = {
  args: {
    options: sampleOptions,
    value: sampleOptions[1],
    placeholder: 'Select an option...',
    showClear: false
  }
};

export const LargeSize: Story = {
  args: {
    options: sampleOptions,
    placeholder: 'Select an option...',
    size: 'Large'
  }
};
