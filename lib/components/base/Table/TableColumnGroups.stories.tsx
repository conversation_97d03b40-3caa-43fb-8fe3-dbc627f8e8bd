import { useState } from 'react';

import type { <PERSON>a, StoryObj } from '@storybook/react';

import { Table } from './Table';
import { TableCheckboxCell } from './TableCheckboxCell';
import { TableInputCell } from './TableInputCell';
import { TableSelectCell } from './TableSelectCell';
import type {
  DataColumnGroupProps,
  DataColumnProps,
  TableDataSourceState
} from './types';
import { createColumnGroups, useColumnGroups } from './useColumnGroups';

// Sample data type
interface Employee {
  id: number;
  name: string;
  email: string;
  department: string;
  position: string;
  salary: number;
  startDate: string;
  isActive: boolean;
  skills: string[];
  performance: 'Excellent' | 'Good' | 'Average' | 'Poor';
}

// Sample data
const sampleEmployees: Employee[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Engineering',
    position: 'Senior Developer',
    salary: 95000,
    startDate: '2022-01-15',
    isActive: true,
    skills: ['React', 'TypeScript', 'Node.js'],
    performance: 'Excellent'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Design',
    position: 'UX Designer',
    salary: 78000,
    startDate: '2021-06-20',
    isActive: true,
    skills: ['Figma', 'User Research', 'Prototyping'],
    performance: 'Good'
  },
  {
    id: 3,
    name: 'Mike Davis',
    email: '<EMAIL>',
    department: 'Marketing',
    position: 'Marketing Manager',
    salary: 85000,
    startDate: '2020-03-10',
    isActive: false,
    skills: ['SEO', 'Content Strategy', 'Analytics'],
    performance: 'Average'
  }
];

const performanceOptions = [
  { id: 'excellent', title: 'Excellent' },
  { id: 'good', title: 'Good' },
  { id: 'average', title: 'Average' },
  { id: 'poor', title: 'Poor' }
];

// Demo component that uses the hook
const TableWithColumnGroups = ({
  showGroups = true,
  customGroups
}: {
  showGroups?: boolean;
  customGroups?: DataColumnGroupProps[];
}) => {
  const [employees, setEmployees] = useState(sampleEmployees);
  const [dataSourceState, setDataSourceState] = useState<
    TableDataSourceState<number, unknown>
  >({
    pageNumber: 1,
    pageSize: 10
  });

  // Define columns
  const columns: DataColumnProps<Employee>[] = [
    {
      key: 'isActive',
      title: 'Active',
      width: 80,
      render: (employee, index) => (
        <TableCheckboxCell
          checked={employee.isActive}
          onChange={(checked) => {
            const newEmployees = [...employees];
            newEmployees[index] = { ...employee, isActive: checked };
            setEmployees(newEmployees);
          }}
        />
      )
    },
    {
      key: 'name',
      title: 'Full Name',
      width: 200,
      grow: 1,
      sortable: true,
      render: (employee) => employee.name
    },
    {
      key: 'email',
      title: 'Email Address',
      width: 250,
      grow: 1,
      render: (employee, index) => (
        <TableInputCell
          type="email"
          value={employee.email}
          placeholder="Enter email"
          onChange={(value) => {
            const newEmployees = [...employees];
            newEmployees[index] = { ...employee, email: value };
            setEmployees(newEmployees);
          }}
        />
      )
    },
    {
      key: 'department',
      title: 'Department',
      width: 150,
      render: (employee) => employee.department
    },
    {
      key: 'position',
      title: 'Position',
      width: 180,
      render: (employee, index) => (
        <TableInputCell
          value={employee.position}
          placeholder="Enter position"
          onChange={(value) => {
            const newEmployees = [...employees];
            newEmployees[index] = { ...employee, position: value };
            setEmployees(newEmployees);
          }}
        />
      )
    },
    {
      key: 'salary',
      title: 'Salary',
      width: 120,
      grow: 1,
      sortable: true,
      render: (employee, index) => (
        <TableInputCell
          type="number"
          value={employee.salary.toString()}
          placeholder="Enter salary"
          onChange={(value) => {
            const newEmployees = [...employees];
            newEmployees[index] = { ...employee, salary: parseInt(value) || 0 };
            setEmployees(newEmployees);
          }}
        />
      )
    },
    {
      key: 'startDate',
      title: 'Start Date',
      width: 120,
      sortable: true,
      render: (employee) => new Date(employee.startDate).toLocaleDateString()
    },
    {
      key: 'performance',
      title: 'Performance',
      width: 150,
      grow: 1,
      render: (employee, index) => (
        <TableSelectCell
          options={performanceOptions}
          value={performanceOptions.find(
            (opt) => opt.title === employee.performance
          )}
          placeholder="Select performance"
          onChange={(option) => {
            if (option) {
              const newEmployees = [...employees];
              newEmployees[index] = {
                ...employee,
                performance: option.title as Employee['performance']
              };
              setEmployees(newEmployees);
            }
          }}
        />
      )
    }
  ];

  // Define column groups
  const defaultGroups = createColumnGroups([
    {
      title: 'Employee Info',
      columnKeys: ['isActive', 'name', 'email'],
      className: 'bg-blue-50 border-blue-200'
    },
    {
      title: 'Job Details',
      columnKeys: ['department', 'position', 'salary'],
      className: 'bg-green-50 border-green-200'
    },
    {
      title: 'Performance & History',
      columnKeys: ['startDate', 'performance'],
      className: 'bg-purple-50 border-purple-200'
    }
  ]);

  // Use the hook
  const { columnGroups } = useColumnGroups({
    columns,
    groups: showGroups ? customGroups || defaultGroups : undefined
  });

  return (
    <div style={{ width: '100%', maxWidth: '1200px' }}>
      <Table<Employee, number, unknown>
        dataSourceState={dataSourceState}
        setDataSourceState={setDataSourceState}
        getRows={() => employees}
        columns={columns}
        columnGroups={columnGroups}
        onRowClick={(id, props) => {
          alert(`Row clicked: ${id}, ${JSON.stringify(props)}`);
        }}
      />
    </div>
  );
};

const meta: Meta<typeof TableWithColumnGroups> = {
  title: 'Design System/Components/Table/ColumnGroups',
  component: TableWithColumnGroups,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
## Column Groups Hook Demo

This story demonstrates the \`useColumnGroups\` hook functionality with interactive table cells.

### Features:
- **Column Grouping**: Organizes related columns under group headers
- **Interactive Cells**: Includes checkbox, input, and select cell types
- **Custom Styling**: Each group can have custom CSS classes
- **Flexible Configuration**: Groups can be enabled/disabled dynamically

### Usage:
\`\`\`tsx
import { useColumnGroups, createColumnGroups } from '@/hooks';

const groups = createColumnGroups([
  {
    title: 'Employee Info',
    columnKeys: ['isActive', 'name', 'email'],
    className: 'bg-blue-50'
  }
]);

const { columnGroups } = useColumnGroups({ columns, groups });
\`\`\`
        `
      }
    }
  },
  argTypes: {
    showGroups: {
      control: { type: 'boolean' },
      description: 'Toggle column groups on/off'
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    showGroups: true
  }
};

export const WithoutGroups: Story = {
  args: {
    showGroups: false
  }
};

export const CustomGroups: Story = {
  args: {
    showGroups: true,
    customGroups: createColumnGroups([
      {
        title: 'Basic Info',
        columnKeys: ['isActive', 'name', 'department'],
        className: 'bg-orange-50 border-orange-200'
      },
      {
        title: 'Contact & Role',
        columnKeys: ['email', 'position'],
        className: 'bg-cyan-50 border-cyan-200'
      },
      {
        title: 'Compensation & Performance',
        columnKeys: ['salary', 'startDate', 'performance'],
        className: 'bg-pink-50 border-pink-200'
      }
    ])
  }
};

// Story showing just the hook usage without the full table
export const HookUsageExample = () => {
  const columns: DataColumnProps<Employee>[] = [
    { key: 'name', title: 'Name' },
    { key: 'email', title: 'Email' },
    { key: 'department', title: 'Department' },
    { key: 'position', title: 'Position' }
  ];

  const groups = createColumnGroups([
    {
      title: 'Personal',
      columnKeys: ['name', 'email']
    },
    {
      title: 'Work',
      columnKeys: ['department', 'position']
    }
  ]);

  const { columnGroups, hasGroups, groupedColumns, ungroupedColumns } =
    useColumnGroups({
      columns,
      groups
    });

  return (
    <div className="space-y-4 p-4">
      <div>
        <h3 className="mb-2 text-lg font-semibold">Hook Results:</h3>
        <div className="space-y-2">
          <p>
            <strong>Has Groups:</strong> {hasGroups ? 'Yes' : 'No'}
          </p>
          <p>
            <strong>Number of Groups:</strong> {columnGroups?.length || 0}
          </p>
          <p>
            <strong>Grouped Columns:</strong> {groupedColumns.length}
          </p>
          <p>
            <strong>Ungrouped Columns:</strong> {ungroupedColumns.length}
          </p>
        </div>
      </div>

      <div>
        <h4 className="mb-2 font-semibold">Column Groups:</h4>
        <div className="space-y-1">
          {columnGroups?.map((group, index) => (
            <div key={index} className="p-2 rounded bg-gray-100">
              <strong>{group.title}:</strong> {group.columns.join(', ')}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
