/* eslint-disable @typescript-eslint/no-explicit-any */
import { PerformanceFormData } from './performanceFormsData';
import { TableCell } from './TableCell';
import { TableTextCell } from './TableTextCell';
import { TableUserInfoCell } from './TableUserInfoCell';
import {
  Column,
  ColumnGroup,
  SampleData,
  User,
  type DataColumnProps
} from './types';
import { createColumnGroups } from './useColumnGroups';
import {
  formatDateTime,
  getColumnDisplayName,
  getColumnGroupDisplayName,
  getTagColor
} from './utils';

import { StarRating, StarRatingSize } from '../StarRating';
import { Tag, TagVariant } from '../Tag';
import { Text } from '../Text';

export const sampleData: SampleData[] = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Engineering',
    active: true,
    status: 'Active'
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Design',
    active: false,
    status: 'Inactive'
  },
  {
    id: 3,
    name: '<PERSON>',
    email: '<EMAIL>',
    department: 'Product',
    active: true,
    status: 'Pending'
  }
];

export const columnsSamples: DataColumnProps<SampleData>[] = [
  {
    key: 'active',
    title: 'Active',
    width: 80,
    render: (item) => (item.active ? 'Yes' : 'No')
  },
  {
    key: 'name',
    title: 'Name',
    width: 150,
    render: (item) => item.name
  },
  {
    key: 'email',
    title: 'Email',
    width: 200,
    grow: 1,
    render: (item) => item.email
  },
  {
    key: 'department',
    title: 'Department',
    width: 150,
    render: (item) => item.department
  },
  {
    key: 'status',
    title: 'Status',
    width: 120,
    grow: 1,
    render: (item) => (
      <span
        className={`py-2 inline-block rounded px-8 text-xs font-medium ${
          item.status === 'Active'
            ? 'bg-success-background text-success-default'
            : item.status === 'Inactive'
              ? 'bg-error-background text-error-default'
              : 'bg-warning-background text-warning-default'
        }`}
      >
        {item.status}
      </span>
    )
  }
];

export const performanceFormsTablecolumns: DataColumnProps<PerformanceFormData>[] =
  [
    // ATR Form columns
    {
      key: Column.AtrFormId,
      title: getColumnDisplayName(Column.AtrFormId),
      width: 100,
      render: (item) => (
        <TableTextCell classNames="overflow-hidden" lineClamp={2}>
          {item.id.slice(-8)}
        </TableTextCell>
      )
    },
    // ATR Template columns
    {
      key: Column.AtrTemplateName,
      title: getColumnDisplayName(Column.AtrTemplateName),
      width: 250,
      grow: 1,
      render: (item) => (
        <TableTextCell classNames="overflow-hidden" lineClamp={2}>
          {item.templateName}
        </TableTextCell>
      )
    },
    // ATR Form Status columns
    {
      key: Column.AtrFormMajorStatus,
      title: getColumnDisplayName(Column.AtrFormMajorStatus),
      width: 140,
      render: (item) =>
        item.majorStatus ? (
          <Tag
            type="Light"
            variant={getTagColor(item.majorStatus) as TagVariant}
            size="Small"
            className="overflow-hidden"
          >
            <Text
              lineClamp={1}
              className="truncate whitespace-nowrap"
              tooltipMode="truncated"
            >
              {item.majorStatus}
            </Text>
          </Tag>
        ) : (
          <TableTextCell classNames="overflow-hidden w-40">-</TableTextCell>
        )
    },
    {
      key: Column.AtrFormMinorStatus,
      title: getColumnDisplayName(Column.AtrFormMinorStatus),
      width: 120,
      render: (item) => (
        <TableTextCell classNames="truncate whitespace-nowrap" lineClamp={2}>
          {item.minorStatus}
        </TableTextCell>
      )
    },
    // Employee columns
    {
      key: Column.EmployeeID,
      title: getColumnDisplayName(Column.EmployeeID),
      width: 100,
      render: (item) => (
        <TableTextCell classNames="truncate whitespace-nowrap" lineClamp={1}>
          {item.employee.employeeId}
        </TableTextCell>
      )
    },
    {
      key: Column.EmployeeFullName,
      title: getColumnDisplayName(Column.EmployeeFullName),
      width: 180,
      render: (item) => (
        <TableUserInfoCell
          className="p-0"
          fullName={item.employee.fullNameEnglish}
          email={item.employee.email}
          size="Small"
        />
      )
    },
    {
      key: Column.EmployeeCompanyName,
      title: getColumnDisplayName(Column.EmployeeCompanyName),
      width: 150,
      render: (item) => (
        <TableTextCell classNames="overflow-hidden" lineClamp={2}>
          {item.employee.companyName}
        </TableTextCell>
      )
    },
    // ATR Group columns
    {
      key: Column.ATRGroupName,
      title: getColumnDisplayName(Column.ATRGroupName),
      width: 200,
      render: (item) => (
        <TableTextCell classNames="overflow-hidden" lineClamp={2}>
          {item.atrGroupName}
        </TableTextCell>
      )
    },
    // Assessment Manager columns
    {
      key: Column.AssessmentLineManagerID,
      title: getColumnDisplayName(Column.AssessmentLineManagerID),
      width: 100,
      render: (item) => (
        <TableTextCell classNames="truncate whitespace-nowrap" lineClamp={1}>
          {item.assessmentLineManager.employeeId}
        </TableTextCell>
      )
    },
    {
      key: Column.AssessmentLineManagerFullName,
      title: getColumnDisplayName(Column.AssessmentLineManagerFullName),
      width: 180,
      render: (item) => (
        <TableUserInfoCell
          fullName={item.assessmentLineManager.fullNameEnglish}
          email={item.assessmentLineManager.email}
          size="Small"
        />
      )
    },
    // Last Updated columns
    {
      key: Column.LastUpdated,
      title: getColumnDisplayName(Column.LastUpdated),
      width: 140,
      render: (item) => (
        <TableTextCell classNames="overflow-hidden" lineClamp={2}>
          {formatDateTime(item?.lastUpdated || '')}
        </TableTextCell>
      )
    },
    // Updated By columns
    {
      key: Column.UpdatedBy,
      title: getColumnDisplayName(Column.UpdatedBy),
      width: 200,
      grow: 1,
      render: (item) => (
        <TableUserInfoCell
          className="p-0"
          fullName={item.assessmentLineManager.fullNameEnglish}
          email={item.assessmentLineManager.email}
          size="Small"
        >
          {item.updatedBy?.fullNameEnglish}
        </TableUserInfoCell>
      )
    }
  ];

// Create column groups based on your implementation
export const groups = createColumnGroups([
  {
    title: getColumnGroupDisplayName(ColumnGroup.AtrForm),
    columnKeys: [Column.AtrFormId]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.AtrTemplate),
    columnKeys: [Column.AtrTemplateName]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.AtrFormStatus),
    columnKeys: [Column.AtrFormMajorStatus, Column.AtrFormMinorStatus]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.Employee),
    columnKeys: [
      Column.EmployeeID,
      Column.EmployeeFullName,
      Column.EmployeeCompanyName
    ]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.ATRGroup),
    columnKeys: [Column.ATRGroupName]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.AssessmentManagerLM),
    columnKeys: [
      Column.AssessmentLineManagerID,
      Column.AssessmentLineManagerFullName
    ]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.LastUpdated),
    columnKeys: [Column.LastUpdated]
  },
  {
    title: getColumnGroupDisplayName(ColumnGroup.UpdatedBy),
    columnKeys: [Column.UpdatedBy]
  }
]);

// Mock User interface for demonstration - Extended to match mockColumns expectations

// Mock data - Extended to match mockColumns expectations
export const mockUsers: User[] = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'Admin',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-01-15',
    department: 'Engineering',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'John Doe',
      companyName: 'TechCorp Inc.',
      positionName: 'Senior Engineer'
    },
    tags: ['Senior', 'Lead'],
    date: '2023-01-15',
    topic: 'Performance Review',
    subject: { title: 'Q1 Performance Evaluation' },
    rating: 4.5,
    feedbackText: 'Excellent performance this quarter.',
    requestDetails: 'Request for promotion consideration.'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-02-20',
    department: 'Marketing',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Jane Smith',
      companyName: 'TechCorp Inc.',
      positionName: 'Marketing Manager'
    },
    tags: ['Manager', 'Creative'],
    date: '2023-02-20',
    topic: 'Campaign Review',
    subject: { title: 'Spring Campaign Analysis' },
    rating: 4.2,
    feedbackText: 'Great work on the recent campaign.',
    requestDetails: 'Request for budget increase.'
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    role: 'Moderator',
    status: { id: 'inactive', name: 'Inactive' },
    joinDate: '2023-03-10',
    department: 'Sales',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Bob Johnson',
      companyName: 'TechCorp Inc.',
      positionName: 'Sales Representative'
    },
    tags: ['Senior', 'B2B'],
    date: '2023-03-10',
    topic: 'Sales Review',
    subject: { title: 'Q1 Sales Performance' },
    rating: 3.8,
    feedbackText: 'Good sales numbers but needs improvement.',
    requestDetails: 'Request for additional training.'
  },
  {
    id: '4',
    name: 'Alice Brown',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'pending', name: 'Pending' },
    joinDate: '2023-04-05',
    department: 'HR',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Alice Brown',
      companyName: 'TechCorp Inc.',
      positionName: 'HR Specialist'
    },
    tags: ['New', 'Onboarding'],
    date: '2023-04-05',
    topic: 'Onboarding Review',
    subject: { title: 'New Employee Feedback' },
    rating: 4.0,
    feedbackText: 'Adapting well to the company culture.',
    requestDetails: 'Request for mentorship program.'
  },
  {
    id: '5',
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    role: 'Admin',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-05-12',
    department: 'Engineering',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Charlie Wilson',
      companyName: 'TechCorp Inc.',
      positionName: 'DevOps Engineer'
    },
    tags: ['DevOps', 'Infrastructure'],
    date: '2023-05-12',
    topic: 'Infrastructure Review',
    subject: { title: 'Cloud Migration Progress' },
    rating: 4.7,
    feedbackText: 'Outstanding work on infrastructure.',
    requestDetails: 'Request for cloud certification budget.'
  },
  {
    id: '6',
    name: 'Diana Davis',
    email: '<EMAIL>',
    role: 'User',
    status: { id: 'active', name: 'Active' },
    joinDate: '2023-06-18',
    department: 'Design',
    employee: {
      email: '<EMAIL>',
      fullNameEnglish: 'Diana Davis',
      companyName: 'TechCorp Inc.',
      positionName: 'UX Designer'
    },
    tags: ['Inactive', 'Assigned'],
    date: '2023-06-18',
    topic: 'Design Review',
    subject: { title: 'User Experience Improvements' },
    rating: 4.3,
    feedbackText: 'Innovative design solutions.',
    requestDetails: 'Request for design tool licenses.'
  }
];

// Mock columns configuration for user table (matches original workingColumns structure)
export const mockColumns: DataColumnProps<User>[] = [
  {
    key: 'name',
    caption: 'Full Name',
    width: 320,
    minWidth: 320,
    sortable: true,
    render: (user) => (
      <TableUserInfoCell
        email={user.email}
        fullName={user.name}
        company={user.employee?.companyName || user.department}
        position={user.employee?.positionName || user.role}
        // tags={user.tags}
      />
    )
  },
  {
    key: 'date',
    caption: 'Date',
    width: 120,
    minWidth: 120,
    sortable: true,
    render: (user) => (
      <TableTextCell>
        {user.date ? new Date(user.date).toLocaleDateString() : ''}
      </TableTextCell>
    )
  },
  {
    key: 'topic',
    caption: 'Topic',
    width: 200,
    minWidth: 200,
    sortable: true,
    render: (user) => (
      <TableTextCell>{user.topic || user.subject?.title || ''}</TableTextCell>
    )
  },
  {
    key: 'rating',
    caption: 'Rating',
    width: 120,
    minWidth: 120,
    sortable: true,
    render: (user) => (
      <TableCell>
        {user.rating ? (
          <StarRating
            value={user.rating}
            disabled
            size={StarRatingSize.Small}
          />
        ) : null}
      </TableCell>
    )
  },
  {
    key: 'preview',
    caption: 'Preview',
    minWidth: 200,
    // width: 300,
    grow: 1,
    sortable: true,
    render: (user) => (
      <TableTextCell>
        {user.feedbackText || user.requestDetails || ''}
      </TableTextCell>
    )
  }
];

export const mockTeamObjectives: any[] = [
  {
    employeeId: '99905158',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'David Johnson',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905159',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Khalid Al Mansoori',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 120,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905160',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Latifa Al Suwaidi',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905161',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Olivia Martinez',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  },
  {
    employeeId: '99905157',
    companyCode: '1100',
    companyName: '',
    fullNameEnglish: 'Sophia Lee',
    email: '<EMAIL>',
    jobTitle:
      'Senior Operations Manager, Group Performance & Operation Management',
    objectiveInfo: {
      completedObjectivesCount: 1,
      totalObjectivesCount: 6,
      totalWeightage: 100,
      skillsCovered: 10
    }
  }
];

export const mockTeamObjectivesColumns: DataColumnProps<any>[] = [
  {
    key: 'EmployeeName',
    caption: 'Employee Name',
    width: 720,
    minWidth: 180,
    sortable: true,
    grow: 1,
    render: (data) => (
      <TableUserInfoCell
        size="Medium"
        email={data.email}
        fullName={data.fullNameEnglish}
        company={data.companyName}
        position={data.jobTitle}
      />
    )
  },
  {
    key: 'Completedobjectives',
    caption: 'Completed objectives',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => (
      <TableCell>
        {String(data.objectiveInfo.completedObjectivesCount)} of{' '}
        {String(data.objectiveInfo.totalObjectivesCount)}
      </TableCell>
    )
  },
  {
    key: 'TotalWeightage',
    caption: 'Total Weightage',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => (
      <TableCell>{String(data.objectiveInfo.totalWeightage)}%</TableCell>
    )
  },
  {
    key: 'SkillsCovered',
    caption: 'Skills Covered',
    width: 180,
    minWidth: 180,
    sortable: false,
    render: (data) => (
      <TableCell>{String(data.objectiveInfo.skillsCovered)}%</TableCell>
    )
  }
];
