import { ChangeEvent, memo } from 'react';

import { Input } from '../Input';

import { cn } from '../../../utils';

export interface TableInputCellProps {
  value?: string;
  placeholder?: string;
  disabled?: boolean;
  readonly?: boolean;
  type?: 'text' | 'number' | 'email' | 'password';
  onChange?: (value: string) => void;
  onBlur?: () => void;
  onEnterPress?: () => void;
  className?: string;
  dataAttributes?: string;
  size?: 'Small' | 'Medium' | 'Large';
  maxLength?: number;
  error?: boolean;
  supportingText?: string;
}

export const TableInputCell = memo<TableInputCellProps>(
  ({
    value,
    placeholder,
    disabled = false,
    readonly = false,
    type = 'text',
    onChange,
    onBlur,
    onEnterPress,
    className,
    dataAttributes,
    size = 'Small',
    maxLength,
    supportingText: _supportingText
  }) => {
    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
      onChange?.(event.target.value);
    };

    const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
      if (event.key === 'Enter') {
        onEnterPress?.();
      }
    };

    return (
      <div
        className={cn('flex h-full w-full items-center', className)}
        data-attributes={dataAttributes}
      >
        <Input
          label=""
          showLabel={false}
          value={value}
          placeholder={placeholder}
          disabled={disabled}
          readOnly={readonly}
          type={type}
          onChange={handleChange}
          onBlur={onBlur}
          onKeyPress={handleKeyPress}
          size={size}
          maxLength={maxLength}
          supportingText=""
          containerClassName="focus-within:border-primary-default h-full w-full rounded-none border border-transparent hover:border-divider-mid"
          inputClassName="h-full w-full rounded-none border-none"
        />
      </div>
    );
  }
);

TableInputCell.displayName = 'TableInputCell';
