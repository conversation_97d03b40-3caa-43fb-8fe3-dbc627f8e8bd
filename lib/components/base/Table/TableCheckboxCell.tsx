import { ChangeEvent, memo } from 'react';

import { Checkbox } from '../Checkbox';

import { cn } from '../../../utils';

export interface TableCheckboxCellProps {
  checked?: boolean;
  indeterminate?: boolean;
  disabled?: boolean;
  onChange?: (checked: boolean) => void;
  className?: string;
  dataAttributes?: string;
}

export const TableCheckboxCell = memo<TableCheckboxCellProps>(
  ({
    checked = false,
    indeterminate = false,
    disabled = false,
    onChange,
    className,
    dataAttributes
  }) => {
    const handleChange = (event: ChangeEvent<HTMLInputElement>) => {
      onChange?.(event.target.checked);
    };

    return (
      <div
        className={cn(
          'flex h-full w-full items-center justify-center',
          className
        )}
        data-attributes={dataAttributes}
      >
        <Checkbox
          checked={checked}
          indeterminate={indeterminate}
          disabled={disabled}
          onChange={handleChange}
          label=""
          withContainer={false}
          className="focus-within:border-primary-default h-full w-full rounded-none border border-transparent hover:border-divider-mid"
        />
      </div>
    );
  }
);

TableCheckboxCell.displayName = 'TableCheckboxCell';
