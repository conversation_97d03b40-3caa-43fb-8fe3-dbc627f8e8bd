import type { Meta, StoryObj } from '@storybook/react';

import { TableCheckboxCell } from './TableCheckboxCell';

const meta: Meta<typeof TableCheckboxCell> = {
  title: 'Design System/Components/Table/TableCheckboxCell',
  component: TableCheckboxCell,
  parameters: {
    layout: 'centered'
  },
  argTypes: {
    checked: {
      control: { type: 'boolean' }
    },
    indeterminate: {
      control: { type: 'boolean' }
    },
    disabled: {
      control: { type: 'boolean' }
    }
  }
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    checked: false,
    indeterminate: false,
    disabled: false
  }
};

export const Checked: Story = {
  args: {
    checked: true,
    disabled: false
  }
};

export const Indeterminate: Story = {
  args: {
    indeterminate: true,
    disabled: false
  }
};

export const Disabled: Story = {
  args: {
    checked: false,
    disabled: true
  }
};
