import { Dispatch, SetStateAction, useEffect, useMemo } from 'react';

// TODO: remove uui-core dependency
import {
  HistoryAdaptedRouter,
  IHistory4,
  useUuiServices,
  UuiContext
} from '@epam/uui-core';
import { createBrowserHistory } from 'history';

import { Table } from './Table';
import {
  PageModel,
  TableDataSourceState,
  TableProps,
  UseQueryResult
} from './types';

import { DropdownOption } from '../Dropdown';
import { DropdownSingleSelect } from '../DropdownSingleSelect';
import { Pagination } from '../Pagination';

export const DEFAULT_PAGE_SIZE_OPTIONS: DropdownOption[] = [
  { id: 10, title: '10' },
  { id: 25, title: '25' },
  { id: 50, title: '50' }
];

export interface DataTableProps<TItem, TId, TFilters = unknown>
  extends Omit<
    TableProps<TItem, TId, TFilters>,
    | 'getRows'
    | 'isLoading'
    | 'isError'
    | 'dataSourceState'
    | 'setDataSourceState'
    | 'renderRowContent'
  > {
  queryResult: UseQueryResult<PageModel<TItem>>;

  // DataTable props (using legacy naming convention)
  dataSourceState: TableDataSourceState<TId, TFilters>;
  setDataSourceState: Dispatch<
    SetStateAction<TableDataSourceState<TId, TFilters>>
  >;
  renderRowContent?: (item: TItem, index: number) => React.ReactNode;

  // Page size options
  pageSizes?: number[];
  setPageSize?: (pageSize: number) => void;

  showPagination?: boolean;
  showPageSizeSelector?: boolean;

  containerClassName?: string;
  paginationClassName?: string;
}

export const DataTable = <TItem, TId, TFilters = unknown>({
  queryResult,
  dataSourceState,
  setDataSourceState,
  renderRowContent,
  showPagination = true,
  showPageSizeSelector = true,
  pageSizes,
  setPageSize,
  containerClassName,
  paginationClassName,
  withCustomRow,
  rowDataAttributes,
  getCustomRowExtraOptions,
  ...restTableProps
}: DataTableProps<TItem, TId, TFilters>) => {
  const history = createBrowserHistory();
  const router = new HistoryAdaptedRouter(history as unknown as IHistory4);
  const { services } = useUuiServices({ router });

  const { isLoading, isError } = queryResult;
  const { data: pageModel } = queryResult;

  // Convert pageSizes to pageSizeOptions if needed
  const resolvedPageSizeOptions = useMemo(() => {
    if (pageSizes) {
      return pageSizes.map((size) => ({ id: size, title: String(size) }));
    }
    return DEFAULT_PAGE_SIZE_OPTIONS;
  }, [pageSizes]);

  // Initialize with first available page size if current pageSize is not in the options
  const effectiveDataSourceState = useMemo(() => {
    const availableSizes = pageSizes || [10, 25, 50];
    if (!availableSizes.includes(dataSourceState.pageSize)) {
      return {
        ...dataSourceState,
        pageSize: availableSizes[0]
      };
    }
    return dataSourceState;
  }, [dataSourceState, pageSizes]);

  // Update parent state if correction is needed
  useEffect(() => {
    const availableSizes = pageSizes || [10, 25, 50];
    if (!availableSizes.includes(dataSourceState.pageSize)) {
      const correctedState = {
        ...dataSourceState,
        pageSize: availableSizes[0]
      };
      setDataSourceState(correctedState);
    }
  }, [pageSizes, dataSourceState, setDataSourceState]);

  const totalPages = useMemo(() => {
    if (!pageModel?.totalCount || !effectiveDataSourceState.pageSize) return 1;
    return Math.ceil(pageModel.totalCount / effectiveDataSourceState.pageSize);
  }, [pageModel?.totalCount, effectiveDataSourceState.pageSize]);

  const handlePageChange = (newPage: number) => {
    const newValue = {
      ...effectiveDataSourceState,
      pageNumber: newPage
    };
    setDataSourceState(newValue);
  };

  const handlePageSizeChange = (option?: DropdownOption) => {
    if (option) {
      const newPageSize = Number(option.id);
      const newValue = {
        ...effectiveDataSourceState,
        pageSize: newPageSize,
        pageNumber: 1
      };
      setDataSourceState(newValue);

      // Call setPageSize callback if provided
      setPageSize?.(newPageSize);
    }
  };

  const currentPageSizeOption = useMemo(() => {
    return (
      resolvedPageSizeOptions.find(
        (option: DropdownOption) =>
          Number(option.id) === effectiveDataSourceState.pageSize
      ) || resolvedPageSizeOptions[0]
    );
  }, [resolvedPageSizeOptions, effectiveDataSourceState.pageSize]);

  const tableProps = withCustomRow
    ? {
        dataSourceState: effectiveDataSourceState,
        setDataSourceState,
        getRows: () => pageModel?.items || [],
        isLoading,
        isError,
        renderRowContent,
        withCustomRow: true as const,
        rowDataAttributes,
        getCustomRowExtraOptions,
        ...restTableProps
      }
    : {
        dataSourceState: effectiveDataSourceState,
        setDataSourceState,
        getRows: () => pageModel?.items || [],
        isLoading,
        isError,
        renderRowContent,
        ...restTableProps
      };

  return (
    <UuiContext.Provider value={services}>
      <div className={containerClassName} data-attributes="DataTable">
        <Table<TItem, TId, TFilters> {...tableProps} />

        {/* Pagination and page size selector in a single row */}
        {(showPagination || showPageSizeSelector) && !isLoading && !isError && (
          <div className="mt-12 flex items-center justify-between">
            {/* Left spacer to help center pagination */}
            <div className="flex-1">{/* Empty div for spacing */}</div>

            {/* Center - Pagination */}
            <div className="flex justify-center">
              {showPagination && totalPages > 1 && (
                <Pagination
                  className={paginationClassName}
                  totalPages={totalPages}
                  currentPage={effectiveDataSourceState.pageNumber}
                  onChange={handlePageChange}
                />
              )}
            </div>

            {/* Right - Page size selector */}
            <div className="flex flex-1 justify-end">
              {showPageSizeSelector && (
                <DropdownSingleSelect
                  value={currentPageSizeOption}
                  onChange={handlePageSizeChange}
                  options={resolvedPageSizeOptions}
                  size="Small"
                  className="w-72"
                  label=""
                  showClear={false}
                />
              )}
            </div>
          </div>
        )}
      </div>
    </UuiContext.Provider>
  );
};
