import { Column, ColumnGroup, PageModel, UseQueryResult } from './types';

import { TagVariant } from '../Tag';

export const formatDateTime = (dateString: string) => {
  if (!dateString || dateString === '0001-01-01T00:00:00+00:00') {
    return 'Never';
  }
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const getTagColor = (status: string): TagVariant => {
  switch (status.toLowerCase()) {
    case 'draft':
      return 'Grey';
    case 'manager assessment':
      return 'Supernova';
    case 'self assessment':
      return 'LightBlue';
    case 'completed':
      return 'Green';
    case 'dotted-line manager assessment':
    case 'dotted-line manager':
      return 'Electric';
    case 'rating approval':
      return 'PacificBlue';
    default:
      return 'Grey';
  }
};

export const getColumnGroupDisplayName = (columnGroupName: ColumnGroup) => {
  switch (columnGroupName) {
    case ColumnGroup.AtrForm:
      return 'ATR Form';
    case ColumnGroup.AtrTemplate:
      return 'ATR Template';
    case ColumnGroup.AtrFormStatus:
      return 'ATR Form Status';
    case ColumnGroup.Employee:
      return 'Employee';
    case ColumnGroup.ATRGroup:
      return 'ATR Group';
    case ColumnGroup.AssessmentManagerLM:
      return 'Assessment Manager (LM)';
    case ColumnGroup.LastUpdated:
      return 'Last Updated';
    case ColumnGroup.UpdatedBy:
      return 'Last Updated By';
    default:
      return '';
  }
};

export const getColumnDisplayName = (columnName: Column) => {
  switch (columnName) {
    case Column.AtrTemplateName:
    case Column.ATRGroupName:
      return 'Name';
    case Column.AtrFormMajorStatus:
      return 'Major';
    case Column.AtrFormMinorStatus:
      return 'Minor';
    case Column.EmployeeCompanyName:
      return 'Company';
    case Column.AtrFormId:
    case Column.EmployeeID:
    case Column.AssessmentLineManagerID:
      return 'ID';
    case Column.EmployeeFullName:
    case Column.AssessmentLineManagerFullName:
      return 'Full Name';
    case Column.LastUpdated:
      return 'Time & Date';
    case Column.UpdatedBy:
      return 'User Name';
    default:
      return '';
  }
};

export const createMockQueryResult = <T = unknown>(
  data: T[],
  loading = false,
  error = false
): UseQueryResult<PageModel<T>> => ({
  data: {
    items: data,
    totalCount: data.length,
    pageNumber: 1,
    pageSize: 10,
    hasNextPage: false,
    hasPreviousPage: false
  },
  isLoading: loading,
  isError: error,
  error: error ? ({} as Error) : null
});

// Placeholder functions for mockColumns compatibility
export const getRequestedCaption = (_type: unknown): string => 'Requested';
export const formatToFullDate = (date: string): string =>
  new Date(date).toLocaleDateString();
export const getSubjectSubType = (_subject: unknown, _topic: unknown): string =>
  'Subject';

export const getToggleApprovalText = (): string => 'Toggle Approval';
export const getToggleText = (): string => 'Toggle';

export const formatToShortDate = (date: string): string =>
  new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric'
  });
