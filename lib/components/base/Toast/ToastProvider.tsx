import { createContext, PropsWithChildren, useCallback, useState } from 'react';

import { Toast, ToastContextType } from './Toast.types';
import { ToastContainer } from './ToastContainer';
import { useToastTimers } from './useToastTimers';

let toastId = 0;

export const ToastContext = createContext<ToastContextType | undefined>(
  undefined
);

export type ToastProviderProps = PropsWithChildren & {
  context?: ToastContextType;
};

const ToastProviderBody = ({ children }: PropsWithChildren) => {
  const [toasts, setToasts] = useState<Toast[]>([]);
  const onToastExpire = useCallback((id: number) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  const { startTimer, clearTimer, pauseAllToasts, resumeAllToasts } =
    useToastTimers(onToastExpire);

  const addToast = useCallback(
    (toast: Omit<Toast, 'id'>) => {
      const id = toastId++;
      setToasts((prev) => [...prev, { ...toast, id }]);
      startTimer(id, toast.duration);
    },
    [startTimer]
  );

  const removeToast = useCallback(
    (id: number) => {
      clearTimer(id);
      setToasts((prev) => prev.filter((t) => t.id !== id));
    },
    [clearTimer]
  );

  return (
    <ToastContext.Provider
      value={{ toasts, addToast, removeToast, pauseAllToasts, resumeAllToasts }}
    >
      <ToastContainer />
      {children}
    </ToastContext.Provider>
  );
};

export const ToastProvider = ({ children, context }: ToastProviderProps) => {
  if (context) {
    return (
      <ToastContext.Provider value={context}>
        <ToastContainer />
        {children}
      </ToastContext.Provider>
    );
  }

  return <ToastProviderBody>{children}</ToastProviderBody>;
};
