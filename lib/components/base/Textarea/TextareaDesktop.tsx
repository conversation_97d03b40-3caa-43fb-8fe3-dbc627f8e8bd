import { forwardRef, useCallback, useEffect, useRef, useState } from 'react';

import { cn, generateId } from '@/utils';

import { MAX_ROWS, MIN_ROWS, TextareaProps } from './Textarea';
import { TextareaVariant } from './types';
import { getContainerVariants, getSupportingTextVariants } from './utils';

export const TextareaDesktop = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      id,
      name,
      value,
      label = 'Label',
      showLabel = true,
      labelClassName,
      variant,
      placeholder = 'Type here...',
      disabled,
      required,
      supportingText,
      supportingTextClassName,
      rows = MIN_ROWS,
      maxRows = MAX_ROWS,
      maxLength = 300,
      trimBeforeCounting = false,
      hideCounter = false,
      drawerHeaderTitle: _drawerHeaderTitle,
      drawerSubmitTitle: _drawerSubmitTitle,
      enhanceWithAI,
      onChange,
      dataAttributes,
      ...rest
    },
    forwardedRef
  ) => {
    const textareaId = id || generateId(name || 'textarea');
    const textareaRef = useRef<HTMLTextAreaElement>(null);

    const [currentValue, setCurrentValue] = useState<string>(value || '');

    useEffect(() => {
      setCurrentValue(value || '');
    }, [value]);

    const adjustHeight = useCallback(() => {
      const textarea = textareaRef.current;
      if (!textarea) return;

      // Reset height to auto to get the correct scrollHeight
      textarea.style.height = 'auto';

      // Define border width and padding to avoid scroll
      const {
        lineHeight,
        borderBottomWidth,
        borderTopWidth,
        paddingTop,
        paddingBottom
      } = getComputedStyle(textarea);
      const borderHeight =
        parseFloat(borderBottomWidth) + parseFloat(borderTopWidth);

      const paddingHeight = parseFloat(paddingTop) + parseFloat(paddingBottom);
      const currentHeight = textarea.scrollHeight + borderHeight;
      const maxHeight =
        maxRows * parseFloat(lineHeight) + borderHeight + paddingHeight;
      const newHeight = Math.min(currentHeight, maxHeight);
      const maxHeightDifference = parseFloat(paddingTop) / 2 + 3;

      textarea.style.height = `${currentHeight > maxHeight ? newHeight - maxHeightDifference : newHeight}px`;
    }, [maxRows]);

    useEffect(() => {
      adjustHeight();
    }, [adjustHeight, currentValue]);

    const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
      // Limit max length inside onChange handler is required for Android OS
      adjustHeight();
      setCurrentValue(e.target.value.slice(0, maxLength));
      onChange?.({
        ...e,
        target: {
          ...e.target,
          value: e.target.value.slice(0, maxLength)
        }
      });
    };

    const setRefs = (node: HTMLTextAreaElement | null) => {
      if (typeof forwardedRef === 'function') {
        forwardedRef(node);
      }
      if (node) {
        (
          textareaRef as React.MutableRefObject<HTMLTextAreaElement | null>
        ).current = node;
      }
    };

    const textLength =
      (trimBeforeCounting ? currentValue?.trim() : currentValue)?.length || 0;
    const isHeaderShown = label || enhanceWithAI;
    const isFooterShown = supportingText || !hideCounter;

    return (
      <div
        data-attributes={dataAttributes}
        className="flex w-full flex-col gap-4"
      >
        {isHeaderShown && (
          <div className="mb-4 flex items-end">
            {/* Label */}
            {label && showLabel && (
              <label
                htmlFor={textareaId}
                className={cn(
                  'text-label-m1-regular text-text-heading',
                  labelClassName
                )}
              >
                {label}
                {required && <span className="ml-[2px]">*</span>}
              </label>
            )}
            <span className="flex-auto"></span>
            <span>{enhanceWithAI}</span>
          </div>
        )}
        {/* Textarea */}
        <textarea
          ref={setRefs}
          id={textareaId}
          className={cn(
            getContainerVariants({
              variant
            }),
            'overflow-y-auto'
          )}
          placeholder={placeholder}
          disabled={disabled}
          rows={rows}
          value={value ?? currentValue}
          maxLength={maxLength}
          onChange={handleChange}
          {...rest}
        />

        {isFooterShown && (
          <div className="flex">
            {supportingText && (
              <span
                className={cn(
                  getSupportingTextVariants({
                    variant,
                    className: supportingTextClassName
                  })
                )}
              >
                {supportingText}
              </span>
            )}
            <span className="flex-auto"></span>

            {!hideCounter && (
              <span
                className={cn(
                  getSupportingTextVariants({
                    variant: TextareaVariant.Primary,
                    className: supportingTextClassName
                  })
                )}
              >
                {textLength}/{maxLength}
              </span>
            )}
          </div>
        )}
      </div>
    );
  }
);
