import { FC, useRef, useState } from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import { DatePicker } from './DatePicker';
import { DatePickerProps } from './DatePicker.types';

import { InputSize } from '../Input';

const DatePickerDecorator: FC<Omit<DatePickerProps, 'onChange'>> = ({
  value,
  ...props
}) => {
  const [date, setDate] = useState<Date | undefined>(value);

  const handleChange = (newDate?: Date) => {
    setDate(newDate);
  };

  const ref = useRef(null);

  return (
    <div className="min-h-[400px]">
      <DatePicker ref={ref} {...props} value={date} onChange={handleChange} />
    </div>
  );
};

const SmallDatePickerDecorator: FC<Omit<DatePickerProps, 'onChange'>> = ({
  value,
  ...props
}) => {
  const [date, setDate] = useState<Date | undefined>(value);

  const handleChange = (newDate?: Date) => {
    setDate(newDate);
  };

  const ref = useRef(null);

  return (
    <div className="ml-auto min-h-[400px] w-[250px]">
      <DatePicker ref={ref} {...props} value={date} onChange={handleChange} />
    </div>
  );
};

const meta = {
  title: 'Design System/Components/Date Picker/Date Picker',
  component: DatePickerDecorator,
  tags: ['autodocs', 'Stable'],
  parameters: {
    docs: {
      description: {
        component:
          'Date picker component for selecting dates in forms. It supports both mobile and desktop views.'
      }
    }
  },
  args: {
    required: true,
    label: 'Select Date',
    size: 'Large',
    name: 'myDatePicker'
  },
  argTypes: {
    required: {
      control: 'boolean'
    },
    label: {
      control: 'text'
    },
    size: {
      control: 'radio',
      options: Object.values(InputSize)
    },
    name: {
      control: 'text'
    }
  }
} satisfies Meta<typeof DatePicker>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};

export const WithInitialValue: Story = {
  args: {
    value: new Date()
  }
};

export const SmallDimension: Story = {
  render: () => <SmallDatePickerDecorator />
};

export const Required: Story = {
  args: {
    value: undefined,
    required: true,
    supportingText: 'This field is required',
    variant: 'Error'
  }
};
