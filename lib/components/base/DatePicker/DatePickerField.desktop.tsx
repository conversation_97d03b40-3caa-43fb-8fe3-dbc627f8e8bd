import { forwardRef, memo, useCallback, useState } from 'react';
import { UI } from 'react-day-picker';
import ReactDOM from 'react-dom';

import { getPortalElement } from '@/utils';

import { DatePickerFieldProps } from './DatePicker.types';
import { formatDateValue } from './DatePicker.utils';
import { DatePickerCalendar } from './DatePickerCalendar';

import { Dropdown } from '../Dropdown';
import { Input } from '../Input';
import { useUIKitModalsSelectors } from '../UIKitProvider';

export const DatePickerFieldDesktop = memo(
  forwardRef<HTMLInputElement, DatePickerFieldProps>(
    (
      { dataAttributes = 'DatePickerDesktop', onChange, value, ...inputProps },
      ref
    ) => {
      const [isOpen, setIsOpen] = useState(false);
      const { portalClassName } = useUIKitModalsSelectors();

      const handleChange = useCallback(
        (date: Date) => {
          setIsOpen(false);
          onChange?.(date);
        },
        [onChange]
      );

      const handleClear = useCallback(() => {
        onChange?.(undefined);
      }, [onChange]);

      return (
        <Dropdown
          placement="bottom-start"
          fallbackPlacements={['top-start', 'bottom-end', 'top-end']}
          isOpen={isOpen}
          setIsOpen={setIsOpen}
          title="Select Date"
          dataAttributes={dataAttributes}
          renderTrigger={({ getTriggerProps }) => {
            const triggerProps = getTriggerProps();
            return (
              <Input
                ref={ref}
                {...triggerProps}
                {...inputProps}
                onRightIconClick={triggerProps.onClick as () => void}
                onClear={handleClear}
                value={formatDateValue(value)}
                readOnly
              />
            );
          }}
          renderDropdown={({ floatingStyles, getFloatingProps, refs }) =>
            isOpen &&
            ReactDOM.createPortal(
              <div
                ref={refs.setFloating}
                style={floatingStyles}
                {...getFloatingProps()}
                className="z-[1040]"
              >
                <DatePickerCalendar
                  classes={{ [UI.Month]: 'shadow-dropdown' }}
                  onSelect={handleChange}
                  selected={value}
                />
              </div>,
              getPortalElement(portalClassName)
            )
          }
          autoWidth
        />
      );
    }
  )
);
