import { useState } from 'react';

import { <PERSON>a, StoryObj } from '@storybook/react';

import { MultiselectFilter } from './components';
import { Filters } from './Filters';
import {
  ATR_GROUPS_OPTIONS,
  COMPANIES_OPTIONS,
  DIRECTORATE_OPTIONS,
  DIVISIONS_OPTIONS,
  FUNCTIONS_OPTIONS
} from './Filters.mock';
import { FilterChangeHandler, FilterValue } from './Filters.types';

type Filters = 'companyName' | 'directorate';

const FiltersWrapped = () => {
  const [selectedFilters, setSelectedFilters] = useState<
    Partial<Record<Filters, FilterValue>>
  >({ companyName: ['1100'] });

  const handleFilterChange: FilterChangeHandler<Filters> = (id) => (filter) => {
    setSelectedFilters((prevState) => {
      if (!filter) {
        const newState = { ...prevState };
        delete newState[id];
        return newState;
      }
      return {
        ...prevState,
        [id]: filter
      };
    });
  };

  const handleFiltersClear = () => {
    setSelectedFilters({});
  };

  return (
    <div className="flex gap-x-16">
      <div className="basis-1/2">
        <Filters
          selectedFilters={selectedFilters}
          onFilterChanged={handleFilterChange}
          onClearFilters={handleFiltersClear}
        >
          <MultiselectFilter
            id="companyName"
            label="Company Name"
            basis="242"
            visibilityMode="hiddenByDefault"
            options={COMPANIES_OPTIONS}
          />
          <MultiselectFilter
            id="directorate"
            label="Directorate"
            basis="242"
            visibilityMode="always"
            options={DIRECTORATE_OPTIONS}
          />
          <MultiselectFilter
            id="function"
            label="Function"
            basis="242"
            options={FUNCTIONS_OPTIONS}
          />
          <MultiselectFilter
            id="division"
            label="Division"
            basis="242"
            options={DIVISIONS_OPTIONS}
          />
          <MultiselectFilter
            id="atrGroup"
            label="ATR Group"
            basis="242"
            options={ATR_GROUPS_OPTIONS}
          />
        </Filters>
      </div>
      <pre className="basis-1/2">
        {JSON.stringify(selectedFilters, null, 2)}
      </pre>
    </div>
  );
};

const meta = {
  title: 'Design System/Components/Filters/Filters',
  component: FiltersWrapped,
  tags: ['autodocs', 'In Review']
} satisfies Meta<typeof Filters>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {};
