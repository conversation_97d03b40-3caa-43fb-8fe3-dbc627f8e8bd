import {
  PropsWithChildren,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';

import isEmpty from 'lodash/isEmpty';
import values from 'lodash/values';

import { cn } from '@/utils';

import { FiltersContext } from './Filters.context';
import {
  Filter,
  FilterChangeHandler,
  FilterState,
  FilterValue
} from './Filters.types';

import { Button } from '../Button';
import { Dropdown } from '../Dropdown';
import { DropdownLayout } from '../Dropdown/components';
import { DropdownMultiselectListItem } from '../DropdownMultiselect/DropdownMultiselectListItem';
import { Link } from '../Link';
import { DataAttributesProps } from '../types';

export type FiltersProps<TKey extends string> = {
  onFilterChanged: FilterChangeHandler<TKey>;
  onClearFilters: () => void;
  selectedFilters: Partial<Record<TKey, FilterValue>>;
  className?: string;
  disabled?: boolean;
} & DataAttributesProps;

function toDefaultFilterState<TKey extends string>({
  id,
  label,
  visibilityMode
}: Pick<Filter<TKey>, 'id' | 'visibilityMode' | 'label'>): FilterState<TKey> {
  return {
    selected: visibilityMode !== 'hiddenByDefault',
    disabled: visibilityMode === 'always',
    label,
    id
  };
}

export const Filters = <TKey extends string>({
  dataAttributes = 'Filters',
  selectedFilters,
  onFilterChanged,
  onClearFilters,
  children,
  className,
  disabled
}: PropsWithChildren<FiltersProps<TKey>>) => {
  const [isManagerOpen, setManagerOpen] = useState(false);
  const [filtersState, setFiltersState] = useState<FilterState<TKey>[]>([]);
  const [isSelectedShownMode, setSelectedShownMode] = useState(false);

  useEffect(() => {
    setFiltersState((currentFilterState) => {
      return currentFilterState.map((state) => {
        if (state.selected) return state;
        if (selectedFilters[state.id]) {
          return {
            ...state,
            selected: !!selectedFilters[state.id]
          };
        }
        return state;
      });
    });
  }, [selectedFilters]);

  const handleRegisterFilter = useCallback<
    FiltersContext<TKey>['onRegisterFilter']
  >(({ id, visibilityMode, label }) => {
    setFiltersState((currentFiltersStateMap) => {
      if (
        !currentFiltersStateMap.find((filterState) => filterState.id === id)
      ) {
        return [
          ...currentFiltersStateMap,
          toDefaultFilterState({ id, visibilityMode, label })
        ];
      }
      return currentFiltersStateMap;
    });
  }, []);

  const handleUnregisterFilter = useCallback<
    FiltersContext<TKey>['onUnregisterFilter']
  >(({ id }) => {
    setFiltersState((currentFiltersStateMap) => {
      if (currentFiltersStateMap.find((filterState) => filterState.id === id)) {
        return [...currentFiltersStateMap].filter(
          (filterState) => filterState.id !== id
        );
      }
      return currentFiltersStateMap;
    });
  }, []);

  const handleRemoveFilter = useCallback<
    FiltersContext<TKey>['onRemoveFilter']
  >(
    (id) => () => {
      setFiltersState((currentFiltersStateMap) => {
        return [...currentFiltersStateMap].map((filterState) => {
          if (filterState.id === id) {
            return { ...filterState, selected: false };
          } else {
            return filterState;
          }
        });
      });
      onFilterChanged(id as TKey)();
    },
    [onFilterChanged]
  );

  const handleToggleFilter = (filterId: TKey) => {
    if (filtersState.find((filter) => filter.id === filterId)) {
      onFilterChanged(filterId)();
    }

    setFiltersState((currentFiltersStateMap) => {
      return [...currentFiltersStateMap].map((filterState) => {
        if (filterState.id === filterId) {
          return { ...filterState, selected: !filterState.selected };
        } else {
          return filterState;
        }
      });
    });
  };

  const handleClearFiltersVisibility = () => {
    setFiltersState((currentFiltersStateMap) => {
      return [...currentFiltersStateMap].map((filter) => ({
        ...filter,
        selected: filter.disabled
      }));
    });
  };

  const selectedOptionsCount = useMemo(
    () =>
      values<FilterState<TKey>>(filtersState).reduce<number>((acc, filter) => {
        return filter?.selected ? acc + 1 : acc;
      }, 0),
    [filtersState]
  );

  const contextProps = useMemo<FiltersContext<TKey>>(
    () => ({
      selectedFilters,
      filtersState,
      onFilterChanged,
      disabled,
      onRemoveFilter: handleRemoveFilter,
      onRegisterFilter: handleRegisterFilter,
      onUnregisterFilter: handleUnregisterFilter
    }),
    [
      handleRegisterFilter,
      handleRemoveFilter,
      handleUnregisterFilter,
      onFilterChanged,
      selectedFilters,
      filtersState,
      disabled
    ]
  );

  const isClearFiltersButtonDisabled = isEmpty(selectedFilters);

  return (
    <FiltersContext.Provider value={contextProps}>
      <div
        data-attributes={dataAttributes}
        className={cn('flex flex-wrap gap-x-16 gap-y-12 @container', className)}
      >
        {children}
        {onClearFilters && (
          <Button
            tooltip={
              isClearFiltersButtonDisabled
                ? { title: 'No filters applied' }
                : { title: 'Clear all selected filters' }
            }
            onClick={onClearFilters}
            variant="Secondary"
            className="!min-w-0"
            disabled={isClearFiltersButtonDisabled}
          >
            Clear All
          </Button>
        )}
        <Dropdown
          placement="bottom-start"
          dropdownOptions={{ width: '280px' }}
          isOpen={isManagerOpen}
          setIsOpen={setManagerOpen}
          className="max-w-fit justify-center"
          renderTrigger={({ getTriggerProps }) => (
            <div {...getTriggerProps()}>
              <Link className="min-h-40" leftIcon="Add_round">
                Add/Remove Filter
              </Link>
            </div>
          )}
          renderDropdown={() => (
            <DropdownLayout.FloatingContainer>
              <DropdownLayout.List
                count={filtersState.length}
                renderItem={(index) => {
                  const currentFilter = filtersState[index];

                  if (isSelectedShownMode && !currentFilter?.selected) {
                    return null;
                  }
                  return (
                    <DropdownMultiselectListItem
                      key={currentFilter.id}
                      title={currentFilter.label}
                      option={{
                        id: currentFilter.id,
                        title: currentFilter.label
                      }}
                      variant="Primary"
                      selected={currentFilter.selected}
                      disabled={currentFilter.disabled}
                      onClick={(option) =>
                        handleToggleFilter(option.id as TKey)
                      }
                    />
                  );
                }}
              />
              <DropdownLayout.Footer
                onClearSelectedOptions={handleClearFiltersVisibility}
                selectedOptionsCount={selectedOptionsCount}
                onSetShowSelected={setSelectedShownMode}
                showSelected={isSelectedShownMode}
              />
            </DropdownLayout.FloatingContainer>
          )}
        />
      </div>
    </FiltersContext.Provider>
  );
};
