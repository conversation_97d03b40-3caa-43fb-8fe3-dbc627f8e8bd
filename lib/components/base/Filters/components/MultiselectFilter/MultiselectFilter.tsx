import {
  memo,
  SetStateAction,
  useCallback,
  useEffect,
  useMemo,
  useState
} from 'react';

import isEmpty from 'lodash/isEmpty';

import { cn } from '@/utils';

import { useFilterController } from '../../Filters.hooks';
import { FilterOption, MultiselectFilterModel } from '../../Filters.types';

import {
  Dropdown,
  dropdownHighlightMatch,
  DropdownLayout
} from '../../../Dropdown';
import { Input } from '../../../Input';
import { Text } from '../../../Text';

export type MultiselectFilterProps<TKey extends string> =
  MultiselectFilterModel<TKey>;

export const MultiselectFilter = memo(
  <TKey extends string>({
    id,
    label,
    options,
    className,
    visibilityMode = 'shownByDefault',
    searchPlaceholder,
    basis,
    isLoading,
    onSearch,
    onSelect,
    onClear,
    onClose
  }: MultiselectFilterProps<TKey>) => {
    const {
      filterState,
      value = [],
      onChange,
      onRemove,
      disabled
    } = useFilterController({
      id,
      visibilityMode,
      label
    });

    const [isOpen, setIsOpen] = useState(false);
    const [selectedOptions, setSelectedOptions] = useState(value);
    const [isSelectedShownMode, setSelectedShownMode] = useState(false);
    const [search, setSearch] = useState('');

    const handleSearch = useCallback(
      (searchQuery: string) => {
        setSearch(searchQuery);
        onSearch?.(searchQuery);
      },
      [onSearch]
    );

    useEffect(() => {
      if (!filterState?.selected) {
        setIsOpen(false);
      }
    }, [filterState?.selected]);

    const handleToggleDropdown = useCallback(
      (isOpen: SetStateAction<boolean>) => {
        setIsOpen(isOpen);
        if (!isOpen) {
          handleSearch('');
          onClose?.();
        } else {
          setSelectedOptions(value);
        }
      },
      [value, handleSearch, onClose]
    );

    const handleApply = useCallback(() => {
      onChange?.(isEmpty(selectedOptions) ? undefined : selectedOptions);
      setIsOpen(false);
    }, [onChange, selectedOptions]);

    const handleClear = useCallback(() => {
      setSelectedOptions([]);
      onChange();
      onClear?.();
    }, [onChange, onClear]);

    const filteredOptions = useMemo<FilterOption[]>(() => {
      if (isSelectedShownMode) {
        if (onSearch && search) {
          return options.filter((option) =>
            selectedOptions.includes(String(option.id))
          );
        }

        return selectedOptions.reduce<FilterOption[]>((acc, id) => {
          const option = options.find(({ id: optionId }) => optionId === id);
          if (option) {
            if (search) {
              const regex = new RegExp(`(${search})`, 'i');
              const isMatch = regex.test(option.displayName);
              return isMatch ? [...acc, option] : acc;
            }

            return [...acc, option];
          }
          return acc;
        }, []);
      } else {
        if (onSearch) {
          return options;
        }

        return options.filter((option) => {
          const regex = new RegExp(`(${search})`, 'i');

          return regex.test(option.displayName);
        });
      }
    }, [onSearch, isSelectedShownMode, options, search, selectedOptions]);

    useEffect(() => {
      if (isSelectedShownMode && isEmpty(filteredOptions) && !search) {
        setSelectedShownMode(false);
      }
    }, [filteredOptions, isSelectedShownMode, search]);

    const inputContent = useMemo(() => {
      const selectedItemsString = value.reduce((acc, id, index) => {
        const selectedOption = options.find(
          ({ id: optionId }) => id === optionId
        );
        if (selectedOption) {
          return `${acc}${index !== 0 ? ',' : ''}${selectedOption.displayName}`;
        }
        return acc;
      }, '');
      return value.length ? (
        <Text
          lineClamp={1}
          className="grow break-all text-body-2-regular text-input-text-filled"
          tooltip={selectedItemsString}
        >
          <span className="text-input-text-disabled">{label}: </span>
          {selectedItemsString}
        </Text>
      ) : undefined;
    }, [label, options, value]);

    const showNoResultsFound = Boolean(filteredOptions.length === 0 && search);

    if (!filterState?.selected) {
      return null;
    }

    return (
      <Dropdown
        style={{ flexBasis: `${basis}px` }}
        placement="bottom-start"
        dropdownOptions={{ width: '364px' }}
        isOpen={isOpen}
        setIsOpen={handleToggleDropdown}
        className={cn('max-w-full grow @md:max-w-[30%]', className)}
        renderTrigger={({ getTriggerProps }) => (
          <div {...getTriggerProps()}>
            <Input
              disabled={disabled}
              inputClassName="justify-start"
              inputContentClassName={cn({ 'max-w-0': !isEmpty(value) })}
              readOnly
              size="Medium"
              label=""
              value=""
              placeholder={value.length ? '' : label}
              rightIcon={isOpen ? 'Expand_up_light' : 'Expand_down_light'}
              badges={inputContent}
            />
          </div>
        )}
        renderDropdown={() => (
          <DropdownLayout.FloatingContainer>
            <DropdownLayout.FilterHeader
              label={label}
              onRemoveClick={visibilityMode !== 'always' ? onRemove : undefined}
            />
            <DropdownLayout.Search
              placeholder={searchPlaceholder}
              value={search}
              onChange={(e) => handleSearch(e.target.value)}
              onClear={() => handleSearch('')}
            />
            <DropdownLayout.List
              count={filteredOptions.length}
              showNoResultsFound={showNoResultsFound}
              isLoading={isLoading}
              renderItem={(index, itemProps) => {
                const { displayName, id, disabled } = filteredOptions[index];
                const isSelected = !!selectedOptions.includes(String(id));
                return (
                  <DropdownLayout.ListItem
                    {...itemProps}
                    onClick={({ id, title }) => {
                      onSelect?.({
                        disabled,
                        id,
                        displayName: title,
                        isSelected: !isSelected
                      });
                      if (isSelected) {
                        return setSelectedOptions(
                          selectedOptions?.filter(
                            (selectedId) => id !== selectedId
                          )
                        );
                      }
                      setSelectedOptions([...selectedOptions, String(id)]);
                    }}
                    variant="Primary"
                    title={dropdownHighlightMatch(displayName, search)}
                    key={index}
                    selected={isSelected}
                    option={{
                      id,
                      disabled,
                      title: displayName
                    }}
                  />
                );
              }}
            ></DropdownLayout.List>
            <DropdownLayout.Footer
              showSelected={isSelectedShownMode}
              onApply={handleApply}
              selectedOptionsCount={selectedOptions.length}
              onSetShowSelected={setSelectedShownMode}
              onClearSelectedOptions={handleClear}
            />
          </DropdownLayout.FloatingContainer>
        )}
      />
    );
  }
);
