import { createContext, useContext } from 'react';

import {
  Filter,
  FilterChangeHandler,
  FilterState,
  FilterValue
} from './Filters.types';

export type FiltersContext<TKey extends string> = {
  selectedFilters: Partial<Record<TKey, FilterValue>>;
  filtersState: FilterState<TKey>[];
  disabled?: boolean;
  onFilterChanged: FilterChangeHandler<TKey>;
  onRemoveFilter: (id: TKey) => () => void;
  onRegisterFilter: (
    props: Pick<Filter<TKey>, 'id' | 'visibilityMode' | 'label'>
  ) => void;
  onUnregisterFilter: (props: Pick<Filter<TKey>, 'id'>) => void;
};

// TODO: Find a way how to create generic context
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const FiltersContext = createContext<FiltersContext<any>>({
  selectedFilters: {},
  filtersState: [],
  disabled: false,
  onFilterChanged: () => () => null,
  onRemoveFilter: () => () => null,
  onRegisterFilter: () => null,
  onUnregisterFilter: () => null
});

export const useFiltersContext = () => useContext(FiltersContext);
