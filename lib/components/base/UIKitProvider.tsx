import { createContext, FC, PropsWithChildren, useContext } from 'react';

import { AvatarLoaderProvider } from './Avatar';
import { ToastContextType, ToastProvider } from './Toast';

export type OnetalentUIKitContextType = {
  portalClassName?: string;
};

export const OnetalentUIKitContext = createContext<OnetalentUIKitContextType>(
  {}
);

export const useUIKitModalsSelectors = () => {
  const ctx = useContext(OnetalentUIKitContext);

  return {
    portalClassName: ctx.portalClassName
  };
};

export type OnetalentUIKitProviderProps = PropsWithChildren &
  OnetalentUIKitContextType & {
    loadAvatar?: (id: string | number) => Promise<string>;
    toastContext?: ToastContextType;
  };

export const OnetalentUIKitProvider: FC<OnetalentUIKitProviderProps> = ({
  children,
  portalClassName,
  loadAvatar,
  toastContext
}) => {
  const defaultLoadAvatarImage = () => Promise.resolve('');
  const loadImage = loadAvatar || defaultLoadAvatarImage;

  return (
    <OnetalentUIKitContext.Provider value={{ portalClassName }}>
      <ToastProvider context={toastContext}>
        <AvatarLoaderProvider loadImage={loadImage}>
          {children}
        </AvatarLoaderProvider>
      </ToastProvider>
    </OnetalentUIKitContext.Provider>
  );
};
