import { FC, ReactNode } from 'react';

import { cva } from 'class-variance-authority';

import { cn } from '@/utils';

import { ButtonSize, ButtonVariant, IconButton } from '../Button';
import { Link } from '../Link';
import { Tooltip, TooltipVariant } from '../Tooltip';
import { DataAttributesProps } from '../types';

import { useIsMobile } from '../../../hooks';
import './Header.scss';

const HEADER_ESERVICE_CLASSNAME =
  'ot-header-background md:h-200 md:min-h-200 py-32 w-full m-x-auto -mt-40 md:mt-0';

const HEADER_L1_CLASSNAME =
  'flex flex-col justify-center h-[104px] min-h-[104px] px-0 w-full m-x-auto -mt-40 md:mt-0';

export const HeaderVariant = {
  // @deprecated use OneHubEService instead
  eService: 'eService',
  // @deprecated use L1Page instead
  L1: 'L1',

  OneHubEService: 'OneHubEService',
  ExternalEService: 'ExternalEService',
  L1Page: 'L1Page'
} as const;

export type HeaderVariant = (typeof HeaderVariant)[keyof typeof HeaderVariant];

const headerTitleVariants = cva('text-text-heading', {
  variants: {
    variant: {
      [HeaderVariant.eService]: 'mt-24 text-header-1-medium',
      [HeaderVariant.OneHubEService]: 'mt-24 text-header-1-medium',

      [HeaderVariant.ExternalEService]: 'text-header-3-medium',
      [HeaderVariant.L1]: 'text-header-3-medium',
      [HeaderVariant.L1Page]: 'text-header-3-medium'
    }
  },
  defaultVariants: {
    variant: HeaderVariant.eService
  }
});

export type HeaderProps = DataAttributesProps & {
  className?: string;
  containerClassName?: string;
  headerTitle?: ReactNode;
  headerVariant?: HeaderVariant;
  buttonTooltip?: string;
  isPinned?: boolean;
  paddingLeft?: number;
  paddingRight?: number;
  contentMaxWidth?: number;
  onPin?(): void;
  onGoBack?(): void;
  onClose?(): void;
};

export const Header: FC<HeaderProps> = ({
  className,
  containerClassName,
  headerTitle,
  headerVariant = HeaderVariant.OneHubEService,
  buttonTooltip,
  dataAttributes = 'Header',
  isPinned,
  paddingLeft = 144,
  paddingRight = 40,
  contentMaxWidth = 1600,
  onPin,
  onGoBack,
  onClose
}) => {
  const isMobile = useIsMobile();

  const headerClassMap = {
    [HeaderVariant.eService]: HEADER_ESERVICE_CLASSNAME,
    [HeaderVariant.OneHubEService]: HEADER_ESERVICE_CLASSNAME,

    [HeaderVariant.ExternalEService]: HEADER_ESERVICE_CLASSNAME,

    [HeaderVariant.L1]: HEADER_L1_CLASSNAME,
    [HeaderVariant.L1Page]: HEADER_L1_CLASSNAME
  };

  const HEADER_CLASSNAME = headerClassMap[headerVariant];

  return (
    <div
      data-attributes={dataAttributes}
      className={cn(HEADER_CLASSNAME, className)}
    >
      <div
        style={
          !isMobile
            ? {
                paddingLeft,
                paddingRight,
                maxWidth: contentMaxWidth
              }
            : undefined
        }
        className={cn(
          'm-auto flex w-full',
          headerVariant === HeaderVariant.eService ||
            headerVariant === HeaderVariant.OneHubEService
            ? 'flex-col'
            : 'items-center gap-x-[10px]',
          containerClassName
        )}
      >
        {/* Back & Close Buttons */}
        {!isMobile && (onGoBack || onClose) && (
          <div className="flex min-h-40 w-auto gap-8">
            {onGoBack && (
              <Tooltip variant={TooltipVariant.Dark} title={buttonTooltip}>
                <IconButton
                  icon="Expand_left_light"
                  variant={ButtonVariant.Link}
                  size={ButtonSize.Medium}
                  onClick={onGoBack}
                />
              </Tooltip>
            )}
            {onClose && (
              <Tooltip variant={TooltipVariant.Dark} title={buttonTooltip}>
                <IconButton
                  icon="Close_round_light"
                  variant={ButtonVariant.Link}
                  size={ButtonSize.Medium}
                  onClick={onClose}
                />
              </Tooltip>
            )}
          </div>
        )}
        <div className={cn('flex w-full flex-col gap-y-4')}>
          {/* Desktop Title */}
          {!isMobile && (
            <h1 className={cn(headerTitleVariants({ variant: headerVariant }))}>
              {headerTitle ? headerTitle : ''}
            </h1>
          )}

          {/* Mobile Title */}
          {isMobile && (
            <h1
              className={cn(
                'mt-24 px-20 pb-12 text-text-heading',
                onGoBack ? 'text-header-3-medium' : 'text-header-2-medium'
              )}
            >
              {headerTitle ? headerTitle : ''}
            </h1>
          )}

          {/* Pin Button */}
          {onPin && !isMobile && (
            <Link
              leftIcon={isPinned ? 'Pin_fill_1' : 'Pin_light_1'}
              onClick={onPin}
              className="w-fit"
            >
              {isPinned ? 'Unpin from Homepage' : 'Pin to Homepage'}
            </Link>
          )}
        </div>
      </div>
    </div>
  );
};
