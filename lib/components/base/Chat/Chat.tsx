import { ChangeEvent, Dispatch, ReactNode } from 'react';

import { MessageType } from './Chat.types';
import {
  ChatActionsProps,
  ChatInputContainer,
  ChatInputProps,
  GreetingMessage,
  HintsProps,
  MessagesList
} from './components';
import { ChatWrapper, CommonChatWrapperProps } from './components/ChatWrapper';

import { Tooltip } from '../Tooltip';

export type ChatProps = {
  isFetching?: boolean;
  withGreetingMessage?: boolean;
  messages: MessageType[];
  attachments?: ReactNode;
  disabled?: boolean;
  textareaDisabledTooltip?: ReactNode;

  value: string;
  setValue: Dispatch<React.SetStateAction<string>>;

  chatWrapperProps?: CommonChatWrapperProps;
  chatInputProps?: Omit<ChatInputProps, 'value' | 'onChange'>;
  chatActionsProps?: Omit<
    ChatActionsProps,
    'isDirty' | 'disabled' | 'isFetching'
  >;
} & HintsProps;

export const Chat = ({
  isFetching = false,
  withGreetingMessage,
  messages,
  attachments,
  disabled,
  textareaDisabledTooltip,
  value,
  setValue,
  hints,
  onHint,

  chatWrapperProps,
  chatInputProps,
  chatActionsProps
}: ChatProps) => {
  const isDirty = value.trim() !== '';

  const getShortMessage = () => {
    if (chatWrapperProps?.shortMessage) return chatWrapperProps?.shortMessage;

    const lastUsersMessage = messages
      .filter((message) => message.sender === 'user')
      .at(-1);

    return lastUsersMessage
      ? lastUsersMessage.content || lastUsersMessage.text
      : '';
  };

  const renderContent = () => {
    if (!messages?.length) {
      if (!withGreetingMessage) return null;

      return <GreetingMessage />;
    }

    return (
      <MessagesList
        messages={messages}
        isFetching={isFetching}
        disabled={disabled}
        hints={hints}
        onHint={onHint}
      />
    );
  };

  const handleSubmit = () => {
    chatActionsProps?.onSendValue?.();
    setValue('');
  };

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    setValue(e.target.value);
  };

  return (
    <ChatWrapper
      {...chatWrapperProps}
      disabled={disabled}
      shortMessage={getShortMessage()}
    >
      <div className="flex h-auto w-full flex-col gap-y-20">
        {renderContent()}
        <div className="flex flex-col gap-8">
          <Tooltip title={disabled ? textareaDisabledTooltip : ''}>
            <ChatInputContainer
              disabled={disabled}
              isFetching={isFetching}
              inputProps={{
                ...chatInputProps,
                value,
                onChange: handleChange
              }}
              actionsProps={{
                ...chatActionsProps,
                onSendValue: handleSubmit,
                isDirty
              }}
            />
          </Tooltip>
          {attachments}
        </div>
      </div>
    </ChatWrapper>
  );
};
