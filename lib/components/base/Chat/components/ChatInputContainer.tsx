import { ChatInput, ChatInputProps } from './ChatInput';
import { ChatActionsProps, ChatInputActions } from './ChatInputActions';

import { cn } from '../../../../utils';

interface ChatInputContainerProps {
  disabled?: boolean;
  isFetching?: boolean;
  inputProps?: ChatInputProps;
  actionsProps?: ChatActionsProps;
}

export const ChatInputContainer = ({
  disabled = false,
  isFetching,
  inputProps,
  actionsProps
}: ChatInputContainerProps) => {
  return (
    <div
      data-attributes="ChatInputContainer"
      className={cn(
        'group',
        'flex size-full min-h-[94px] flex-col gap-y-16 rounded-2xl border border-solid border-input-stroke-defualt p-[11px] text-body-1-regular',
        // rest state
        'has-[textarea:placeholder-shown]:border-transparent has-[textarea:placeholder-shown]:bg-input-fill-placeholder',
        'has-[textarea:not-placeholder-shown]:border-input-stroke-defualt has-[textarea:not-placeholder-shown]:bg-input-fill-active',
        // hover
        'has-[textarea:not(:disabled)]:hover:!border-input-stroke-hover',
        // active
        'has-[textarea:focus]:!border-input-stroke-active has-[textarea:focus]:!bg-input-fill-active',
        // disabled
        'has-[textarea:disabled]:cursor-not-allowed has-[textarea:disabled]:border-input-stroke-disabled has-[textarea:disabled]:bg-input-fill-disabled'
      )}
    >
      <ChatInput {...inputProps} disabled={disabled || isFetching} />
      <ChatInputActions
        {...actionsProps}
        disabled={disabled}
        isFetching={isFetching}
      />
    </div>
  );
};
