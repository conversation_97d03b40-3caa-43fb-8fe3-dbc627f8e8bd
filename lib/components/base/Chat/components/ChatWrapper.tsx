import {
  PropsWithChildren,
  ReactNode,
  useEffect,
  useRef,
  useState
} from 'react';

import { IconButton } from '../../Button';
import { Icon } from '../../Icon';
import { Text } from '../../Text';

import { cn } from '../../../../utils';

export type CommonChatWrapperProps = {
  chatWrapperClassName?: string;
  isExpandable?: boolean;
  defaultCollapseState?: boolean;
} & PreviewMessageProps;

type ChatWrapperProps = CommonChatWrapperProps & PropsWithChildren;

export const ChatWrapper = ({
  chatWrapperClassName,
  shortMessage,
  isExpandable = false,
  defaultCollapseState = false,
  disabled,
  children
}: ChatWrapperProps) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapseState);
  const [height, setHeight] = useState('auto');
  const contentRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!isExpandable || !contentRef.current) return;
    const el = contentRef.current;

    const updateHeight = () => {
      const newHeight = el.offsetHeight;
      setHeight(newHeight + 'px');
    };

    updateHeight();

    const observer = new ResizeObserver(() => {
      updateHeight();
    });

    observer.observe(el);
    return () => {
      observer.disconnect();
    };
  }, [isCollapsed, isExpandable]);

  return (
    <div
      data-attributes="ChatWrapper"
      className={cn(
        'overflow-hidden rounded-[20px] border border-solid border-divider-mid bg-surface-grey_0',
        chatWrapperClassName
      )}
      ref={wrapperRef}
      style={{ height, transition: 'height 0.4s ease' }}
    >
      <div ref={contentRef} className="p-[15px]">
        {isCollapsed ? (
          <PreviewMessage shortMessage={shortMessage} disabled={disabled} />
        ) : (
          children
        )}
        {isExpandable && (
          <div className="flex h-40 items-end justify-center">
            <IconButton
              icon={isCollapsed ? 'Expand_down' : 'Expand_up'}
              variant="Tertiary"
              className="rounded-full"
              size="Small"
              dataAttributes="ChatWrapperToggleButton"
              onClick={() => setIsCollapsed((prev) => !prev)}
            />
          </div>
        )}
      </div>
    </div>
  );
};

type PreviewMessageProps = {
  shortMessage?: ReactNode;
  disabled?: boolean;
};

const PreviewMessage = ({ shortMessage, disabled }: PreviewMessageProps) => (
  <div className="flex gap-x-8" data-attributes="CollapsedChat">
    <Icon
      name="Search"
      className={cn(
        disabled
          ? 'text-button-purple-fill-disabled'
          : 'text-button-purple-fill-rested'
      )}
    />
    <Text
      lineClamp={3}
      className={cn(
        'text-body-1-medium',
        disabled ? 'text-input-text-disabled' : 'text-text-heading'
      )}
      data-attributes="PreviewMessage"
    >
      {shortMessage}
    </Text>
  </div>
);
