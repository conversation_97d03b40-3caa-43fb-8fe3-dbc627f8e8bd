import { ReactNode, useMemo } from 'react';

import { Button, IconButton } from '../../Button';
import { ContextMenuTrigger, ContextualMenu } from '../../ContextualMenu';
import { TooltipProps } from '../../Tooltip';

import { cn } from '../../../../utils';

export type ChatActionsProps = {
  disabled?: boolean;
  isFetching?: boolean;
  isDirty?: boolean;
  menuActions?: ReactNode;
  disabledMenuActions?: boolean;
  menuActionsTooltip?: string;
  otherActions?: ReactNode;
  submitButtonTooltip?: string;
  onEnhanceValue?: () => void;
  onSendValue?: () => void;
};

export const ChatInputActions = ({
  disabled,
  isFetching,

  isDirty = false,
  menuActions,
  disabledMenuActions,
  menuActionsTooltip,
  submitButtonTooltip,
  onEnhanceValue,
  onSendValue
}: ChatActionsProps) => {
  const submitTooltip: TooltipProps | undefined = useMemo(() => {
    if (disabled) return undefined;

    const tooltip = !isDirty && submitButtonTooltip;
    return tooltip
      ? {
          title: tooltip,
          size: 'Small'
        }
      : undefined;
  }, [disabled, isDirty, submitButtonTooltip]);

  return (
    <div
      className="flex w-full justify-between"
      data-attributes="ChatInputActions"
    >
      <div className="flex gap-x-8">
        {menuActions && (
          <ContextualMenu
            placement="bottom-start"
            fallbackPlacements={['top-start']}
          >
            <ContextMenuTrigger>
              <IconButton
                disabled={disabled || disabledMenuActions}
                icon="Add_round"
                variant="Tertiary"
                tooltip={{
                  title: disabledMenuActions ? menuActionsTooltip : undefined
                }}
                size="Small"
                className={cn(
                  'rounded-full',
                  isDirty
                    ? '!bg-button-tertiary-fill-rested'
                    : '!bg-modal-button-fill-rested',
                  'group-has-[textarea:focus]:!bg-button-tertiary-fill-rested'
                )}
              />
            </ContextMenuTrigger>
            {menuActions}
          </ContextualMenu>
        )}
        {onEnhanceValue && (
          <Button
            leftIcon="AI_fill"
            size="Small"
            variant="SecondaryPurple"
            className="rounded-2xl"
            disabled={disabled || !isDirty}
            onClick={onEnhanceValue}
          >
            Enhance with AI
          </Button>
        )}
      </div>
      <IconButton
        icon="Arrow_top"
        className="size-32 rounded-full disabled:cursor-not-allowed"
        onClick={onSendValue}
        size="Small"
        disabled={isFetching || disabled || !isDirty}
        tooltip={submitTooltip}
      />
    </div>
  );
};
