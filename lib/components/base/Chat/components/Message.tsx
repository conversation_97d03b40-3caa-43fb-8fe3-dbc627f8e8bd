import { forwardRef, ReactNode } from 'react';

import { Icon } from '../../Icon';

interface MessageProps {
  text: ReactNode;
}

const AIMessage: React.FC<MessageProps> = ({ text }) => (
  <div
    data-attributes="AIMessage"
    className={
      'flex w-full flex-col gap-16 self-start rounded-xl bg-surface-grey_0 px-16 py-12 text-body-1-regular text-text-heading'
    }
  >
    <div className="flex items-center gap-4">
      <Icon
        name="AI_fill"
        className="!size-12 !min-h-12 !min-w-12 text-button-secondary-purple-fill-pressed"
      />
      <span className="text-body-4-regular">AI Answer</span>
    </div>
    <div className="h-[1px] w-full border-b border-solid border-divider-light" />
    <span className="text-body-1-regular text-text-heading">{text}</span>
  </div>
);

const UserMessage: React.FC<MessageProps> = ({ text }) => (
  <div
    className="max-w-[686px] self-end rounded-2xl bg-surface-grey_30 px-16 py-12 text-body-1-regular text-text-heading"
    data-attributes="UserMessage"
  >
    {text}
  </div>
);

const ProcessingMessage = forwardRef<HTMLDivElement>((_, ref) => (
  <div
    className="flex flex-col items-start gap-[10px]"
    data-attributes="ProcessingMessage"
    ref={ref}
  >
    <span className="text-body-3-regular text-text-tertiary">
      Processing your message…
    </span>

    <div className="flex gap-8" data-attributes="ProcessingLoader">
      <span className="dot animate-chase [animation-delay:0s]" />
      <span className="dot animate-chase [animation-delay:0.2s]" />
      <span className="dot animate-chase [animation-delay:0.4s]" />
    </div>
  </div>
));

export const Message = {
  AI: AIMessage,
  User: UserMessage,
  Processing: ProcessingMessage
};
