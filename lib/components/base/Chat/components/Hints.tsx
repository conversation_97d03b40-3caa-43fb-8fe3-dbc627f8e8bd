import { Hint } from '../Chat.types';

import { Button } from '../../Button';
import { Text } from '../../Text';

export type HintsProps = {
  disabled?: boolean;
  hints?: Hint[] | null;
  onHint?: (value: Hint) => void;
};

export const Hints: React.FC<HintsProps> = ({ disabled, hints, onHint }) => {
  const handleHintClick = (hint: Hint) => {
    onHint?.(hint);
  };

  if (!hints?.length) return null;

  return (
    <div
      data-attributes="HintsContainer"
      className="flex flex-col flex-nowrap items-end gap-8 md:flex-row md:items-start"
    >
      {hints.map((hint, index) => (
        <Button
          key={'hint-' + index}
          onClick={() => handleHintClick(hint)}
          variant="Secondary"
          className="max-h-[38px] rounded-[20px] !py-16 text-text-heading"
          disabled={disabled}
        >
          <Text lineClamp={1} className="truncate whitespace-nowrap">
            {hint.text}
          </Text>
        </Button>
      ))}
    </div>
  );
};
