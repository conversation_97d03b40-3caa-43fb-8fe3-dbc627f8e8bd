import { PROMPT_MAX_LENGTH } from '../Chat.types';

import { Textarea, TextareaProps } from '../../Textarea';

export type ChatInputProps = TextareaProps;

export const ChatInput = ({
  placeholder = 'Ask anything...',
  disabled = false,
  rows = 1,
  maxRows = 3,
  value = '',
  ...rest
}: ChatInputProps) => (
  <Textarea
    value={value}
    placeholder={placeholder}
    disabled={disabled}
    rows={rows}
    maxRows={maxRows}
    label=""
    showLabel={false}
    hideCounter
    className="resize-none bg-transparent px-8 outline-none disabled:cursor-not-allowed md:border-none"
    maxLength={PROMPT_MAX_LENGTH}
    {...rest}
  />
);
