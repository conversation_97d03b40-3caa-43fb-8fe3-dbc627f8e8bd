import { useEffect, useRef } from 'react';

import { Hints, HintsProps } from './Hints';
import { Message } from './Message';

import { MessageType, SenderTypes } from '../Chat.types';

type MessagesProps = {
  messages?: MessageType[];
  isFetching?: boolean;
  disabled?: boolean;
} & HintsProps;

export const MessagesList: React.FC<MessagesProps> = ({
  messages = [],
  isFetching,
  disabled,

  hints,
  onHint
}) => {
  const scrollRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    scrollRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  if (!messages?.length) return null;

  return (
    <div
      data-attributes="MessagesList"
      className="gradient-surface-gray flex max-h-[440px] flex-col gap-20 overflow-y-auto rounded-lg p-8"
    >
      <div className="flex flex-col gap-20 self-stretch">
        {messages.map((msg, index) => {
          if (msg.sender === SenderTypes.User) {
            return <Message.User key={msg.id} text={msg.content ?? msg.text} />;
          }

          const isLast = messages.length - 1 === index;
          return (
            <div
              key={msg.id}
              ref={isLast && !isFetching ? scrollRef : undefined}
              className="flex flex-col gap-y-8"
              data-attributes="AIMessageWrapper"
            >
              <Message.AI text={msg.content ?? msg.text} />
              {isLast && (
                <Hints disabled={disabled} hints={hints} onHint={onHint} />
              )}
            </div>
          );
        })}
        {isFetching && <Message.Processing ref={scrollRef} />}
      </div>
    </div>
  );
};
