import { useState } from 'react';

import type { Meta, StoryObj } from '@storybook/react';

import { Chat, ChatProps } from './Chat';
import { Hint, MessageType } from './Chat.types';

import { Badge } from '../Badge';
import { Button } from '../Button';
import { ContextualMenuList, ContextualMenuListItem } from '../ContextualMenu';
import { Drawer } from '../Drawer';
import { Input } from '../Input';
import { Modal } from '../Modal';

const meta = {
  title: 'Design System/Components/Chat/Chat',
  component: Chat,
  tags: ['autodocs']
} satisfies Meta<typeof Chat>;

export default meta;

type Story = StoryObj<typeof Chat>;

const useMessages = (initialMessages: MessageType[] = []) => {
  const [messages, setMessages] = useState<MessageType[]>(initialMessages);
  const [hints, setHints] = useState<Hint[] | null>(null);
  const [isFetching, setIsFetching] = useState(false);

  const handleSendMessage = (message: string, attachment?: string) => {
    const userMessage: MessageType = {
      id: Date.now().toString(),
      text: message,
      sender: 'user',
      content: attachment ? (
        <div>
          <p>{message}</p>
          <strong>Attachment:</strong> {attachment}
        </div>
      ) : undefined
    };

    setHints(null);
    setMessages((prev) => [...prev, userMessage]);
    setIsFetching(true);

    setTimeout(() => {
      const systemMessage: MessageType = {
        id: (Date.now() + 1).toString(),
        text: `You said: "${message}". How else can I assist you?`,
        sender: 'bot'
      };

      setMessages((prev) => [...prev, systemMessage]);

      if (messages.length % 4 === 0) {
        setHints([
          {
            id: '1',
            text: 'Follow up AI generated question A?'
          },
          {
            id: '2',
            text: 'Follow up AI generated question B?'
          },
          {
            id: '3',
            text: 'Follow up AI generated question C?'
          }
        ]);
      }

      setIsFetching(false);
    }, 10000);
  };

  return {
    messages,
    hints,
    isFetching,
    setHints,
    handleSendMessage
  };
};

const useMenuActions = (
  setValue: React.Dispatch<React.SetStateAction<string>>
) => {
  const [attachments, setAttachments] = useState<string>();

  const [modalType, setModalType] = useState('');
  const [textInModal, setTextInModal] = useState('');

  const handleAdd = () => {
    if (modalType === 'attachment') {
      setAttachments(textInModal);
    }

    if (modalType === 'update') {
      setValue((prevValue) =>
        `${prevValue}${prevValue ? '\n' : ''}${textInModal}`.trim()
      );
    }

    setTextInModal('');
    setModalType('');
  };

  const menuActions = (
    <ContextualMenuList>
      <ContextualMenuListItem onClick={() => setModalType('attachment')}>
        Add Attachment
      </ContextualMenuListItem>
      <ContextualMenuListItem onClick={() => setModalType('update')}>
        Update Prompt
      </ContextualMenuListItem>
    </ContextualMenuList>
  );

  const renderModal = () => {
    if (!modalType) return null;

    return (
      <Modal isOpen footer={<Button onClick={handleAdd}>Add</Button>}>
        <Input
          placeholder="Type here..."
          value={textInModal}
          onChange={(e) => setTextInModal(e.target.value)}
        />
      </Modal>
    );
  };

  return {
    attachments,
    menuActions,
    renderModal,
    setAttachments
  };
};

const useTextAreaForm = () => {
  const [value, setValue] = useState('');

  const isDirty = value.trim() !== '';

  return { value, setValue, isDirty };
};

const useEnhance = (setValue: React.Dispatch<React.SetStateAction<string>>) => {
  const [isEnhanceOpen, setIsEnhanceOpen] = useState(false);

  const handleEnhanceOpen = () => {
    setIsEnhanceOpen(true);
  };

  const handleEnhanceClose = () => {
    setIsEnhanceOpen(false);
  };

  const handleApplyEnhance = () => {
    setValue((prev) => `This is enhanced text: ${prev}!`);
    handleEnhanceClose();
  };

  const renderEnhanceDrawer = () => {
    if (!isEnhanceOpen) return null;

    return (
      <Drawer
        isOpen
        onClose={handleEnhanceClose}
        header="Enhance With AI"
        footer={
          <>
            <Button
              variant="Secondary"
              size="Large"
              className="flex-auto"
              onClick={handleEnhanceClose}
            >
              Cancel
            </Button>
            <Button
              variant="Primary"
              size="Large"
              className="flex-auto"
              onClick={handleApplyEnhance}
            >
              Apply
            </Button>
          </>
        }
      />
    );
  };

  return {
    handleEnhanceOpen,
    renderEnhanceDrawer
  };
};

const ChatExample = (props: ChatProps) => {
  const { messages: initialMessages } = props;

  const { messages, hints, isFetching, setHints, handleSendMessage } =
    useMessages(initialMessages);
  const { value, setValue } = useTextAreaForm();
  const { attachments, menuActions, renderModal, setAttachments } =
    useMenuActions(setValue);
  const { handleEnhanceOpen, renderEnhanceDrawer } = useEnhance(setValue);

  const handleSendValue = () => {
    handleSendMessage(value, attachments);
    setValue('');
    setAttachments('');
  };

  const handleOnHint = (hint: Hint) => {
    setHints(null);
    setValue(hint.text);
  };

  const disabled = isFetching;

  const attachmentsEl = attachments ? (
    <div>
      <Badge
        onRemove={() => setAttachments('')}
        disabled={disabled}
        label={attachments}
        className="w-auto [&>span]:leading-[24px]"
        tooltipMode="off"
        showRemove
      />
    </div>
  ) : null;

  return (
    <div className="m-auto flex w-full max-w-[800px] flex-col">
      <Chat
        isFetching={isFetching}
        withGreetingMessage={!messages.length}
        messages={messages}
        attachments={attachmentsEl}
        disabled={disabled}
        value={value}
        setValue={setValue}
        hints={hints}
        onHint={handleOnHint}
        chatWrapperProps={{
          isExpandable: !!messages.length
        }}
        chatActionsProps={{
          menuActions,
          disabledMenuActions: !!attachments,
          menuActionsTooltip: 'Only one reference can be added',
          submitButtonTooltip: 'Please enter a search query',
          onSendValue: handleSendValue,
          onEnhanceValue: handleEnhanceOpen
        }}
      />
      {renderModal()}
      {renderEnhanceDrawer()}
    </div>
  );
};

export const Default: Story = {
  name: 'Default',
  render: (args: ChatProps) => <ChatExample {...args} />
};

const DisabledChat = () => {
  const [value, setValue] = useState('');
  const hints = [
    {
      id: '1',
      text: 'Follow up AI generated question A?'
    },
    {
      id: '2',
      text: 'Follow up AI generated question B?'
    },
    {
      id: '3',
      text: 'Follow up AI generated question C?'
    }
  ];

  const messages: MessageType[] = [
    {
      id: '1',
      text: 'Hello!',
      sender: 'user'
    },
    {
      id: '2',
      text: 'Hi there!',
      sender: 'bot'
    }
  ];

  return (
    <Chat
      disabled
      textareaDisabledTooltip="Chat is disabled"
      value={value}
      setValue={setValue}
      messages={messages}
      chatWrapperProps={{ isExpandable: true }}
      hints={hints}
    />
  );
};

export const Disabled: Story = {
  name: 'Disabled',
  render: DisabledChat
};
