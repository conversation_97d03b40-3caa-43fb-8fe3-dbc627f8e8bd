import { useMemo } from 'react';

export interface DataColumnGroupProps {
  title: string;
  columns: string[];
  className?: string;
}

export interface DataColumnProps {
  key: string;
  title?: string;
  caption?: string;
  sortable?: boolean;
  width?: number;
  minWidth?: string | number;
  grow?: number;
  className?: string;
  headerClassName?: string;
  canCopy?: boolean;
  fixed?: 'left' | 'right';
}

export interface UseColumnGroupsConfig {
  columns: DataColumnProps[];
  groups?: DataColumnGroupProps[];
}

export interface UseColumnGroupsReturn {
  columnGroups: DataColumnGroupProps[] | undefined;
  groupedColumns: DataColumnProps[];
  ungroupedColumns: DataColumnProps[];
  hasGroups: boolean;
}

/**
 * Hook for managing column groups in tables
 * Organizes columns into groups and provides utilities for rendering
 */
export const useColumnGroups = ({
  columns,
  groups
}: UseColumnGroupsConfig): UseColumnGroupsReturn => {
  const result = useMemo(() => {
    if (!groups || groups.length === 0) {
      return {
        columnGroups: undefined,
        groupedColumns: [],
        ungroupedColumns: columns,
        hasGroups: false
      };
    }

    // Get all column keys that are part of groups
    const groupedColumnKeys = new Set(groups.flatMap((group) => group.columns));

    // Separate grouped and ungrouped columns
    const groupedColumns = columns.filter((col) =>
      groupedColumnKeys.has(col.key)
    );
    const ungroupedColumns = columns.filter(
      (col) => !groupedColumnKeys.has(col.key)
    );

    // Validate that all group column keys exist in the columns array
    const validGroups = groups.filter((group) => {
      const validColumns = group.columns.filter((colKey) =>
        columns.some((col) => col.key === colKey)
      );
      return validColumns.length > 0;
    });

    return {
      columnGroups: validGroups.length > 0 ? validGroups : undefined,
      groupedColumns,
      ungroupedColumns,
      hasGroups: validGroups.length > 0
    };
  }, [columns, groups]);

  return result;
};

/**
 * Utility function to create column groups
 */
export const createColumnGroup = (
  title: string,
  columnKeys: string[],
  className?: string
): DataColumnGroupProps => ({
  title,
  columns: columnKeys,
  className
});

/**
 * Utility function to create multiple column groups
 */
export const createColumnGroups = (
  groupConfigs: Array<{
    title: string;
    columnKeys: string[];
    className?: string;
  }>
): DataColumnGroupProps[] => {
  return groupConfigs.map((config) =>
    createColumnGroup(config.title, config.columnKeys, config.className)
  );
};
