# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@adobe/css-tools@^4.4.0":
  version "4.4.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@adobe/css-tools/-/css-tools-4.4.3.tgz#beebbefb0264fdeb32d3052acae0e0d94315a9a2"
  integrity sha1-vuu++wJk/esy0wUqyuDg2UMVqaI=

"@alloc/quick-lru@^5.2.0":
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@alloc/quick-lru/-/quick-lru-5.2.0.tgz#7bf68b20c0a350f936915fcae06f58e32007ce30"
  integrity sha1-e/aLIMCjUPk2kV/K4G9Y4yAHzjA=

"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.3.0":
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@ampproject/remapping/-/remapping-2.3.0.tgz#ed441b6fa600072520ce18b43d2c8cc8caecc7f4"
  integrity sha1-7UQbb6YAByUgzhi0PSyMyMrsx/Q=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@babel/code-frame@^7.0.0", "@babel/code-frame@^7.10.4", "@babel/code-frame@^7.12.13", "@babel/code-frame@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/code-frame/-/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/compat-data@^7.20.5", "@babel/compat-data@^7.27.2":
  version "7.27.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/compat-data/-/compat-data-7.27.5.tgz#7d0658ec1a8420fc866d1df1b03bea0e79934c82"
  integrity sha1-fQZY7BqEIPyGbR3xsDvqDnmTTII=

"@babel/core@^7.11.6", "@babel/core@^7.12.3", "@babel/core@^7.18.9", "@babel/core@^7.21.3", "@babel/core@^7.23.9", "@babel/core@^7.26.10", "@babel/core@^7.5.5":
  version "7.27.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/core/-/core-7.27.4.tgz#cc1fc55d0ce140a1828d1dd2a2eba285adbfb3ce"
  integrity sha1-zB/FXQzhQKGCjR3Souuiha2/s84=
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.4"
    "@babel/parser" "^7.27.4"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.27.4"
    "@babel/types" "^7.27.3"
    convert-source-map "^2.0.0"
    debug "^4.1.0"
    gensync "^1.0.0-beta.2"
    json5 "^2.2.3"
    semver "^6.3.1"

"@babel/generator@^7.27.3", "@babel/generator@^7.7.2":
  version "7.27.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/generator/-/generator-7.27.5.tgz#3eb01866b345ba261b04911020cbe22dd4be8c8c"
  integrity sha1-PrAYZrNFuiYbBJEQIMviLdS+jIw=
  dependencies:
    "@babel/parser" "^7.27.5"
    "@babel/types" "^7.27.3"
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.25"
    jsesc "^3.0.2"

"@babel/helper-annotate-as-pure@^7.27.1":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz#f31fd86b915fc4daf1f3ac6976c59be7084ed9c5"
  integrity sha1-8x/Ya5FfxNrx86xpdsWb5whO2cU=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/helper-compilation-targets@^7.20.7", "@babel/helper-compilation-targets@^7.27.2":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz#46a0f6efab808d51d29ce96858dd10ce8732733d"
  integrity sha1-RqD276uAjVHSnOloWN0Qzocycz0=
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    browserslist "^4.24.0"
    lru-cache "^5.1.1"
    semver "^6.3.1"

"@babel/helper-module-imports@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz#7ef769a323e2655e126673bb6d2d6913bbead204"
  integrity sha1-fvdpoyPiZV4SZnO7bS1pE7vq0gQ=
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz#db0bbcfba5802f9ef7870705a7ef8788508ede02"
  integrity sha1-2wu8+6WAL573hwcFp++HiFCO3gI=
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-plugin-utils@^7.0.0", "@babel/helper-plugin-utils@^7.10.4", "@babel/helper-plugin-utils@^7.12.13", "@babel/helper-plugin-utils@^7.14.5", "@babel/helper-plugin-utils@^7.20.2", "@babel/helper-plugin-utils@^7.27.1", "@babel/helper-plugin-utils@^7.8.0":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz#ddb2f876534ff8013e6c2b299bf4d39b3c51d44c"
  integrity sha1-3bL4dlNP+AE+bCspm/TTmzxR1Ew=

"@babel/helper-string-parser@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz#54da796097ab19ce67ed9f88b47bb2ec49367687"
  integrity sha1-VNp5YJerGc5n7Z+ItHuy7Ek2doc=

"@babel/helper-validator-identifier@^7.15.7", "@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@babel/helper-validator-option@^7.27.1":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz#fa52f5b1e7db1ab049445b421c4471303897702f"
  integrity sha1-+lL1sefbGrBJRFtCHERxMDiXcC8=

"@babel/helpers@^7.27.4":
  version "7.27.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/helpers/-/helpers-7.27.6.tgz#6456fed15b2cb669d2d1fabe84b66b34991d812c"
  integrity sha1-ZFb+0VsstmnS0fq+hLZrNJkdgSw=
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.6"

"@babel/parser@^7.1.0", "@babel/parser@^7.14.7", "@babel/parser@^7.20.7", "@babel/parser@^7.23.9", "@babel/parser@^7.25.4", "@babel/parser@^7.27.2", "@babel/parser@^7.27.4", "@babel/parser@^7.27.5":
  version "7.27.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/parser/-/parser-7.27.5.tgz#ed22f871f110aa285a6fd934a0efed621d118826"
  integrity sha1-7SL4cfEQqihab9k0oO/tYh0RiCY=
  dependencies:
    "@babel/types" "^7.27.3"

"@babel/plugin-proposal-object-rest-spread@^7.5.5":
  version "7.20.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-proposal-object-rest-spread/-/plugin-proposal-object-rest-spread-7.20.7.tgz#aa662940ef425779c75534a5c41e9d936edc390a"
  integrity sha1-qmYpQO9CV3nHVTSlxB6dk27cOQo=
  dependencies:
    "@babel/compat-data" "^7.20.5"
    "@babel/helper-compilation-targets" "^7.20.7"
    "@babel/helper-plugin-utils" "^7.20.2"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-transform-parameters" "^7.20.7"

"@babel/plugin-syntax-async-generators@^7.8.4":
  version "7.8.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz#a983fb1aeb2ec3f6ed042a210f640e90e786fe0d"
  integrity sha1-qYP7Gusuw/btBCohD2QOkOeG/g0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-bigint@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz#4c9a6f669f5d0cdf1b90a1671e9a146be5300cea"
  integrity sha1-TJpvZp9dDN8bkKFnHpoUa+UwDOo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-class-properties@^7.12.13":
  version "7.12.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz#b5c987274c4a3a82b89714796931a6b53544ae10"
  integrity sha1-tcmHJ0xKOoK4lxR5aTGmtTVErhA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.12.13"

"@babel/plugin-syntax-class-static-block@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz#195df89b146b4b78b3bf897fd7a257c84659d406"
  integrity sha1-GV34mxRrS3izv4l/16JXyEZZ1AY=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-import-attributes@^7.24.7":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz#34c017d54496f9b11b61474e7ea3dfd5563ffe07"
  integrity sha1-NMAX1USW+bEbYUdOfqPf1VY//gc=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-import-meta@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz#ee601348c370fa334d2207be158777496521fd51"
  integrity sha1-7mATSMNw+jNNIge+FYd3SWUh/VE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-json-strings@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz#01ca21b668cd8218c9e640cb6dd88c5412b2c96a"
  integrity sha1-AcohtmjNghjJ5kDLbdiMVBKyyWo=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-jsx@^7.27.1", "@babel/plugin-syntax-jsx@^7.7.2":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz#2f9beb5eff30fa507c5532d107daac7b888fa34c"
  integrity sha1-L5vrXv8w+lB8VTLRB9qse4iPo0w=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-syntax-logical-assignment-operators@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz#ca91ef46303530448b906652bac2e9fe9941f699"
  integrity sha1-ypHvRjA1MESLkGZSusLp/plB9pk=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz#167ed70368886081f74b5c36c65a88c03b66d1a9"
  integrity sha1-Fn7XA2iIYIH3S1w2xlqIwDtm0ak=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-numeric-separator@^7.10.4":
  version "7.10.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz#b9b070b3e33570cd9fd07ba7fa91c0dd37b9af97"
  integrity sha1-ubBws+M1cM2f0Hun+pHA3Te5r5c=
  dependencies:
    "@babel/helper-plugin-utils" "^7.10.4"

"@babel/plugin-syntax-object-rest-spread@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz#60e225edcbd98a640332a2e72dd3e66f1af55871"
  integrity sha1-YOIl7cvZimQDMqLnLdPmbxr1WHE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-catch-binding@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz#6111a265bcfb020eb9efd0fdfd7d26402b9ed6c1"
  integrity sha1-YRGiZbz7Ag6579D9/X0mQCue1sE=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-optional-chaining@^7.8.3":
  version "7.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz#4f69c2ab95167e0180cd5336613f8c5788f7d48a"
  integrity sha1-T2nCq5UWfgGAzVM2YT+MV4j31Io=
  dependencies:
    "@babel/helper-plugin-utils" "^7.8.0"

"@babel/plugin-syntax-private-property-in-object@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz#0dc6671ec0ea22b6e94a1114f857970cd39de1ad"
  integrity sha1-DcZnHsDqIrbpShEU+FeXDNOd4a0=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-top-level-await@^7.14.5":
  version "7.14.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz#c1cfdadc35a646240001f06138247b741c34d94c"
  integrity sha1-wc/a3DWmRiQAAfBhOCR7dBw02Uw=
  dependencies:
    "@babel/helper-plugin-utils" "^7.14.5"

"@babel/plugin-syntax-typescript@^7.7.2":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz#5147d29066a793450f220c63fa3a9431b7e6dd18"
  integrity sha1-UUfSkGank0UPIgxj+jqUMbfm3Rg=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-destructuring@^7.5.0":
  version "7.27.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.3.tgz#3cc8299ed798d9a909f8d66ddeb40849ec32e3b0"
  integrity sha1-PMgpnteY2akJ+NZt3rQISewy47A=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-parameters@^7.20.7":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz#80334b54b9b1ac5244155a0c8304a187a618d5a7"
  integrity sha1-gDNLVLmxrFJEFVoMgwShh6YY1ac=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-self@^7.25.9":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz#af678d8506acf52c577cac73ff7fe6615c85fc92"
  integrity sha1-r2eNhQas9SxXfKxz/3/mYVyF/JI=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.25.9":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz#dcfe2c24094bb757bf73960374e7c55e434f19f0"
  integrity sha1-3P4sJAlLt1e/c5YDdOfFXkNPGfA=
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx@^7.3.0":
  version "7.27.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz#1023bc94b78b0a2d68c82b5e96aed573bcfb9db0"
  integrity sha1-ECO8lLeLCi1oyCtelq7Vc7z7nbA=
  dependencies:
    "@babel/helper-annotate-as-pure" "^7.27.1"
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-plugin-utils" "^7.27.1"
    "@babel/plugin-syntax-jsx" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/runtime@^7.0.0", "@babel/runtime@^7.12.13", "@babel/runtime@^7.7.6", "@babel/runtime@^7.8.7":
  version "7.28.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/runtime/-/runtime-7.28.3.tgz#75c5034b55ba868121668be5d5bb31cc64e6e61a"
  integrity sha1-dcUDS1W6hoEhZovl1bsxzGTm5ho=

"@babel/runtime@^7.12.5", "@babel/runtime@^7.17.8", "@babel/runtime@^7.5.5":
  version "7.27.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/runtime/-/runtime-7.27.6.tgz#ec4070a04d76bae8ddbb10770ba55714a417b7c6"
  integrity sha1-7EBwoE12uujduxB3C6VXFKQXt8Y=

"@babel/template@^7.27.2", "@babel/template@^7.3.3":
  version "7.27.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/template/-/template-7.27.2.tgz#fa78ceed3c4e7b63ebf6cb39e5852fca45f6809d"
  integrity sha1-+njO7TxOe2Pr9ss55YUvykX2gJ0=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.18.9", "@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.27.4":
  version "7.27.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/traverse/-/traverse-7.27.4.tgz#b0045ac7023c8472c3d35effd7cc9ebd638da6ea"
  integrity sha1-sARaxwI8hHLD017/18yevWONpuo=
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.27.3"
    "@babel/parser" "^7.27.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.27.3"
    debug "^4.3.1"
    globals "^11.1.0"

"@babel/types@^7.0.0", "@babel/types@^7.18.9", "@babel/types@^7.20.7", "@babel/types@^7.21.3", "@babel/types@^7.25.4", "@babel/types@^7.27.1", "@babel/types@^7.27.3", "@babel/types@^7.27.6", "@babel/types@^7.3.3":
  version "7.27.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@babel/types/-/types-7.27.6.tgz#a434ca7add514d4e646c80f7375c0aa2befc5535"
  integrity sha1-pDTKet1RTU5kbID3N1wKor78VTU=
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@bcoe/v8-coverage@^0.2.3":
  version "0.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz#75a2e8b51cb758a7553d6804a5932d7aace75c39"
  integrity sha1-daLotRy3WKdVPWgEpZMteqznXDk=

"@bcoe/v8-coverage@^1.0.2":
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@bcoe/v8-coverage/-/v8-coverage-1.0.2.tgz#bbe12dca5b4ef983a0d0af4b07b9bc90ea0ababa"
  integrity sha1-u+EtyltO+YOg0K9LB7m8kOoKuro=

"@bufbuild/protobuf@^2.0.0":
  version "2.5.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@bufbuild/protobuf/-/protobuf-2.5.2.tgz#9b6cf005c50fdda72701da82f8db44635463d730"
  integrity sha1-m2zwBcUP3acnAdqC+NtEY1Rj1zA=

"@changesets/apply-release-plan@^7.0.12":
  version "7.0.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/apply-release-plan/-/apply-release-plan-7.0.12.tgz#8413977f117fa95f6e2db6f0c35479a2eba6960a"
  integrity sha1-hBOXfxF/qV9uLbbww1R5ouumlgo=
  dependencies:
    "@changesets/config" "^3.1.1"
    "@changesets/get-version-range-type" "^0.4.0"
    "@changesets/git" "^3.0.4"
    "@changesets/should-skip-package" "^0.1.2"
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"
    detect-indent "^6.0.0"
    fs-extra "^7.0.1"
    lodash.startcase "^4.4.0"
    outdent "^0.5.0"
    prettier "^2.7.1"
    resolve-from "^5.0.0"
    semver "^7.5.3"

"@changesets/assemble-release-plan@^6.0.9":
  version "6.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/assemble-release-plan/-/assemble-release-plan-6.0.9.tgz#8aa5baf0037a85812e320172e83b92ca31e85fd6"
  integrity sha1-iqW68AN6hYEuMgFy6DuSyjHoX9Y=
  dependencies:
    "@changesets/errors" "^0.2.0"
    "@changesets/get-dependents-graph" "^2.1.3"
    "@changesets/should-skip-package" "^0.1.2"
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"
    semver "^7.5.3"

"@changesets/changelog-git@^0.2.1":
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/changelog-git/-/changelog-git-0.2.1.tgz#7f311f3dc11eae1235aa7fd2c1807112962b409b"
  integrity sha1-fzEfPcEerhI1qn/SwYBxEpYrQJs=
  dependencies:
    "@changesets/types" "^6.1.0"

"@changesets/cli@2.29.5":
  version "2.29.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/cli/-/cli-2.29.5.tgz#7ff589686b5a16b6790962ac09182c7462db4899"
  integrity sha1-f/WJaGtaFrZ5CWKsCRgsdGLbSJk=
  dependencies:
    "@changesets/apply-release-plan" "^7.0.12"
    "@changesets/assemble-release-plan" "^6.0.9"
    "@changesets/changelog-git" "^0.2.1"
    "@changesets/config" "^3.1.1"
    "@changesets/errors" "^0.2.0"
    "@changesets/get-dependents-graph" "^2.1.3"
    "@changesets/get-release-plan" "^4.0.13"
    "@changesets/git" "^3.0.4"
    "@changesets/logger" "^0.1.1"
    "@changesets/pre" "^2.0.2"
    "@changesets/read" "^0.6.5"
    "@changesets/should-skip-package" "^0.1.2"
    "@changesets/types" "^6.1.0"
    "@changesets/write" "^0.4.0"
    "@manypkg/get-packages" "^1.1.3"
    ansi-colors "^4.1.3"
    ci-info "^3.7.0"
    enquirer "^2.4.1"
    external-editor "^3.1.0"
    fs-extra "^7.0.1"
    mri "^1.2.0"
    p-limit "^2.2.0"
    package-manager-detector "^0.2.0"
    picocolors "^1.1.0"
    resolve-from "^5.0.0"
    semver "^7.5.3"
    spawndamnit "^3.0.1"
    term-size "^2.1.0"

"@changesets/config@^3.1.1":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/config/-/config-3.1.1.tgz#3e5b1f74236a4552c5f4eddf2bd05a43a0b71160"
  integrity sha1-PlsfdCNqRVLF9O3fK9BaQ6C3EWA=
  dependencies:
    "@changesets/errors" "^0.2.0"
    "@changesets/get-dependents-graph" "^2.1.3"
    "@changesets/logger" "^0.1.1"
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"
    fs-extra "^7.0.1"
    micromatch "^4.0.8"

"@changesets/errors@^0.2.0":
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/errors/-/errors-0.2.0.tgz#3c545e802b0f053389cadcf0ed54e5636ff9026a"
  integrity sha1-PFRegCsPBTOJytzw7VTlY2/5Amo=
  dependencies:
    extendable-error "^0.1.5"

"@changesets/get-dependents-graph@^2.1.3":
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/get-dependents-graph/-/get-dependents-graph-2.1.3.tgz#cd31b39daab7102921fb65acdcb51b4658502eee"
  integrity sha1-zTGznaq3ECkh+2Ws3LUbRlhQLu4=
  dependencies:
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"
    picocolors "^1.1.0"
    semver "^7.5.3"

"@changesets/get-release-plan@^4.0.13":
  version "4.0.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/get-release-plan/-/get-release-plan-4.0.13.tgz#02e2d9b89a3911bfc4bf1dafe237098b4b7454e9"
  integrity sha1-AuLZuJo5Eb/Evx2v4jcJi0t0VOk=
  dependencies:
    "@changesets/assemble-release-plan" "^6.0.9"
    "@changesets/config" "^3.1.1"
    "@changesets/pre" "^2.0.2"
    "@changesets/read" "^0.6.5"
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"

"@changesets/get-version-range-type@^0.4.0":
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/get-version-range-type/-/get-version-range-type-0.4.0.tgz#429a90410eefef4368502c41c63413e291740bf5"
  integrity sha1-QpqQQQ7v70NoUCxBxjQT4pF0C/U=

"@changesets/git@^3.0.4":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/git/-/git-3.0.4.tgz#75e3811ab407ec010beb51131ceb5c6b3975c914"
  integrity sha1-deOBGrQH7AEL61ETHOtcazl1yRQ=
  dependencies:
    "@changesets/errors" "^0.2.0"
    "@manypkg/get-packages" "^1.1.3"
    is-subdir "^1.1.1"
    micromatch "^4.0.8"
    spawndamnit "^3.0.1"

"@changesets/logger@^0.1.1":
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/logger/-/logger-0.1.1.tgz#9926ac4dc8fb00472fe1711603b6b4755d64b435"
  integrity sha1-mSasTcj7AEcv4XEWA7a0dV1ktDU=
  dependencies:
    picocolors "^1.1.0"

"@changesets/parse@^0.4.1":
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/parse/-/parse-0.4.1.tgz#18ba51d2eb784d27469034f06344f8fdcba586df"
  integrity sha1-GLpR0ut4TSdGkDTwY0T4/culht8=
  dependencies:
    "@changesets/types" "^6.1.0"
    js-yaml "^3.13.1"

"@changesets/pre@^2.0.2":
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/pre/-/pre-2.0.2.tgz#b35e84d25fca8b970340642ca04ce76c7fc34ced"
  integrity sha1-s16E0l/Ki5cDQGQsoEznbH/DTO0=
  dependencies:
    "@changesets/errors" "^0.2.0"
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"
    fs-extra "^7.0.1"

"@changesets/read@^0.6.5":
  version "0.6.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/read/-/read-0.6.5.tgz#7a68457e6356d3df187aa18e388f1b8dba3d2156"
  integrity sha1-emhFfmNW098YeqGOOI8bjbo9IVY=
  dependencies:
    "@changesets/git" "^3.0.4"
    "@changesets/logger" "^0.1.1"
    "@changesets/parse" "^0.4.1"
    "@changesets/types" "^6.1.0"
    fs-extra "^7.0.1"
    p-filter "^2.1.0"
    picocolors "^1.1.0"

"@changesets/should-skip-package@^0.1.2":
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/should-skip-package/-/should-skip-package-0.1.2.tgz#c018e1e05eab3d97afa4c4590f2b0db7486ae488"
  integrity sha1-wBjh4F6rPZevpMRZDysNt0hq5Ig=
  dependencies:
    "@changesets/types" "^6.1.0"
    "@manypkg/get-packages" "^1.1.3"

"@changesets/types@^4.0.1":
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/types/-/types-4.1.0.tgz#fb8f7ca2324fd54954824e864f9a61a82cb78fe0"
  integrity sha1-+498ojJP1UlUgk6GT5phqCy3j+A=

"@changesets/types@^6.1.0":
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/types/-/types-6.1.0.tgz#12a4c8490827d26bc6fbf97a151499be2fb6d2f5"
  integrity sha1-EqTISQgn0mvG+/l6FRSZvi+20vU=

"@changesets/write@^0.4.0":
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@changesets/write/-/write-0.4.0.tgz#ec903cbd8aa9b6da6fa09ef19fb609eedd115ed6"
  integrity sha1-7JA8vYqpttpvoJ7xn7YJ7t0RXtY=
  dependencies:
    "@changesets/types" "^6.1.0"
    fs-extra "^7.0.1"
    human-id "^4.1.1"
    prettier "^2.7.1"

"@chromatic-com/storybook@^3":
  version "3.2.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@chromatic-com/storybook/-/storybook-3.2.6.tgz#24fd1a2536311c7538145ccc4d971e90c202e43b"
  integrity sha1-JP0aJTYxHHU4FFzMTZcekMIC5Ds=
  dependencies:
    chromatic "^11.15.0"
    filesize "^10.0.12"
    jsonfile "^6.1.0"
    react-confetti "^6.1.0"
    strip-ansi "^7.1.0"

"@cspotcode/source-map-support@^0.8.0":
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz#00629c35a688e05a88b1cda684fb9d5e73f000a1"
  integrity sha1-AGKcNaaI4FqIsc2mhPudXnPwAKE=
  dependencies:
    "@jridgewell/trace-mapping" "0.3.9"

"@csstools/cascade-layer-name-parser@^1.0.13":
  version "1.0.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/cascade-layer-name-parser/-/cascade-layer-name-parser-1.0.13.tgz#6900157489bc53da1f6a66eaccd432025f6cd6fb"
  integrity sha1-aQAVdIm8U9ofambqzNQyAl9s1vs=

"@csstools/color-helpers@^4.2.1":
  version "4.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/color-helpers/-/color-helpers-4.2.1.tgz#da573554220ccb59757f12de62bf70c6b15645d4"
  integrity sha1-2lc1VCIMy1l1fxLeYr9wxrFWRdQ=

"@csstools/css-calc@^1.2.4":
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/css-calc/-/css-calc-1.2.4.tgz#9d9fb0dca33666cf97659f8f2c343ed0210e0e73"
  integrity sha1-nZ+w3KM2Zs+XZZ+PLDQ+0CEODnM=

"@csstools/css-color-parser@^2.0.4":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/css-color-parser/-/css-color-parser-2.0.5.tgz#ce1fe52f23f35f37bea2cf61ac865115aa17880a"
  integrity sha1-zh/lLyPzXze+os9hrIZRFaoXiAo=
  dependencies:
    "@csstools/color-helpers" "^4.2.1"
    "@csstools/css-calc" "^1.2.4"

"@csstools/css-parser-algorithms@^2.7.1":
  version "2.7.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/css-parser-algorithms/-/css-parser-algorithms-2.7.1.tgz#6d93a8f7d8aeb7cd9ed0868f946e46f021b6aa70"
  integrity sha1-bZOo99iut82e0IaPlG5G8CG2qnA=

"@csstools/css-tokenizer@^2.4.1":
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/css-tokenizer/-/css-tokenizer-2.4.1.tgz#1d8b2e200197cf5f35ceb07ca2dade31f3a00ae8"
  integrity sha1-HYsuIAGXz181zrB8otreMfOgCug=

"@csstools/media-query-list-parser@^2.1.13":
  version "2.1.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/media-query-list-parser/-/media-query-list-parser-2.1.13.tgz#f00be93f6bede07c14ddf51a168ad2748e4fe9e5"
  integrity sha1-8AvpP2vt4HwU3fUaForSdI5P6eU=

"@csstools/postcss-cascade-layers@^4.0.6":
  version "4.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-cascade-layers/-/postcss-cascade-layers-4.0.6.tgz#5a421cd2d5792d1eb8c28e682dc5f2c3b85cb045"
  integrity sha1-WkIc0tV5LR64wo5oLcXyw7hcsEU=
  dependencies:
    "@csstools/selector-specificity" "^3.1.1"
    postcss-selector-parser "^6.0.13"

"@csstools/postcss-color-function@^3.0.19":
  version "3.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-color-function/-/postcss-color-function-3.0.19.tgz#8db83be25bb590a29549b0305bdaa74e76366c62"
  integrity sha1-jbg74lu1kKKVSbAwW9qnTnY2bGI=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-color-mix-function@^2.0.19":
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-color-mix-function/-/postcss-color-mix-function-2.0.19.tgz#dd5c8cccd95613d11d8a8f96a57c148daa0e6306"
  integrity sha1-3VyMzNlWE9Edio+WpXwUjaoOYwY=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-content-alt-text@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-content-alt-text/-/postcss-content-alt-text-1.0.0.tgz#f69f74cd7ff679a912a444a274f67b9e0ce67127"
  integrity sha1-9p90zX/2eakSpESidPZ7ngzmcSc=
  dependencies:
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-exponential-functions@^1.0.9":
  version "1.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-exponential-functions/-/postcss-exponential-functions-1.0.9.tgz#443b42c26c65b57a84a21d81075dacd93eeb7fd8"
  integrity sha1-RDtCwmxltXqEoh2BB12s2T7rf9g=
  dependencies:
    "@csstools/css-calc" "^1.2.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"

"@csstools/postcss-font-format-keywords@^3.0.2":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-font-format-keywords/-/postcss-font-format-keywords-3.0.2.tgz#b504cfc60588ac39fa5d1c67ef3da802b1bd7701"
  integrity sha1-tQTPxgWIrDn6XRxn7z2oArG9dwE=
  dependencies:
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-gamut-mapping@^1.0.11":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-gamut-mapping/-/postcss-gamut-mapping-1.0.11.tgz#7f5b0457fc16df8e0f9dd2fbe86b7e5a0240592c"
  integrity sha1-f1sEV/wW344PndL76Gt+WgJAWSw=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"

"@csstools/postcss-gradients-interpolation-method@^4.0.20":
  version "4.0.20"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-gradients-interpolation-method/-/postcss-gradients-interpolation-method-4.0.20.tgz#e2a165719798cd8b503865297d8095c857eba77f"
  integrity sha1-4qFlcZeYzYtQOGUpfYCVyFfrp38=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-hwb-function@^3.0.18":
  version "3.0.18"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-hwb-function/-/postcss-hwb-function-3.0.18.tgz#267dc59c97033b1108e377c98c45c35b713ea66b"
  integrity sha1-Jn3FnJcDOxEI43fJjEXDW3E+pms=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-ic-unit@^3.0.7":
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-ic-unit/-/postcss-ic-unit-3.0.7.tgz#2a4428c0d19bd456b4bfd60dcbe9e7c4974dfcef"
  integrity sha1-KkQowNGb1Fa0v9YNy+nnxJdN/O8=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-initial@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-initial/-/postcss-initial-1.0.1.tgz#5aa378de9bfd0e6e377433f8986bdecf579e1268"
  integrity sha1-WqN43pv9Dm43dDP4mGvez1eeEmg=

"@csstools/postcss-is-pseudo-class@^4.0.8":
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-is-pseudo-class/-/postcss-is-pseudo-class-4.0.8.tgz#d2bcc6c2d86d9653c333926a9ea488c2fc221a7f"
  integrity sha1-0rzGwthtllPDM5JqnqSIwvwiGn8=
  dependencies:
    "@csstools/selector-specificity" "^3.1.1"
    postcss-selector-parser "^6.0.13"

"@csstools/postcss-light-dark-function@^1.0.8":
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-light-dark-function/-/postcss-light-dark-function-1.0.8.tgz#4d4cdad50a9b54b6b3a79cf32bf1cd956e82b0d7"
  integrity sha1-TUza1QqbVLazp5zzK/HNlW6CsNc=
  dependencies:
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-logical-float-and-clear@^2.0.1":
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-logical-float-and-clear/-/postcss-logical-float-and-clear-2.0.1.tgz#c70ed8293cc376b1572bf56794219f54dc58c54d"
  integrity sha1-xw7YKTzDdrFXK/VnlCGfVNxYxU0=

"@csstools/postcss-logical-overflow@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-logical-overflow/-/postcss-logical-overflow-1.0.1.tgz#d14631369f43ef989c7e32f051ddb6952a8ce35c"
  integrity sha1-0UYxNp9D75icfjLwUd22lSqM41w=

"@csstools/postcss-logical-overscroll-behavior@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-logical-overscroll-behavior/-/postcss-logical-overscroll-behavior-1.0.1.tgz#9305a6f0d08bb7b5f1a228272951f72d3bf9d44f"
  integrity sha1-kwWm8NCLt7XxoignKVH3LTv51E8=

"@csstools/postcss-logical-resize@^2.0.1":
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-logical-resize/-/postcss-logical-resize-2.0.1.tgz#a46c1b51055db96fb63af3bfe58909c773aea377"
  integrity sha1-pGwbUQVduW+2OvO/5YkJx3Ouo3c=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-logical-viewport-units@^2.0.11":
  version "2.0.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-logical-viewport-units/-/postcss-logical-viewport-units-2.0.11.tgz#f87fcaecd33403e19cb4d77a19e62ede8ed4ec13"
  integrity sha1-+H/K7NM0A+GctNd6GeYu3o7U7BM=
  dependencies:
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-media-minmax@^1.1.8":
  version "1.1.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-media-minmax/-/postcss-media-minmax-1.1.8.tgz#a90b576805312b1bea7bda7d1726402b7f5ef430"
  integrity sha1-qQtXaAUxKxvqe9p9FyZAK39e9DA=
  dependencies:
    "@csstools/css-calc" "^1.2.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/media-query-list-parser" "^2.1.13"

"@csstools/postcss-media-queries-aspect-ratio-number-values@^2.0.11":
  version "2.0.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-media-queries-aspect-ratio-number-values/-/postcss-media-queries-aspect-ratio-number-values-2.0.11.tgz#bb93203839521e99101b6adbab72dc9d9b57c9bc"
  integrity sha1-u5MgODlSHpkQG2rbq3LcnZtXybw=
  dependencies:
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/media-query-list-parser" "^2.1.13"

"@csstools/postcss-nested-calc@^3.0.2":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-nested-calc/-/postcss-nested-calc-3.0.2.tgz#72ae4d087987ab5596397f5c2e5db4403b81c4a9"
  integrity sha1-cq5NCHmHq1WWOX9cLl20QDuBxKk=
  dependencies:
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-normalize-display-values@^3.0.2":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-normalize-display-values/-/postcss-normalize-display-values-3.0.2.tgz#9013e6ade2fbd4cd725438c9ff0b1000062cf20d"
  integrity sha1-kBPmreL71M1yVDjJ/wsQAAYs8g0=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-oklab-function@^3.0.19":
  version "3.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-oklab-function/-/postcss-oklab-function-3.0.19.tgz#3bd0719914780fb53558af11958d0f4e6d2f952e"
  integrity sha1-O9BxmRR4D7U1WK8RlY0PTm0vlS4=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-progressive-custom-properties@^3.3.0":
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-progressive-custom-properties/-/postcss-progressive-custom-properties-3.3.0.tgz#20177d3fc61d8f170c4ee1686f3d2ab6eec27bbb"
  integrity sha1-IBd9P8YdjxcMTuFobz0qtu7Ce7s=
  dependencies:
    postcss-value-parser "^4.2.0"

"@csstools/postcss-relative-color-syntax@^2.0.19":
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-relative-color-syntax/-/postcss-relative-color-syntax-2.0.19.tgz#246b3a782e88df58184943c2471209c3d2085d65"
  integrity sha1-JGs6eC6I31gYSUPCRxIJw9IIXWU=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

"@csstools/postcss-scope-pseudo-class@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-scope-pseudo-class/-/postcss-scope-pseudo-class-3.0.1.tgz#c5454ea2fb3cf9beaf212d3a631a5c18cd4fbc14"
  integrity sha1-xUVOovs8+b6vIS06YxpcGM1PvBQ=
  dependencies:
    postcss-selector-parser "^6.0.13"

"@csstools/postcss-stepped-value-functions@^3.0.10":
  version "3.0.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-stepped-value-functions/-/postcss-stepped-value-functions-3.0.10.tgz#41cf7b2fc6abc9216b453137a35aeeeb056d70d9"
  integrity sha1-Qc97L8arySFrRTE3o1ru6wVtcNk=
  dependencies:
    "@csstools/css-calc" "^1.2.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"

"@csstools/postcss-text-decoration-shorthand@^3.0.7":
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-text-decoration-shorthand/-/postcss-text-decoration-shorthand-3.0.7.tgz#58dc60bb0718f6ec7d0a41d4124cf45a6813aeaa"
  integrity sha1-WNxguwcY9ux9CkHUEkz0WmgTrqo=
  dependencies:
    "@csstools/color-helpers" "^4.2.1"
    postcss-value-parser "^4.2.0"

"@csstools/postcss-trigonometric-functions@^3.0.10":
  version "3.0.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-trigonometric-functions/-/postcss-trigonometric-functions-3.0.10.tgz#0ad99b0a2a77cdd9c957b6e6e83221acf9b6afd8"
  integrity sha1-CtmbCip3zdnJV7bm6DIhrPm2r9g=
  dependencies:
    "@csstools/css-calc" "^1.2.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"

"@csstools/postcss-unset-value@^3.0.1":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/postcss-unset-value/-/postcss-unset-value-3.0.1.tgz#598a25630fd9ab0edf066d235916f7441404942a"
  integrity sha1-WYolYw/Zqw7fBm0jWRb3RBQElCo=

"@csstools/selector-resolve-nested@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/selector-resolve-nested/-/selector-resolve-nested-1.1.0.tgz#d872f2da402d3ce8bd0cf16ea5f9fba76b18e430"
  integrity sha1-2HLy2kAtPOi9DPFupfn7p2sY5DA=

"@csstools/selector-specificity@^3.1.1":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/selector-specificity/-/selector-specificity-3.1.1.tgz#63085d2995ca0f0e55aa8b8a07d69bfd48b844fe"
  integrity sha1-YwhdKZXKDw5VqouKB9ab/Ui4RP4=

"@csstools/utilities@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@csstools/utilities/-/utilities-1.0.0.tgz#42f3c213f2fb929324d465684ab9f46a0febd4bb"
  integrity sha1-QvPCE/L7kpMk1GVoSrn0ag/r1Ls=

"@date-fns/tz@1.2.0":
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@date-fns/tz/-/tz-1.2.0.tgz#81cb3211693830babaf3b96aff51607e143030a6"
  integrity sha1-gcsyEWk4MLq687lq/1FgfhQwMKY=

"@epam/assets@6.1.2":
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@epam/assets/-/assets-6.1.2.tgz#efc9fba47599893343f7f3821a984c3bf062ee7b"
  integrity sha1-78n7pHWZiTND9/OCGphMO/Bi7ns=

"@epam/uui-components@6.1.2":
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@epam/uui-components/-/uui-components-6.1.2.tgz#24f76c15bf850434698fdd2046e9a1b02c045e5c"
  integrity sha1-JPdsFb+FBDRpj90gRumhsCwEXlw=
  dependencies:
    "@epam/uui-core" "6.1.2"
    "@floating-ui/react" "0.27.5"
    "@types/classnames" "2.2.6"
    "@types/react-transition-group" "4.4.12"
    classnames "2.2.6"
    dayjs "1.11.12"
    react-custom-scrollbars-2 "^4.5.0"
    react-fast-compare "^3.2.2"
    react-focus-lock "2.13.5"
    react-transition-group "4.4.5"

"@epam/uui-core@6.1.2":
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@epam/uui-core/-/uui-core-6.1.2.tgz#2d3846f8746bff52517f44b2ec536e8e68cf5bea"
  integrity sha1-LThG+HRr/1JRf0Sy7FNujmjPW+o=
  dependencies:
    "@floating-ui/react" "0.27.5"
    "@types/classnames" "2.2.6"
    "@types/lodash.debounce" "4.0.6"
    classnames "2.2.6"
    csstype "2.6.10"
    dayjs "1.11.12"
    lodash.debounce "4.0.8"
    react-fast-compare "3.2.2"

"@epam/uui@6.1.2":
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@epam/uui/-/uui-6.1.2.tgz#0704d054d1bc7768fb42bd20b0d696e2b05d90ca"
  integrity sha1-BwTQVNG8d2j7Qr0gsNaW4rBdkMo=
  dependencies:
    "@epam/assets" "6.1.2"
    "@epam/uui-components" "6.1.2"
    "@epam/uui-core" "6.1.2"
    "@floating-ui/react" "0.27.5"
    classnames "2.2.6"
    dayjs "1.11.12"
    react-fast-compare "3.2.2"
    react-focus-lock "2.13.6"

"@esbuild/aix-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.21.5.tgz#c7184a326533fcdf1b8ee0733e21c713b975575f"
  integrity sha1-xxhKMmUz/N8bjuBzPiHHE7l1V18=

"@esbuild/aix-ppc64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/aix-ppc64/-/aix-ppc64-0.25.5.tgz#4e0f91776c2b340e75558f60552195f6fad09f18"
  integrity sha1-Tg+Rd2wrNA51VY9gVSGV9vrQnxg=

"@esbuild/android-arm64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-arm64/-/android-arm64-0.21.5.tgz#09d9b4357780da9ea3a7dfb833a1f1ff439b4052"
  integrity sha1-Cdm0NXeA2p6jp9+4M6Hx/0ObQFI=

"@esbuild/android-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-arm64/-/android-arm64-0.25.5.tgz#bc766407f1718923f6b8079c8c61bf86ac3a6a4f"
  integrity sha1-vHZkB/FxiSP2uAecjGG/hqw6ak8=

"@esbuild/android-arm@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-arm/-/android-arm-0.21.5.tgz#9b04384fb771926dfa6d7ad04324ecb2ab9b2e28"
  integrity sha1-mwQ4T7dxkm36bXrQQyTssqubLig=

"@esbuild/android-arm@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-arm/-/android-arm-0.25.5.tgz#4290d6d3407bae3883ad2cded1081a234473ce26"
  integrity sha1-QpDW00B7rjiDrSze0QgaI0RzziY=

"@esbuild/android-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-x64/-/android-x64-0.21.5.tgz#29918ec2db754cedcb6c1b04de8cd6547af6461e"
  integrity sha1-KZGOwtt1TO3LbBsE3ozWVHr2Rh4=

"@esbuild/android-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/android-x64/-/android-x64-0.25.5.tgz#40c11d9cbca4f2406548c8a9895d321bc3b35eff"
  integrity sha1-QMEdnLyk8kBlSMipiV0yG8OzXv8=

"@esbuild/darwin-arm64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.21.5.tgz#e495b539660e51690f3928af50a76fb0a6ccff2a"
  integrity sha1-5JW1OWYOUWkPOSivUKdvsKbM/yo=

"@esbuild/darwin-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/darwin-arm64/-/darwin-arm64-0.25.5.tgz#49d8bf8b1df95f759ac81eb1d0736018006d7e34"
  integrity sha1-Sdi/ix35X3WayB6x0HNgGABtfjQ=

"@esbuild/darwin-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.21.5.tgz#c13838fa57372839abdddc91d71542ceea2e1e22"
  integrity sha1-wTg4+lc3KDmr3dyR1xVCzuouHiI=

"@esbuild/darwin-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/darwin-x64/-/darwin-x64-0.25.5.tgz#e27a5d92a14886ef1d492fd50fc61a2d4d87e418"
  integrity sha1-4npdkqFIhu8dSS/VD8YaLU2H5Bg=

"@esbuild/freebsd-arm64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.21.5.tgz#646b989aa20bf89fd071dd5dbfad69a3542e550e"
  integrity sha1-ZGuYmqIL+J/Qcd1dv61po1QuVQ4=

"@esbuild/freebsd-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/freebsd-arm64/-/freebsd-arm64-0.25.5.tgz#97cede59d638840ca104e605cdb9f1b118ba0b1c"
  integrity sha1-l87eWdY4hAyhBOYFzbnxsRi6Cxw=

"@esbuild/freebsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.21.5.tgz#aa615cfc80af954d3458906e38ca22c18cf5c261"
  integrity sha1-qmFc/ICvlU00WJBuOMoiwYz1wmE=

"@esbuild/freebsd-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/freebsd-x64/-/freebsd-x64-0.25.5.tgz#71c77812042a1a8190c3d581e140d15b876b9c6f"
  integrity sha1-ccd4EgQqGoGQw9WB4UDRW4drnG8=

"@esbuild/linux-arm64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.21.5.tgz#70ac6fa14f5cb7e1f7f887bcffb680ad09922b5b"
  integrity sha1-cKxvoU9ct+H3+Ie8/7aArQmSK1s=

"@esbuild/linux-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-arm64/-/linux-arm64-0.25.5.tgz#f7b7c8f97eff8ffd2e47f6c67eb5c9765f2181b8"
  integrity sha1-97fI+X7/j/0uR/bGfrXJdl8hgbg=

"@esbuild/linux-arm@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-arm/-/linux-arm-0.21.5.tgz#fc6fd11a8aca56c1f6f3894f2bea0479f8f626b9"
  integrity sha1-/G/RGorKVsH284lPK+oEefj2Jrk=

"@esbuild/linux-arm@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-arm/-/linux-arm-0.25.5.tgz#2a0be71b6cd8201fa559aea45598dffabc05d911"
  integrity sha1-KgvnG2zYIB+lWa6kVZjf+rwF2RE=

"@esbuild/linux-ia32@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.21.5.tgz#3271f53b3f93e3d093d518d1649d6d68d346ede2"
  integrity sha1-MnH1Oz+T49CT1RjRZJ1taNNG7eI=

"@esbuild/linux-ia32@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-ia32/-/linux-ia32-0.25.5.tgz#763414463cd9ea6fa1f96555d2762f9f84c61783"
  integrity sha1-djQURjzZ6m+h+WVV0nYvn4TGF4M=

"@esbuild/linux-loong64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.21.5.tgz#ed62e04238c57026aea831c5a130b73c0f9f26df"
  integrity sha1-7WLgQjjFcCauqDHFoTC3PA+fJt8=

"@esbuild/linux-loong64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-loong64/-/linux-loong64-0.25.5.tgz#428cf2213ff786a502a52c96cf29d1fcf1eb8506"
  integrity sha1-QozyIT/3hqUCpSyWzynR/PHrhQY=

"@esbuild/linux-mips64el@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.21.5.tgz#e79b8eb48bf3b106fadec1ac8240fb97b4e64cbe"
  integrity sha1-55uOtIvzsQb63sGsgkD7l7TmTL4=

"@esbuild/linux-mips64el@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-mips64el/-/linux-mips64el-0.25.5.tgz#5cbcc7fd841b4cd53358afd33527cd394e325d96"
  integrity sha1-XLzH/YQbTNUzWK/TNSfNOU4yXZY=

"@esbuild/linux-ppc64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.21.5.tgz#5f2203860a143b9919d383ef7573521fb154c3e4"
  integrity sha1-XyIDhgoUO5kZ04PvdXNSH7FUw+Q=

"@esbuild/linux-ppc64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-ppc64/-/linux-ppc64-0.25.5.tgz#0d954ab39ce4f5e50f00c4f8c4fd38f976c13ad9"
  integrity sha1-DZVKs5zk9eUPAMT4xP04+XbBOtk=

"@esbuild/linux-riscv64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.21.5.tgz#07bcafd99322d5af62f618cb9e6a9b7f4bb825dc"
  integrity sha1-B7yv2ZMi1a9i9hjLnmqbf0u4Jdw=

"@esbuild/linux-riscv64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-riscv64/-/linux-riscv64-0.25.5.tgz#0e7dd30730505abd8088321e8497e94b547bfb1e"
  integrity sha1-Dn3TBzBQWr2AiDIehJfpS1R7+x4=

"@esbuild/linux-s390x@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.21.5.tgz#b7ccf686751d6a3e44b8627ababc8be3ef62d8de"
  integrity sha1-t8z2hnUdaj5EuGJ6uryL4+9i2N4=

"@esbuild/linux-s390x@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-s390x/-/linux-s390x-0.25.5.tgz#5669af81327a398a336d7e40e320b5bbd6e6e72d"
  integrity sha1-VmmvgTJ6OYozbX5A4yC1u9bm5y0=

"@esbuild/linux-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-x64/-/linux-x64-0.21.5.tgz#6d8f0c768e070e64309af8004bb94e68ab2bb3b0"
  integrity sha1-bY8Mdo4HDmQwmvgAS7lOaKsrs7A=

"@esbuild/linux-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/linux-x64/-/linux-x64-0.25.5.tgz#b2357dd153aa49038967ddc1ffd90c68a9d2a0d4"
  integrity sha1-sjV90VOqSQOJZ93B/9kMaKnSoNQ=

"@esbuild/netbsd-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/netbsd-arm64/-/netbsd-arm64-0.25.5.tgz#53b4dfb8fe1cee93777c9e366893bd3daa6ba63d"
  integrity sha1-U7TfuP4c7pN3fJ42aJO9Paprpj0=

"@esbuild/netbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.21.5.tgz#bbe430f60d378ecb88decb219c602667387a6047"
  integrity sha1-u+Qw9g03jsuI3sshnGAmZzh6YEc=

"@esbuild/netbsd-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/netbsd-x64/-/netbsd-x64-0.25.5.tgz#a0206f6314ce7dc8713b7732703d0f58de1d1e79"
  integrity sha1-oCBvYxTOfchxO3cycD0PWN4dHnk=

"@esbuild/openbsd-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/openbsd-arm64/-/openbsd-arm64-0.25.5.tgz#2a796c87c44e8de78001d808c77d948a21ec22fd"
  integrity sha1-Knlsh8ROjeeAAdgIx32UiiHsIv0=

"@esbuild/openbsd-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.21.5.tgz#99d1cf2937279560d2104821f5ccce220cb2af70"
  integrity sha1-mdHPKTcnlWDSEEgh9czOIgyyr3A=

"@esbuild/openbsd-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/openbsd-x64/-/openbsd-x64-0.25.5.tgz#28d0cd8909b7fa3953af998f2b2ed34f576728f0"
  integrity sha1-KNDNiQm3+jlTr5mPKy7TT1dnKPA=

"@esbuild/sunos-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.21.5.tgz#08741512c10d529566baba837b4fe052c8f3487b"
  integrity sha1-CHQVEsENUpVmurqDe0/gUsjzSHs=

"@esbuild/sunos-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/sunos-x64/-/sunos-x64-0.25.5.tgz#a28164f5b997e8247d407e36c90d3fd5ddbe0dc5"
  integrity sha1-ooFk9bmX6CR9QH42yQ0/1d2+DcU=

"@esbuild/win32-arm64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.21.5.tgz#675b7385398411240735016144ab2e99a60fc75d"
  integrity sha1-Z1tzhTmEESQHNQFhRKsumaYPx10=

"@esbuild/win32-arm64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-arm64/-/win32-arm64-0.25.5.tgz#6eadbead38e8bd12f633a5190e45eff80e24007e"
  integrity sha1-bq2+rTjovRL2M6UZDkXv+A4kAH4=

"@esbuild/win32-ia32@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.21.5.tgz#1bfc3ce98aa6ca9a0969e4d2af72144c59c1193b"
  integrity sha1-G/w86YqmypoJaeTSr3IUTFnBGTs=

"@esbuild/win32-ia32@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-ia32/-/win32-ia32-0.25.5.tgz#bab6288005482f9ed2adb9ded7e88eba9a62cc0d"
  integrity sha1-urYogAVIL57Srbne1+iOuppizA0=

"@esbuild/win32-x64@0.21.5":
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-x64/-/win32-x64-0.21.5.tgz#acad351d582d157bb145535db2a6ff53dd514b5c"
  integrity sha1-rK01HVgtFXuxRVNdsqb/U91RS1w=

"@esbuild/win32-x64@0.25.5":
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@esbuild/win32-x64/-/win32-x64-0.25.5.tgz#7fc114af5f6563f19f73324b5d5ff36ece0803d1"
  integrity sha1-f8EUr19lY/GfczJLXV/zbs4IA9E=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@eslint-community/regexpp/-/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@eslint/eslintrc/-/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.0":
  version "8.57.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@eslint/js/-/js-8.57.0.tgz#a5417ae8427873f1dd08b70b3574b453e67b5f7f"
  integrity sha1-pUF66EJ4c/HdCLcLNXS0U+Z7X38=

"@ferocia-oss/osnap@^1.3.5":
  version "1.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@ferocia-oss/osnap/-/osnap-1.3.5.tgz#a6e8a5ff79c5de5402874c63aba10e14af8d6fee"
  integrity sha1-puil/3nF3lQCh0xjq6EOFK+Nb+4=
  dependencies:
    execa "^7.1.0"
    minimist "^1.2.7"
    tempfile "^3.0.0"
    which "^3.0.0"

"@floating-ui/core@^1.7.1":
  version "1.7.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/core/-/core-1.7.1.tgz#1abc6b157d4a936174f9dbd078278c3a81c8bc6b"
  integrity sha1-GrxrFX1Kk2F0+dvQeCeMOoHIvGs=
  dependencies:
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/dom@^1.0.0":
  version "1.7.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/dom/-/dom-1.7.1.tgz#76a4e3cbf7a08edf40c34711cf64e0cc8053d912"
  integrity sha1-dqTjy/egjt9Aw0cRz2TgzIBT2RI=
  dependencies:
    "@floating-ui/core" "^1.7.1"
    "@floating-ui/utils" "^0.2.9"

"@floating-ui/react-dom@^2.1.2":
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/react-dom/-/react-dom-2.1.3.tgz#1dea32e59514a67d182f0c89c8975ff959774b61"
  integrity sha1-Heoy5ZUUpn0YLwyJyJdf+Vl3S2E=
  dependencies:
    "@floating-ui/dom" "^1.0.0"

"@floating-ui/react@0.27.5":
  version "0.27.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/react/-/react-0.27.5.tgz#27a6e63a8ef35eb8712ef304a154ea706da26814"
  integrity sha1-J6bmOo7zXrhxLvMEoVTqcG2iaBQ=
  dependencies:
    "@floating-ui/react-dom" "^2.1.2"
    "@floating-ui/utils" "^0.2.9"
    tabbable "^6.0.0"

"@floating-ui/react@0.27.7":
  version "0.27.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/react/-/react-0.27.7.tgz#dd5512f84528b849a99d93266b963b84c83bd201"
  integrity sha1-3VUS+EUouEmpnZMma5Y7hMg70gE=
  dependencies:
    "@floating-ui/react-dom" "^2.1.2"
    "@floating-ui/utils" "^0.2.9"
    tabbable "^6.0.0"

"@floating-ui/utils@^0.2.9":
  version "0.2.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@floating-ui/utils/-/utils-0.2.9.tgz#50dea3616bc8191fb8e112283b49eaff03e78429"
  integrity sha1-UN6jYWvIGR+44RIoO0nq/wPnhCk=

"@hookform/resolvers@3.9.0":
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@hookform/resolvers/-/resolvers-3.9.0.tgz#cf540ac21c6c0cd24a40cf53d8e6d64391fb753d"
  integrity sha1-z1QKwhxsDNJKQM9T2ObWQ5H7dT0=

"@humanwhocodes/config-array@^0.11.14":
  version "0.11.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@humanwhocodes/config-array/-/config-array-0.11.14.tgz#d78e481a039f7566ecc9660b4ea7fe6b1fec442b"
  integrity sha1-145IGgOfdWbsyWYLTqf+ax/sRCs=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.2"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.2":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@humanwhocodes/object-schema/-/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@isaacs/cliui@^8.0.2":
  version "8.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@isaacs/cliui/-/cliui-8.0.2.tgz#b37667b7bc181c168782259bab42474fbf52b550"
  integrity sha1-s3Znt7wYHBaHgiWbq0JHT79StVA=
  dependencies:
    string-width "^5.1.2"
    string-width-cjs "npm:string-width@^4.2.0"
    strip-ansi "^7.0.1"
    strip-ansi-cjs "npm:strip-ansi@^6.0.1"
    wrap-ansi "^8.1.0"
    wrap-ansi-cjs "npm:wrap-ansi@^7.0.0"

"@istanbuljs/load-nyc-config@^1.0.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz#fd3db1d59ecf7cf121e80650bb86712f9b55eced"
  integrity sha1-/T2x1Z7PfPEh6AZQu4ZxL5tV7O0=
  dependencies:
    camelcase "^5.3.1"
    find-up "^4.1.0"
    get-package-type "^0.1.0"
    js-yaml "^3.13.1"
    resolve-from "^5.0.0"

"@istanbuljs/schema@^0.1.2", "@istanbuljs/schema@^0.1.3":
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@istanbuljs/schema/-/schema-0.1.3.tgz#e45e384e4b8ec16bce2fd903af78450f6bf7ec98"
  integrity sha1-5F44TkuOwWvOL9kDr3hFD2v37Jg=

"@jest/console@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/console/-/console-29.7.0.tgz#cd4822dbdb84529265c5a2bdb529a3c9cc950ffc"
  integrity sha1-zUgi29uEUpJlxaK9tSmjycyVD/w=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"

"@jest/core@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/core/-/core-29.7.0.tgz#b6cccc239f30ff36609658c5a5e2291757ce448f"
  integrity sha1-tszMI58w/zZglljFpeIpF1fORI8=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/reporters" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-changed-files "^29.7.0"
    jest-config "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-resolve-dependencies "^29.7.0"
    jest-runner "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    jest-watcher "^29.7.0"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-ansi "^6.0.0"

"@jest/environment@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/environment/-/environment-29.7.0.tgz#24d61f54ff1f786f3cd4073b4b94416383baf2a7"
  integrity sha1-JNYfVP8feG881Ac7S5RBY4O68qc=
  dependencies:
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"

"@jest/expect-utils@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/expect-utils/-/expect-utils-29.7.0.tgz#023efe5d26a8a70f21677d0a1afc0f0a44e3a1c6"
  integrity sha1-Aj7+XSaopw8hZ30KGvwPCkTjocY=
  dependencies:
    jest-get-type "^29.6.3"

"@jest/expect@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/expect/-/expect-29.7.0.tgz#76a3edb0cb753b70dfbfe23283510d3d45432bf2"
  integrity sha1-dqPtsMt1O3Dfv+Iyg1ENPUVDK/I=
  dependencies:
    expect "^29.7.0"
    jest-snapshot "^29.7.0"

"@jest/fake-timers@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/fake-timers/-/fake-timers-29.7.0.tgz#fd91bf1fffb16d7d0d24a426ab1a47a49881a565"
  integrity sha1-/ZG/H/+xbX0NJKQmqxpHpJiBpWU=
  dependencies:
    "@jest/types" "^29.6.3"
    "@sinonjs/fake-timers" "^10.0.2"
    "@types/node" "*"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

"@jest/globals@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/globals/-/globals-29.7.0.tgz#8d9290f9ec47ff772607fa864ca1d5a2efae1d4d"
  integrity sha1-jZKQ+exH/3cmB/qGTKHVou+uHU0=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/types" "^29.6.3"
    jest-mock "^29.7.0"

"@jest/reporters@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/reporters/-/reporters-29.7.0.tgz#04b262ecb3b8faa83b0b3d321623972393e8f4c7"
  integrity sha1-BLJi7LO4+qg7Cz0yFiOXI5Po9Mc=
  dependencies:
    "@bcoe/v8-coverage" "^0.2.3"
    "@jest/console" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    "@types/node" "*"
    chalk "^4.0.0"
    collect-v8-coverage "^1.0.0"
    exit "^0.1.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    istanbul-lib-coverage "^3.0.0"
    istanbul-lib-instrument "^6.0.0"
    istanbul-lib-report "^3.0.0"
    istanbul-lib-source-maps "^4.0.0"
    istanbul-reports "^3.1.3"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    slash "^3.0.0"
    string-length "^4.0.1"
    strip-ansi "^6.0.0"
    v8-to-istanbul "^9.0.1"

"@jest/schemas@^28.1.3":
  version "28.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/schemas/-/schemas-28.1.3.tgz#ad8b86a66f11f33619e3d7e1dcddd7f2d40ff905"
  integrity sha1-rYuGpm8R8zYZ49fh3N3X8tQP+QU=
  dependencies:
    "@sinclair/typebox" "^0.24.1"

"@jest/schemas@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/schemas/-/schemas-29.6.3.tgz#430b5ce8a4e0044a7e3819663305a7b3091c8e03"
  integrity sha1-Qwtc6KTgBEp+OBlmMwWnswkcjgM=
  dependencies:
    "@sinclair/typebox" "^0.27.8"

"@jest/source-map@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/source-map/-/source-map-29.6.3.tgz#d90ba772095cf37a34a5eb9413f1b562a08554c4"
  integrity sha1-2Quncglc83o0peuUE/G1YqCFVMQ=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.18"
    callsites "^3.0.0"
    graceful-fs "^4.2.9"

"@jest/test-result@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/test-result/-/test-result-29.7.0.tgz#8db9a80aa1a097bb2262572686734baed9b1657c"
  integrity sha1-jbmoCqGgl7siYlcmhnNLrtmxZXw=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    collect-v8-coverage "^1.0.0"

"@jest/test-sequencer@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz#6cef977ce1d39834a3aea887a1726628a6f072ce"
  integrity sha1-bO+XfOHTmDSjrqiHoXJmKKbwcs4=
  dependencies:
    "@jest/test-result" "^29.7.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    slash "^3.0.0"

"@jest/transform@^29.7.0":
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/transform/-/transform-29.7.0.tgz#df2dd9c346c7d7768b8a06639994640c642e284c"
  integrity sha1-3y3Zw0bH13aLigZjmZRkDGQuKEw=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/types" "^29.6.3"
    "@jridgewell/trace-mapping" "^0.3.18"
    babel-plugin-istanbul "^6.1.1"
    chalk "^4.0.0"
    convert-source-map "^2.0.0"
    fast-json-stable-stringify "^2.1.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    micromatch "^4.0.4"
    pirates "^4.0.4"
    slash "^3.0.0"
    write-file-atomic "^4.0.2"

"@jest/types@^27.5.1":
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/types/-/types-27.5.1.tgz#3c79ec4a8ba61c170bf937bcf9e98a9df175ec80"
  integrity sha1-PHnsSoumHBcL+Te8+emKnfF17IA=
  dependencies:
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^16.0.0"
    chalk "^4.0.0"

"@jest/types@^29.6.3":
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jest/types/-/types-29.6.3.tgz#1131f8cf634e7e84c5e77bab12f052af585fba59"
  integrity sha1-ETH4z2NOfoTF53urEvBSr1hfulk=
  dependencies:
    "@jest/schemas" "^29.6.3"
    "@types/istanbul-lib-coverage" "^2.0.0"
    "@types/istanbul-reports" "^3.0.0"
    "@types/node" "*"
    "@types/yargs" "^17.0.8"
    chalk "^4.0.0"

"@joshwooding/vite-plugin-react-docgen-typescript@0.5.0":
  version "0.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@joshwooding/vite-plugin-react-docgen-typescript/-/vite-plugin-react-docgen-typescript-0.5.0.tgz#d653164553d731fc95ad80f2f27662908e5989a0"
  integrity sha1-1lMWRVPXMfyVrYDy8nZikI5ZiaA=
  dependencies:
    glob "^10.0.0"
    magic-string "^0.27.0"
    react-docgen-typescript "^2.2.2"

"@jridgewell/gen-mapping@^0.3.2", "@jridgewell/gen-mapping@^0.3.5":
  version "0.3.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz#4f0e06362e01362f823d348f1872b08f666d8142"
  integrity sha1-Tw4GNi4BNi+CPTSPGHKwj2ZtgUI=
  dependencies:
    "@jridgewell/set-array" "^1.2.1"
    "@jridgewell/sourcemap-codec" "^1.4.10"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.0.3", "@jridgewell/resolve-uri@^3.1.0":
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz#7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6"
  integrity sha1-eg7mAfYPmaIMfHxf8MgDiMEYm9Y=

"@jridgewell/set-array@^1.2.1":
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/set-array/-/set-array-1.2.1.tgz#558fb6472ed16a4c850b889530e6b36438c49280"
  integrity sha1-VY+2Ry7RakyFC4iVMOazZDjEkoA=

"@jridgewell/sourcemap-codec@^1.4.10", "@jridgewell/sourcemap-codec@^1.4.13", "@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz#3188bcb273a414b0d215fd22a58540b989b9409a"
  integrity sha1-MYi8snOkFLDSFf0ipYVAuYm5QJo=

"@jridgewell/trace-mapping@0.3.9":
  version "0.3.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz#6534fd5933a53ba7cbf3a17615e273a0d1273ff9"
  integrity sha1-ZTT9WTOlO6fL86F2FeJzoNEnP/k=
  dependencies:
    "@jridgewell/resolve-uri" "^3.0.3"
    "@jridgewell/sourcemap-codec" "^1.4.10"

"@jridgewell/trace-mapping@^0.3.12", "@jridgewell/trace-mapping@^0.3.18", "@jridgewell/trace-mapping@^0.3.23", "@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.25":
  version "0.3.25"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz#15f190e98895f3fc23276ee14bc76b675c2e50f0"
  integrity sha1-FfGQ6YiV8/wjJ27hS8drZ1wuUPA=
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@loki/browser@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/browser/-/browser-0.35.0.tgz#81e233f76276ab70ae337c08ceeba25156c05280"
  integrity sha1-geIz92J2q3CuM3wIzuuiUVbAUoA=
  dependencies:
    "@loki/integration-core" "^0.35.0"

"@loki/core@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/core/-/core-0.35.0.tgz#bfbdae6d801563014bc7ee5d0e05e655a2b4e534"
  integrity sha1-v72ubYAVYwFLx+5dDgXmVaK05TQ=
  dependencies:
    mime-types "^2.1.35"
    shelljs "^0.8.3"

"@loki/diff-graphics-magick@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/diff-graphics-magick/-/diff-graphics-magick-0.35.0.tgz#328eb5eed978b56dc8d45df5ec5167e44809e36f"
  integrity sha1-Mo617tl4tW3I1F317FFn5EgJ428=
  dependencies:
    fs-extra "^9.1.0"
    gm "^1.23.1"

"@loki/diff-looks-same@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/diff-looks-same/-/diff-looks-same-0.35.0.tgz#b989411ae7ba07c70ebea3e8094be5aef3db7611"
  integrity sha1-uYlBGue6B8cOvqPoCUvlrvPbdhE=
  dependencies:
    fs-extra "^9.1.0"
    looks-same "^4.0.0"

"@loki/diff-pixelmatch@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/diff-pixelmatch/-/diff-pixelmatch-0.35.0.tgz#910f4cdc01fefd02784426d0cbf6bec4b9078aee"
  integrity sha1-kQ9M3AH+/QJ4RCbQy/a+xLkHiu4=
  dependencies:
    fs-extra "^9.1.0"
    pixelmatch "^5.2.0"
    pngjs "^4.0.1"

"@loki/integration-core@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/integration-core/-/integration-core-0.35.0.tgz#865b48b60cc03b53be5904389b2dd2577945508c"
  integrity sha1-hltItgzAO1O+WQQ4my3SV3lFUIw=

"@loki/integration-react-native@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/integration-react-native/-/integration-react-native-0.35.0.tgz#91f24a7fbdcfc43148d5913ce5b083d5057c551f"
  integrity sha1-kfJKf73PxDFI1ZE85bCD1QV8VR8=
  dependencies:
    "@loki/integration-core" "^0.35.0"
    hoist-non-react-statics "*"

"@loki/integration-react@^0.35.1":
  version "0.35.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/integration-react/-/integration-react-0.35.1.tgz#82f5148e632be798df2a492aeb285f9d8fe56f5f"
  integrity sha1-gvUUjmMr55jfKkkq6yhfnY/lb18=
  dependencies:
    "@loki/browser" "^0.35.0"

"@loki/integration-vue@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/integration-vue/-/integration-vue-0.35.0.tgz#a6697f65d6b892ef208cd3283d71bfc03ad97059"
  integrity sha1-pml/Zda4ku8gjNMoPXG/wDrZcFk=
  dependencies:
    "@loki/browser" "^0.35.0"

"@loki/runner@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/runner/-/runner-0.35.0.tgz#0d5d0df4b87342c34fdc1bc02258ecef937141c0"
  integrity sha1-DV0N9LhzQsNP3BvAIljs75NxQcA=
  dependencies:
    "@loki/core" "^0.35.0"
    "@loki/diff-graphics-magick" "^0.35.0"
    "@loki/diff-looks-same" "^0.35.0"
    "@loki/diff-pixelmatch" "^0.35.0"
    "@loki/target-chrome-app" "^0.35.0"
    "@loki/target-chrome-aws-lambda" "^0.35.0"
    "@loki/target-chrome-docker" "^0.35.0"
    "@loki/target-native-android-emulator" "^0.35.0"
    "@loki/target-native-ios-simulator" "^0.35.0"
    async "^3.2.0"
    chalk "^4.1.0"
    ci-info "^2.0.0"
    cosmiconfig "^7.0.0"
    fs-extra "^9.1.0"
    import-jsx "^4.0.1"
    ink "^3.2.0"
    minimist "^1.2.0"
    ramda "^0.27.1"
    react "^17.0.2"
    transliteration "^2.2.0"

"@loki/target-chrome-app@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-chrome-app/-/target-chrome-app-0.35.0.tgz#df90b414cb52cf633b6f5004f64a9a4e5bf443fe"
  integrity sha1-35C0FMtSz2M7b1AE9kqaTlv0Q/4=
  dependencies:
    "@loki/core" "^0.35.0"
    "@loki/target-chrome-core" "^0.35.0"
    chrome-launcher "0.15.2"
    chrome-remote-interface "^0.32.1"
    debug "^4.1.1"
    find-free-port-sync "^1.0.0"

"@loki/target-chrome-aws-lambda@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-chrome-aws-lambda/-/target-chrome-aws-lambda-0.35.0.tgz#28b2698e2c7369939963fdd4fe0adac627b188c8"
  integrity sha1-KLJpjixzaZOZY/3U/graxiexiMg=
  dependencies:
    "@loki/core" "^0.35.0"
    aws-sdk "^2.840.0"
    debug "^4.1.1"

"@loki/target-chrome-core@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-chrome-core/-/target-chrome-core-0.35.0.tgz#fd3816b548e568c3a88fae97f1892aace1fa7d48"
  integrity sha1-/TgWtUjlaMOoj66X8YkqrOH6fUg=
  dependencies:
    "@loki/browser" "^0.35.0"
    "@loki/core" "^0.35.0"
    "@loki/integration-core" "^0.35.0"
    debug "^4.1.1"

"@loki/target-chrome-docker@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-chrome-docker/-/target-chrome-docker-0.35.0.tgz#59e786233debe1fdfa40462b743c338e818d5eab"
  integrity sha1-WeeGIz3r4f36QEYrdDwzjoGNXqs=
  dependencies:
    "@loki/core" "^0.35.0"
    "@loki/target-chrome-core" "^0.35.0"
    chrome-remote-interface "^0.32.1"
    debug "^4.1.1"
    execa "^5.0.0"
    find-free-port-sync "^1.0.0"
    fs-extra "^9.1.0"
    wait-port "^1.1.0"

"@loki/target-native-android-emulator@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-native-android-emulator/-/target-native-android-emulator-0.35.0.tgz#9fea4b4d9d022f216dabd0575e287d5b220c602f"
  integrity sha1-n+pLTZ0CLyFtq9BXXih9WyIMYC8=
  dependencies:
    "@ferocia-oss/osnap" "^1.3.5"
    "@loki/core" "^0.35.0"
    "@loki/target-native-core" "^0.35.0"
    fs-extra "^9.1.0"
    tempy "^1.0.0"

"@loki/target-native-core@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-native-core/-/target-native-core-0.35.0.tgz#06c9cb46488aefe1cd4090943fe700ffdb7234dc"
  integrity sha1-BsnLRkiK7+HNQJCUP+cA/9tyNNw=
  dependencies:
    "@loki/core" "^0.35.0"
    debug "^4.1.1"
    ws "^7.2.0"

"@loki/target-native-ios-simulator@^0.35.0":
  version "0.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@loki/target-native-ios-simulator/-/target-native-ios-simulator-0.35.0.tgz#84e69fe0d72a3ffc29480787ffe555ba5f01fd61"
  integrity sha1-hOaf4NcqP/wpSAeH/+VVul8B/WE=
  dependencies:
    "@ferocia-oss/osnap" "^1.3.5"
    "@loki/core" "^0.35.0"
    "@loki/target-native-core" "^0.35.0"
    fs-extra "^9.1.0"
    tempy "^1.0.0"

"@manypkg/find-root@^1.1.0":
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@manypkg/find-root/-/find-root-1.1.0.tgz#a62d8ed1cd7e7d4c11d9d52a8397460b5d4ad29f"
  integrity sha1-pi2O0c1+fUwR2dUqg5dGC11K0p8=
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@types/node" "^12.7.1"
    find-up "^4.1.0"
    fs-extra "^8.1.0"

"@manypkg/get-packages@^1.1.3":
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@manypkg/get-packages/-/get-packages-1.1.3.tgz#e184db9bba792fa4693de4658cfb1463ac2c9c47"
  integrity sha1-4YTbm7p5L6RpPeRljPsUY6wsnEc=
  dependencies:
    "@babel/runtime" "^7.5.5"
    "@changesets/types" "^4.0.1"
    "@manypkg/find-root" "^1.1.0"
    fs-extra "^8.1.0"
    globby "^11.0.0"
    read-yaml-file "^1.1.0"

"@mdx-js/react@^3.0.0":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@mdx-js/react/-/react-3.1.0.tgz#c4522e335b3897b9a845db1dbdd2f966ae8fb0ed"
  integrity sha1-xFIuM1s4l7moRdsdvdL5Zq6PsO0=
  dependencies:
    "@types/mdx" "^2.0.0"

"@microsoft/api-extractor-model@7.30.6":
  version "7.30.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@microsoft/api-extractor-model/-/api-extractor-model-7.30.6.tgz#cd9c434521dda3b226cc0f6aefb9c20afaf99c92"
  integrity sha1-zZxDRSHdo7ImzA9q77nCCvr5nJI=
  dependencies:
    "@microsoft/tsdoc" "~0.15.1"
    "@microsoft/tsdoc-config" "~0.17.1"
    "@rushstack/node-core-library" "5.13.1"

"@microsoft/api-extractor@^7.50.1":
  version "7.52.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@microsoft/api-extractor/-/api-extractor-7.52.8.tgz#7cc944f44ca1b1ad9d7272ab5d98e81987c1f8ca"
  integrity sha1-fMlE9Eyhsa2dcnKrXZjoGYfB+Mo=
  dependencies:
    "@microsoft/api-extractor-model" "7.30.6"
    "@microsoft/tsdoc" "~0.15.1"
    "@microsoft/tsdoc-config" "~0.17.1"
    "@rushstack/node-core-library" "5.13.1"
    "@rushstack/rig-package" "0.5.3"
    "@rushstack/terminal" "0.15.3"
    "@rushstack/ts-command-line" "5.0.1"
    lodash "~4.17.15"
    minimatch "~3.0.3"
    resolve "~1.22.1"
    semver "~7.5.4"
    source-map "~0.6.1"
    typescript "5.8.2"

"@microsoft/tsdoc-config@~0.17.1":
  version "0.17.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@microsoft/tsdoc-config/-/tsdoc-config-0.17.1.tgz#e0f0b50628f4ad7fe121ca616beacfe6a25b9335"
  integrity sha1-4PC1Bij0rX/hIcpha+rP5qJbkzU=
  dependencies:
    "@microsoft/tsdoc" "0.15.1"
    ajv "~8.12.0"
    jju "~1.4.0"
    resolve "~1.22.2"

"@microsoft/tsdoc@0.15.1", "@microsoft/tsdoc@~0.15.1":
  version "0.15.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@microsoft/tsdoc/-/tsdoc-0.15.1.tgz#d4f6937353bc4568292654efb0a0e0532adbcba2"
  integrity sha1-1PaTc1O8RWgpJlTvsKDgUyrby6I=

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@pkgjs/parseargs@^0.11.0":
  version "0.11.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@pkgjs/parseargs/-/parseargs-0.11.0.tgz#a77ea742fab25775145434eb1d2328cf5013ac33"
  integrity sha1-p36nQvqyV3UUVDTrHSMoz1ATrDM=

"@pkgr/core@^0.1.0":
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@pkgr/core/-/core-0.1.2.tgz#1cf95080bb7072fafaa3cb13b442fab4695c3893"
  integrity sha1-HPlQgLtwcvr6o8sTtEL6tGlcOJM=

"@polka/url@^1.0.0-next.24":
  version "1.0.0-next.29"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@polka/url/-/url-1.0.0-next.29.tgz#5a40109a1ab5f84d6fd8fc928b19f367cbe7e7b1"
  integrity sha1-WkAQmhq1+E1v2PySixnzZ8vn57E=

"@reduxjs/toolkit@1.x.x || 2.x.x":
  version "2.8.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@reduxjs/toolkit/-/toolkit-2.8.2.tgz#f4e9f973c6fc930c1e0f3bf462cc95210c28f5f9"
  integrity sha1-9On5c8b8kwweDzv0YsyVIQwo9fk=
  dependencies:
    "@standard-schema/spec" "^1.0.0"
    "@standard-schema/utils" "^0.3.0"
    immer "^10.0.3"
    redux "^5.0.1"
    redux-thunk "^3.1.0"
    reselect "^5.1.0"

"@rolldown/pluginutils@1.0.0-beta.11":
  version "1.0.0-beta.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.11.tgz#1e3e8044dd053c3dfa4bbbb3861f6e180ee78343"
  integrity sha1-Hj6ARN0FPD36S7uzhh9uGA7ng0M=

"@rolldown/pluginutils@1.0.0-beta.9":
  version "1.0.0-beta.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.9.tgz#68ef9fff5a9791a642cea0dc4380ce6cb487a84a"
  integrity sha1-aO+f/1qXkaZCzqDcQ4DObLSHqEo=

"@rollup/pluginutils@^5.0.2", "@rollup/pluginutils@^5.1.3", "@rollup/pluginutils@^5.1.4":
  version "5.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/pluginutils/-/pluginutils-5.1.4.tgz#bb94f1f9eaaac944da237767cdfee6c5b2262d4a"
  integrity sha1-u5Tx+eqqyUTaI3dnzf7mxbImLUo=
  dependencies:
    "@types/estree" "^1.0.0"
    estree-walker "^2.0.2"
    picomatch "^4.0.2"

"@rollup/rollup-android-arm-eabi@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-android-arm-eabi/-/rollup-android-arm-eabi-4.43.0.tgz#9241b59af721beb7e3587a56c6c245d6c465753d"
  integrity sha1-kkG1mvchvrfjWHpWxsJF1sRldT0=

"@rollup/rollup-android-arm64@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-android-arm64/-/rollup-android-arm64-4.43.0.tgz#f70ee53ba991fdd65c277b0716c559736d490a58"
  integrity sha1-9w7lO6mR/dZcJ3sHFsVZc21JClg=

"@rollup/rollup-darwin-arm64@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-darwin-arm64/-/rollup-darwin-arm64-4.43.0.tgz#9f59000e817cf5760d87515ce899f8b93fe8756a"
  integrity sha1-n1kADoF89XYNh1Fc6Jn4uT/odWo=

"@rollup/rollup-darwin-x64@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-darwin-x64/-/rollup-darwin-x64-4.43.0.tgz#c92aebd02725ae1b88bdce40f08f7823e8055c78"
  integrity sha1-ySrr0CclrhuIvc5A8I94I+gFXHg=

"@rollup/rollup-freebsd-arm64@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-freebsd-arm64/-/rollup-freebsd-arm64-4.43.0.tgz#b128dbe7b353922ddd729a4fc4e408ddcbf338b5"
  integrity sha1-sSjb57NTki3dcppPxOQI3cvzOLU=

"@rollup/rollup-freebsd-x64@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-freebsd-x64/-/rollup-freebsd-x64-4.43.0.tgz#88297a0ddfadddd61d7d9b73eb42b3f227301d30"
  integrity sha1-iCl6Dd+t3dYdfZtz60Kz8icwHTA=

"@rollup/rollup-linux-arm-gnueabihf@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-arm-gnueabihf/-/rollup-linux-arm-gnueabihf-4.43.0.tgz#a59afc092523ebe43d3899f33da9cdd2ec01fb87"
  integrity sha1-pZr8CSUj6+Q9OJnzPanN0uwB+4c=

"@rollup/rollup-linux-arm-musleabihf@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-arm-musleabihf/-/rollup-linux-arm-musleabihf-4.43.0.tgz#3095c1327b794bd187d03e372e633717fb69b4c0"
  integrity sha1-MJXBMnt5S9GH0D43LmM3F/tptMA=

"@rollup/rollup-linux-arm64-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-arm64-gnu/-/rollup-linux-arm64-gnu-4.43.0.tgz#e43bb77df3a6de85312e991d1e3ad352d1abb00d"
  integrity sha1-5Du3ffOm3oUxLpkdHjrTUtGrsA0=

"@rollup/rollup-linux-arm64-musl@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-arm64-musl/-/rollup-linux-arm64-musl-4.43.0.tgz#34873a437bcd87618f702dc66f0cbce170aebf9f"
  integrity sha1-NIc6Q3vNh2GPcC3Gbwy84XCuv58=

"@rollup/rollup-linux-loongarch64-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-loongarch64-gnu/-/rollup-linux-loongarch64-gnu-4.43.0.tgz#224ff524349e365baa56f1f512822548c2d76910"
  integrity sha1-Ik/1JDSeNluqVvH1EoIlSMLXaRA=

"@rollup/rollup-linux-powerpc64le-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-powerpc64le-gnu/-/rollup-linux-powerpc64le-gnu-4.43.0.tgz#43c3c053b26ace18a1d3dab204596a466c1b0e34"
  integrity sha1-Q8PAU7Jqzhih09qyBFlqRmwbDjQ=

"@rollup/rollup-linux-riscv64-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-riscv64-gnu/-/rollup-linux-riscv64-gnu-4.43.0.tgz#e7df825d71daefa7037605015455aa58be43cd7a"
  integrity sha1-59+CXXHa76cDdgUBVFWqWL5DzXo=

"@rollup/rollup-linux-riscv64-musl@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-riscv64-musl/-/rollup-linux-riscv64-musl-4.43.0.tgz#d76ad93a7f4c0b2855a024d8d859196acf38acf5"
  integrity sha1-12rZOn9MCyhVoCTY2FkZas84rPU=

"@rollup/rollup-linux-s390x-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-s390x-gnu/-/rollup-linux-s390x-gnu-4.43.0.tgz#0852608843d05852af3f447bf43bb63d80d62b6a"
  integrity sha1-CFJgiEPQWFKvP0R79Du2PYDWK2o=

"@rollup/rollup-linux-x64-gnu@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-x64-gnu/-/rollup-linux-x64-gnu-4.43.0.tgz#d16a57f86357a4e697142bee244afed59b24e6c5"
  integrity sha1-0WpX+GNXpOaXFCvuJEr+1Zsk5sU=

"@rollup/rollup-linux-x64-musl@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-linux-x64-musl/-/rollup-linux-x64-musl-4.43.0.tgz#51cbc8b1eb46ebc0e284725418b6fbf48686e4e2"
  integrity sha1-UcvIsetG68DihHJUGLb79IaG5OI=

"@rollup/rollup-win32-arm64-msvc@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-win32-arm64-msvc/-/rollup-win32-arm64-msvc-4.43.0.tgz#d6d84aace2b211119bf0ab1c586e29d01e32aa01"
  integrity sha1-1thKrOKyERGb8KscWG4p0B4yqgE=

"@rollup/rollup-win32-ia32-msvc@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-win32-ia32-msvc/-/rollup-win32-ia32-msvc-4.43.0.tgz#4af33168de2f65b97a8f36bd1d8d21cea34d3ccb"
  integrity sha1-SvMxaN4vZbl6jza9HY0hzqNNPMs=

"@rollup/rollup-win32-x64-msvc@4.43.0":
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.43.0.tgz#42a88207659e404e8ffa655cae763cbad94906ab"
  integrity sha1-QqiCB2WeQE6P+mVcrnY8utlJBqs=

"@rushstack/node-core-library@5.13.1":
  version "5.13.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rushstack/node-core-library/-/node-core-library-5.13.1.tgz#e56b915ecb08b5a92711acac6b233417353a32dc"
  integrity sha1-5WuRXssItaknEaysayM0FzU6Mtw=
  dependencies:
    ajv "~8.13.0"
    ajv-draft-04 "~1.0.0"
    ajv-formats "~3.0.1"
    fs-extra "~11.3.0"
    import-lazy "~4.0.0"
    jju "~1.4.0"
    resolve "~1.22.1"
    semver "~7.5.4"

"@rushstack/rig-package@0.5.3":
  version "0.5.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rushstack/rig-package/-/rig-package-0.5.3.tgz#ea4d8a3458540b1295500149c04e645f23134e5d"
  integrity sha1-6k2KNFhUCxKVUAFJwE5kXyMTTl0=
  dependencies:
    resolve "~1.22.1"
    strip-json-comments "~3.1.1"

"@rushstack/terminal@0.15.3":
  version "0.15.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rushstack/terminal/-/terminal-0.15.3.tgz#365e0ae5ac73bb4883b096ae36c5011f52911861"
  integrity sha1-Nl4K5axzu0iDsJauNsUBH1KRGGE=
  dependencies:
    "@rushstack/node-core-library" "5.13.1"
    supports-color "~8.1.1"

"@rushstack/ts-command-line@5.0.1":
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@rushstack/ts-command-line/-/ts-command-line-5.0.1.tgz#e147394b5ce87ef79db95b5b4f155461d6f2c50e"
  integrity sha1-4Uc5S1zofveduVtbTxVUYdbyxQ4=
  dependencies:
    "@rushstack/terminal" "0.15.3"
    "@types/argparse" "1.0.38"
    argparse "~1.0.9"
    string-argv "~0.3.1"

"@sinclair/typebox@^0.24.1":
  version "0.24.51"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@sinclair/typebox/-/typebox-0.24.51.tgz#645f33fe4e02defe26f2f5c0410e1c094eac7f5f"
  integrity sha1-ZF8z/k4C3v4m8vXAQQ4cCU6sf18=

"@sinclair/typebox@^0.27.8":
  version "0.27.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@sinclair/typebox/-/typebox-0.27.8.tgz#6667fac16c436b5434a387a34dedb013198f6e6e"
  integrity sha1-Zmf6wWxDa1Q0o4ejTe2wExmPbm4=

"@sinonjs/commons@^3.0.0":
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@sinonjs/commons/-/commons-3.0.1.tgz#1029357e44ca901a615585f6d27738dbc89084cd"
  integrity sha1-ECk1fkTKkBphVYX20nc428iQhM0=
  dependencies:
    type-detect "4.0.8"

"@sinonjs/fake-timers@^10.0.2":
  version "10.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz#55fdff1ecab9f354019129daf4df0dd4d923ea66"
  integrity sha1-Vf3/Hsq581QBkSna9N8N1Nkj6mY=
  dependencies:
    "@sinonjs/commons" "^3.0.0"

"@standard-schema/spec@^1.0.0":
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@standard-schema/spec/-/spec-1.0.0.tgz#f193b73dc316c4170f2e82a881da0f550d551b9c"
  integrity sha1-8ZO3PcMWxBcPLoKogdoPVQ1VG5w=

"@standard-schema/utils@^0.3.0":
  version "0.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@standard-schema/utils/-/utils-0.3.0.tgz#3d5e608f16c2390c10528e98e59aef6bf73cae7b"
  integrity sha1-PV5gjxbCOQwQUo6Y5Zrva/c8rns=

"@storybook/addon-actions@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-actions/-/addon-actions-8.6.12.tgz#b3a0ded04e0318f9dcad12c8946ae66426e0700e"
  integrity sha1-s6De0E4DGPncrRLIlGrmZCbgcA4=
  dependencies:
    "@storybook/global" "^5.0.0"
    "@types/uuid" "^9.0.1"
    dequal "^2.0.2"
    polished "^4.2.2"
    uuid "^9.0.0"

"@storybook/addon-backgrounds@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-backgrounds/-/addon-backgrounds-8.6.12.tgz#a2d1f9e9802cc9594b6ae84e2fd8fc033d506d29"
  integrity sha1-otH56YAsyVlLauhOL9j8Az1QbSk=
  dependencies:
    "@storybook/global" "^5.0.0"
    memoizerific "^1.11.3"
    ts-dedent "^2.0.0"

"@storybook/addon-controls@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-controls/-/addon-controls-8.6.12.tgz#a2a8d9c0b571040ea7d6890ed250f2e1175bf176"
  integrity sha1-oqjZwLVxBA6n1okO0lDy4Rdb8XY=
  dependencies:
    "@storybook/global" "^5.0.0"
    dequal "^2.0.2"
    ts-dedent "^2.0.0"

"@storybook/addon-docs@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-docs/-/addon-docs-8.6.12.tgz#81ef50ae5cdc38ac3858f3a1322b0e38d3bf764f"
  integrity sha1-ge9QrlzcOKw4WPOhMisOONO/dk8=
  dependencies:
    "@mdx-js/react" "^3.0.0"
    "@storybook/blocks" "8.6.12"
    "@storybook/csf-plugin" "8.6.12"
    "@storybook/react-dom-shim" "8.6.12"
    react "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
    react-dom "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"
    ts-dedent "^2.0.0"

"@storybook/addon-essentials@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-essentials/-/addon-essentials-8.6.12.tgz#8553b25e365e33905f4e5fe1e35bc22c15d525b3"
  integrity sha1-hVOyXjZeM5BfTl/h41vCLBXVJbM=
  dependencies:
    "@storybook/addon-actions" "8.6.12"
    "@storybook/addon-backgrounds" "8.6.12"
    "@storybook/addon-controls" "8.6.12"
    "@storybook/addon-docs" "8.6.12"
    "@storybook/addon-highlight" "8.6.12"
    "@storybook/addon-measure" "8.6.12"
    "@storybook/addon-outline" "8.6.12"
    "@storybook/addon-toolbars" "8.6.12"
    "@storybook/addon-viewport" "8.6.12"
    ts-dedent "^2.0.0"

"@storybook/addon-highlight@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-highlight/-/addon-highlight-8.6.12.tgz#3d65f89ef46a0b50b1c256b44a6f0be6572303dc"
  integrity sha1-PWX4nvRqC1Cxwla0Sm8L5lcjA9w=
  dependencies:
    "@storybook/global" "^5.0.0"

"@storybook/addon-interactions@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-interactions/-/addon-interactions-8.6.12.tgz#04fadf3809a6afdfa0ab38c52ca0584253f107dd"
  integrity sha1-BPrfOAmmr9+gqzjFLKBYQlPxB90=
  dependencies:
    "@storybook/global" "^5.0.0"
    "@storybook/instrumenter" "8.6.12"
    "@storybook/test" "8.6.12"
    polished "^4.2.2"
    ts-dedent "^2.2.0"

"@storybook/addon-measure@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-measure/-/addon-measure-8.6.12.tgz#8b5988e41be6d8098665b4bc90f260ad33b4455e"
  integrity sha1-i1mI5Bvm2AmGZbS8kPJgrTO0RV4=
  dependencies:
    "@storybook/global" "^5.0.0"
    tiny-invariant "^1.3.1"

"@storybook/addon-onboarding@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-onboarding/-/addon-onboarding-8.6.12.tgz#531899977b902abb03b9ff2969975993ffaf255b"
  integrity sha1-UxiZl3uQKrsDuf8paZdZk/+vJVs=

"@storybook/addon-outline@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-outline/-/addon-outline-8.6.12.tgz#82153dd55b7e7e8510703ad68518dda9d6884113"
  integrity sha1-ghU91Vt+foUQcDrWhRjdqdaIQRM=
  dependencies:
    "@storybook/global" "^5.0.0"
    ts-dedent "^2.0.0"

"@storybook/addon-themes@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-themes/-/addon-themes-8.6.12.tgz#e9d648ed51a05442cb67881b302932a6e3217699"
  integrity sha1-6dZI7VGgVELLZ4gbMCkypuMhdpk=
  dependencies:
    ts-dedent "^2.0.0"

"@storybook/addon-toolbars@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-toolbars/-/addon-toolbars-8.6.12.tgz#debf68d7aa807216a479a2b2b5670baabe1a2ebc"
  integrity sha1-3r9o16qAchakeaKytWcLqr4aLrw=

"@storybook/addon-viewport@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/addon-viewport/-/addon-viewport-8.6.12.tgz#70c6b33c03767f9a1b576c713bc984474e33d3a1"
  integrity sha1-cMazPAN2f5obV2xxO8mER04z06E=
  dependencies:
    memoizerific "^1.11.3"

"@storybook/blocks@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/blocks/-/blocks-8.6.12.tgz#b129deb427f39c2772600bb6fcef415687e5556c"
  integrity sha1-sSnetCfznCdyYAu2/O9BVoflVWw=
  dependencies:
    "@storybook/icons" "^1.2.12"
    ts-dedent "^2.0.0"

"@storybook/builder-vite@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/builder-vite/-/builder-vite-8.6.12.tgz#33e5f795d1531bfcaa8f697f81a1baff2f1deedd"
  integrity sha1-M+X3ldFTG/yqj2l/gaG6/y8d7t0=
  dependencies:
    "@storybook/csf-plugin" "8.6.12"
    browser-assert "^1.2.1"
    ts-dedent "^2.0.0"

"@storybook/components@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/components/-/components-8.6.12.tgz#8bfaf4bb061145c8a8acdda6f334691ad716b202"
  integrity sha1-i/r0uwYRRciorN2m8zRpGtcWsgI=

"@storybook/components@^8.0.0":
  version "8.6.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/components/-/components-8.6.14.tgz#3cfc5e120f3dc38990fc37b34a22eff1e3f4bdfb"
  integrity sha1-PPxeEg89w4mQ/DezSiLv8eP0vfs=

"@storybook/core-events@^8.0.0":
  version "8.6.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/core-events/-/core-events-8.6.14.tgz#ba2be7b0644655d17db143b2be5f45199e617af4"
  integrity sha1-uivnsGRGVdF9sUOyvl9FGZ5hevQ=

"@storybook/core@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/core/-/core-8.6.12.tgz#ad6d7587f8a8b9f05764ba39f99c1e67e1ba341f"
  integrity sha1-rW11h/ioufBXZLo5+ZweZ+G6NB8=
  dependencies:
    "@storybook/theming" "8.6.12"
    better-opn "^3.0.2"
    browser-assert "^1.2.1"
    esbuild "^0.18.0 || ^0.19.0 || ^0.20.0 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0"
    esbuild-register "^3.5.0"
    jsdoc-type-pratt-parser "^4.0.0"
    process "^0.11.10"
    recast "^0.23.5"
    semver "^7.6.2"
    util "^0.12.5"
    ws "^8.2.3"

"@storybook/csf-plugin@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/csf-plugin/-/csf-plugin-8.6.12.tgz#5850c7ffe3864ded80a0ca9b906bb6e09aab492c"
  integrity sha1-WFDH/+OGTe2AoMqbkGu24JqrSSw=
  dependencies:
    unplugin "^1.3.1"

"@storybook/csf@^0.1.11":
  version "0.1.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/csf/-/csf-0.1.13.tgz#c8a9bea2ae518a3d9700546748fa30a8b07f7f80"
  integrity sha1-yKm+oq5Rij2XAFRnSPowqLB/f4A=
  dependencies:
    type-fest "^2.19.0"

"@storybook/expect@storybook-jest":
  version "28.1.3-5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/expect/-/expect-28.1.3-5.tgz#ecb680851866aa411238b23b48c43285bd7477cf"
  integrity sha1-7LaAhRhmqkESOLI7SMQyhb10d88=
  dependencies:
    "@types/jest" "28.1.3"

"@storybook/global@^5.0.0":
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/global/-/global-5.0.0.tgz#b793d34b94f572c1d7d9e0f44fac4e0dbc9572ed"
  integrity sha1-t5PTS5T1csHX2eD0T6xODbyVcu0=

"@storybook/icons@^1.2.12", "@storybook/icons@^1.2.5":
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/icons/-/icons-1.4.0.tgz#7cf7ab3dfb41943930954c4ef493a73798d8b31d"
  integrity sha1-fPerPftBlDkwlUxO9JOnN5jYsx0=

"@storybook/instrumenter@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/instrumenter/-/instrumenter-8.6.12.tgz#7a99e061c3b9574b38d5677540852ba2b330e6a5"
  integrity sha1-epngYcO5V0s41Wd1QIUrorMw5qU=
  dependencies:
    "@storybook/global" "^5.0.0"
    "@vitest/utils" "^2.1.1"

"@storybook/jest@^0.2.3":
  version "0.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/jest/-/jest-0.2.3.tgz#21512b92469978b37f69d6555949801ef34af153"
  integrity sha1-IVErkkaZeLN/adZVWUmAHvNK8VM=
  dependencies:
    "@storybook/expect" storybook-jest
    "@testing-library/jest-dom" "^6.1.2"
    "@types/jest" "28.1.3"
    jest-mock "^27.3.0"

"@storybook/manager-api@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/manager-api/-/manager-api-8.6.12.tgz#a9169a3fb8de97ba406ed83314112d5c47d035ad"
  integrity sha1-qRaaP7jel7pAbtgzFBEtXEfQNa0=

"@storybook/manager-api@^8.0.0":
  version "8.6.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/manager-api/-/manager-api-8.6.14.tgz#1e0740193fbfd4a66e9ff5f75c7f976e16028752"
  integrity sha1-HgdAGT+/1KZun/X3XH+XbhYCh1I=

"@storybook/preview-api@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/preview-api/-/preview-api-8.6.12.tgz#07ac0c697801d4c2ffe26afb2673d8a417bd63ef"
  integrity sha1-B6wMaXgB1ML/4mr7JnPYpBe9Y+8=

"@storybook/react-dom-shim@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/react-dom-shim/-/react-dom-shim-8.6.12.tgz#425fc62e8445e41239c18b0f78ce97a6e06d4eae"
  integrity sha1-Ql/GLoRF5BI5wYsPeM6XpuBtTq4=

"@storybook/react-vite@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/react-vite/-/react-vite-8.6.12.tgz#48e8f86a84579cde948966a159d6001ca711c48a"
  integrity sha1-SOj4aoRXnN6UiWahWdYAHKcRxIo=
  dependencies:
    "@joshwooding/vite-plugin-react-docgen-typescript" "0.5.0"
    "@rollup/pluginutils" "^5.0.2"
    "@storybook/builder-vite" "8.6.12"
    "@storybook/react" "8.6.12"
    find-up "^5.0.0"
    magic-string "^0.30.0"
    react-docgen "^7.0.0"
    resolve "^1.22.8"
    tsconfig-paths "^4.2.0"

"@storybook/react@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/react/-/react-8.6.12.tgz#b3918a83555973a90fb132843c080465ec1bea66"
  integrity sha1-s5GKg1VZc6kPsTKEPAgEZewb6mY=
  dependencies:
    "@storybook/components" "8.6.12"
    "@storybook/global" "^5.0.0"
    "@storybook/manager-api" "8.6.12"
    "@storybook/preview-api" "8.6.12"
    "@storybook/react-dom-shim" "8.6.12"
    "@storybook/theming" "8.6.12"

"@storybook/test@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/test/-/test-8.6.12.tgz#ca60fff18b0c5ff2ffcfe4f35523739efb358d6a"
  integrity sha1-ymD/8YsMX/L/z+TzVSNznvs1jWo=
  dependencies:
    "@storybook/global" "^5.0.0"
    "@storybook/instrumenter" "8.6.12"
    "@testing-library/dom" "10.4.0"
    "@testing-library/jest-dom" "6.5.0"
    "@testing-library/user-event" "14.5.2"
    "@vitest/expect" "2.0.5"
    "@vitest/spy" "2.0.5"

"@storybook/theming@8.6.12":
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/theming/-/theming-8.6.12.tgz#2d521f422daf85e0b27179520ce1cfb520284447"
  integrity sha1-LVIfQi2vheCycXlSDOHPtSAoREc=

"@storybook/theming@^8.0.0":
  version "8.6.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@storybook/theming/-/theming-8.6.14.tgz#78c6dc878f705de70c67f2b2d08b8313b985d81a"
  integrity sha1-eMbch49wXecMZ/Ky0IuDE7mF2Bo=

"@svgr/babel-plugin-add-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-add-jsx-attribute/-/babel-plugin-add-jsx-attribute-8.0.0.tgz#4001f5d5dd87fa13303e36ee106e3ff3a7eb8b22"
  integrity sha1-QAH11d2H+hMwPjbuEG4/86friyI=

"@svgr/babel-plugin-remove-jsx-attribute@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-remove-jsx-attribute/-/babel-plugin-remove-jsx-attribute-8.0.0.tgz#69177f7937233caca3a1afb051906698f2f59186"
  integrity sha1-aRd/eTcjPKyjoa+wUZBmmPL1kYY=

"@svgr/babel-plugin-remove-jsx-empty-expression@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-remove-jsx-empty-expression/-/babel-plugin-remove-jsx-empty-expression-8.0.0.tgz#c2c48104cfd7dcd557f373b70a56e9e3bdae1d44"
  integrity sha1-wsSBBM/X3NVX83O3Clbp472uHUQ=

"@svgr/babel-plugin-replace-jsx-attribute-value@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-replace-jsx-attribute-value/-/babel-plugin-replace-jsx-attribute-value-8.0.0.tgz#8fbb6b2e91fa26ac5d4aa25c6b6e4f20f9c0ae27"
  integrity sha1-j7trLpH6JqxdSqJca25PIPnAric=

"@svgr/babel-plugin-svg-dynamic-title@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-svg-dynamic-title/-/babel-plugin-svg-dynamic-title-8.0.0.tgz#1d5ba1d281363fc0f2f29a60d6d936f9bbc657b0"
  integrity sha1-HVuh0oE2P8Dy8ppg1tk2+bvGV7A=

"@svgr/babel-plugin-svg-em-dimensions@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-svg-em-dimensions/-/babel-plugin-svg-em-dimensions-8.0.0.tgz#35e08df300ea8b1d41cb8f62309c241b0369e501"
  integrity sha1-NeCN8wDqix1By49iMJwkGwNp5QE=

"@svgr/babel-plugin-transform-react-native-svg@8.1.0":
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-transform-react-native-svg/-/babel-plugin-transform-react-native-svg-8.1.0.tgz#90a8b63998b688b284f255c6a5248abd5b28d754"
  integrity sha1-kKi2OZi2iLKE8lXGpSSKvVso11Q=

"@svgr/babel-plugin-transform-svg-component@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-plugin-transform-svg-component/-/babel-plugin-transform-svg-component-8.0.0.tgz#013b4bfca88779711f0ed2739f3f7efcefcf4f7e"
  integrity sha1-ATtL/KiHeXEfDtJznz9+/O/PT34=

"@svgr/babel-preset@8.1.0":
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/babel-preset/-/babel-preset-8.1.0.tgz#0e87119aecdf1c424840b9d4565b7137cabf9ece"
  integrity sha1-DocRmuzfHEJIQLnUVltxN8q/ns4=
  dependencies:
    "@svgr/babel-plugin-add-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-attribute" "8.0.0"
    "@svgr/babel-plugin-remove-jsx-empty-expression" "8.0.0"
    "@svgr/babel-plugin-replace-jsx-attribute-value" "8.0.0"
    "@svgr/babel-plugin-svg-dynamic-title" "8.0.0"
    "@svgr/babel-plugin-svg-em-dimensions" "8.0.0"
    "@svgr/babel-plugin-transform-react-native-svg" "8.1.0"
    "@svgr/babel-plugin-transform-svg-component" "8.0.0"

"@svgr/core@^8.1.0":
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/core/-/core-8.1.0.tgz#41146f9b40b1a10beaf5cc4f361a16a3c1885e88"
  integrity sha1-QRRvm0CxoQvq9cxPNhoWo8GIXog=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    camelcase "^6.2.0"
    cosmiconfig "^8.1.3"
    snake-case "^3.0.4"

"@svgr/hast-util-to-babel-ast@8.0.0":
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/hast-util-to-babel-ast/-/hast-util-to-babel-ast-8.0.0.tgz#6952fd9ce0f470e1aded293b792a2705faf4ffd4"
  integrity sha1-aVL9nOD0cOGt7Sk7eSonBfr0/9Q=
  dependencies:
    "@babel/types" "^7.21.3"
    entities "^4.4.0"

"@svgr/plugin-jsx@^8.1.0":
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@svgr/plugin-jsx/-/plugin-jsx-8.1.0.tgz#96969f04a24b58b174ee4cd974c60475acbd6928"
  integrity sha1-lpafBKJLWLF07kzZdMYEday9aSg=
  dependencies:
    "@babel/core" "^7.21.3"
    "@svgr/babel-preset" "8.1.0"
    "@svgr/hast-util-to-babel-ast" "8.0.0"
    svg-parser "^2.0.4"

"@swc/core-darwin-arm64@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-darwin-arm64/-/core-darwin-arm64-1.12.1.tgz#a2aa56b644472f12e66357701ac8120cc331ed01"
  integrity sha1-oqpWtkRHLxLmY1dwGsgSDMMx7QE=

"@swc/core-darwin-x64@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-darwin-x64/-/core-darwin-x64-1.12.1.tgz#075049a2cfb386c3e533b535fd0ec4d1c851b3af"
  integrity sha1-B1BJos+zhsPlM7U1/Q7E0chRs68=

"@swc/core-linux-arm-gnueabihf@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-linux-arm-gnueabihf/-/core-linux-arm-gnueabihf-1.12.1.tgz#53ace44bc470eb96fc5115e2f83d240b11be98ae"
  integrity sha1-U6zkS8Rw65b8URXi+D0kCxG+mK4=

"@swc/core-linux-arm64-gnu@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-linux-arm64-gnu/-/core-linux-arm64-gnu-1.12.1.tgz#d120abbb46d8d7f6b0ab831ee7868651ece87cd1"
  integrity sha1-0SCru0bY1/awq4Me54aGUezofNE=

"@swc/core-linux-arm64-musl@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-linux-arm64-musl/-/core-linux-arm64-musl-1.12.1.tgz#468732c327c657fde5a486daf910cbf60391a6a2"
  integrity sha1-RocywyfGV/3lpIba+RDL9gORpqI=

"@swc/core-linux-x64-gnu@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-linux-x64-gnu/-/core-linux-x64-gnu-1.12.1.tgz#fda705587c211400017f163a45c24706b42649ba"
  integrity sha1-/acFWHwhFAABfxY6RcJHBrQmSbo=

"@swc/core-linux-x64-musl@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-linux-x64-musl/-/core-linux-x64-musl-1.12.1.tgz#c8d05ecdabbc23598d6bd880a2202eade55af9c9"
  integrity sha1-yNBezau8I1mNa9iAoiAureVa+ck=

"@swc/core-win32-arm64-msvc@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-win32-arm64-msvc/-/core-win32-arm64-msvc-1.12.1.tgz#dea562db2529d82bedcb71be1030a01b88707fc6"
  integrity sha1-3qVi2yUp2Cvty3G+EDCgG4hwf8Y=

"@swc/core-win32-ia32-msvc@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-win32-ia32-msvc/-/core-win32-ia32-msvc-1.12.1.tgz#8e2be4875df831b2c7d340bed0688094a914b03c"
  integrity sha1-jivkh134MbLH00C+0GiAlKkUsDw=

"@swc/core-win32-x64-msvc@1.12.1":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core-win32-x64-msvc/-/core-win32-x64-msvc-1.12.1.tgz#f0cf5136844cd42d93a6bff7c1be2b8cb1c71174"
  integrity sha1-8M9RNoRM1C2Tpr/3wb4rjLHHEXQ=

"@swc/core@^1.11.31":
  version "1.12.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/core/-/core-1.12.1.tgz#373759c60a3bf44a0dc02d411d31c31fbfd7c64b"
  integrity sha1-NzdZxgo79EoNwC1BHTHDH7/Xxks=
  dependencies:
    "@swc/counter" "^0.1.3"
    "@swc/types" "^0.1.23"
  optionalDependencies:
    "@swc/core-darwin-arm64" "1.12.1"
    "@swc/core-darwin-x64" "1.12.1"
    "@swc/core-linux-arm-gnueabihf" "1.12.1"
    "@swc/core-linux-arm64-gnu" "1.12.1"
    "@swc/core-linux-arm64-musl" "1.12.1"
    "@swc/core-linux-x64-gnu" "1.12.1"
    "@swc/core-linux-x64-musl" "1.12.1"
    "@swc/core-win32-arm64-msvc" "1.12.1"
    "@swc/core-win32-ia32-msvc" "1.12.1"
    "@swc/core-win32-x64-msvc" "1.12.1"

"@swc/counter@^0.1.3":
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/counter/-/counter-0.1.3.tgz#cc7463bd02949611c6329596fccd2b0ec782b0e9"
  integrity sha1-zHRjvQKUlhHGMpWW/M0rDseCsOk=

"@swc/types@^0.1.23":
  version "0.1.23"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@swc/types/-/types-0.1.23.tgz#7eabf88b9cfd929253859c562ae95982ee04b4e8"
  integrity sha1-fqv4i5z9kpJThZxWKulZgu4EtOg=
  dependencies:
    "@swc/counter" "^0.1.3"

"@tailwindcss/container-queries@0.1.1":
  version "0.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tailwindcss/container-queries/-/container-queries-0.1.1.tgz#9a759ce2cb8736a4c6a0cb93aeb740573a731974"
  integrity sha1-mnWc4suHNqTGoMuTrrdAVzpzGXQ=

"@tanstack/react-virtual@3.13.12":
  version "3.13.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tanstack/react-virtual/-/react-virtual-3.13.12.tgz#d372dc2783739cc04ec1a728ca8203937687a819"
  integrity sha1-03LcJ4NznMBOwacoyoIDk3aHqBk=
  dependencies:
    "@tanstack/virtual-core" "3.13.12"

"@tanstack/virtual-core@3.13.12":
  version "3.13.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tanstack/virtual-core/-/virtual-core-3.13.12.tgz#1dff176df9cc8f93c78c5e46bcea11079b397578"
  integrity sha1-Hf8XbfnMj5PHjF5GvOoRB5s5dXg=

"@testing-library/dom@10.4.0", "@testing-library/dom@^10.4.0":
  version "10.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/dom/-/dom-10.4.0.tgz#82a9d9462f11d240ecadbf406607c6ceeeff43a8"
  integrity sha1-gqnZRi8R0kDsrb9AZgfGzu7/Q6g=
  dependencies:
    "@babel/code-frame" "^7.10.4"
    "@babel/runtime" "^7.12.5"
    "@types/aria-query" "^5.0.1"
    aria-query "5.3.0"
    chalk "^4.1.0"
    dom-accessibility-api "^0.5.9"
    lz-string "^1.5.0"
    pretty-format "^27.0.2"

"@testing-library/jest-dom@6.5.0":
  version "6.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/jest-dom/-/jest-dom-6.5.0.tgz#50484da3f80fb222a853479f618a9ce5c47bfe54"
  integrity sha1-UEhNo/gPsiKoU0efYYqc5cR7/lQ=
  dependencies:
    "@adobe/css-tools" "^4.4.0"
    aria-query "^5.0.0"
    chalk "^3.0.0"
    css.escape "^1.5.1"
    dom-accessibility-api "^0.6.3"
    lodash "^4.17.21"
    redent "^3.0.0"

"@testing-library/jest-dom@^6.1.2", "@testing-library/jest-dom@^6.6.3":
  version "6.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/jest-dom/-/jest-dom-6.6.3.tgz#26ba906cf928c0f8172e182c6fe214eb4f9f2bd2"
  integrity sha1-JrqQbPkowPgXLhgsb+IU60+fK9I=
  dependencies:
    "@adobe/css-tools" "^4.4.0"
    aria-query "^5.0.0"
    chalk "^3.0.0"
    css.escape "^1.5.1"
    dom-accessibility-api "^0.6.3"
    lodash "^4.17.21"
    redent "^3.0.0"

"@testing-library/react@^16.3.0":
  version "16.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/react/-/react-16.3.0.tgz#3a85bb9bdebf180cd76dba16454e242564d598a6"
  integrity sha1-OoW7m96/GAzXbboWRU4kJWTVmKY=
  dependencies:
    "@babel/runtime" "^7.12.5"

"@testing-library/user-event@14.5.2":
  version "14.5.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/user-event/-/user-event-14.5.2.tgz#db7257d727c891905947bd1c1a99da20e03c2ebd"
  integrity sha1-23JX1yfIkZBZR70cGpnaIOA8Lr0=

"@testing-library/user-event@^14.6.1":
  version "14.6.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@testing-library/user-event/-/user-event-14.6.1.tgz#13e09a32d7a8b7060fe38304788ebf4197cd2149"
  integrity sha1-E+CaMteotwYP44MEeI6/QZfNIUk=

"@tsconfig/node10@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tsconfig/node10/-/node10-1.0.11.tgz#6ee46400685f130e278128c7b38b7e031ff5b2f2"
  integrity sha1-buRkAGhfEw4ngSjHs4t+Ax/1svI=

"@tsconfig/node12@^1.0.7":
  version "1.0.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tsconfig/node12/-/node12-1.0.11.tgz#ee3def1f27d9ed66dac6e46a295cffb0152e058d"
  integrity sha1-7j3vHyfZ7WbaxuRqKVz/sBUuBY0=

"@tsconfig/node14@^1.0.0":
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tsconfig/node14/-/node14-1.0.3.tgz#e4386316284f00b98435bf40f72f75a09dabf6c1"
  integrity sha1-5DhjFihPALmENb9A9y91oJ2r9sE=

"@tsconfig/node16@^1.0.2":
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@tsconfig/node16/-/node16-1.0.4.tgz#0b92dcc0cc1c81f6f306a381f28e31b1a56536e9"
  integrity sha1-C5LcwMwcgfbzBqOB8o4xsaVlNuk=

"@types/argparse@1.0.38":
  version "1.0.38"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/argparse/-/argparse-1.0.38.tgz#a81fd8606d481f873a3800c6ebae4f1d768a56a9"
  integrity sha1-qB/YYG1IH4c6OADG665PHXaKVqk=

"@types/aria-query@^5.0.1":
  version "5.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/aria-query/-/aria-query-5.0.4.tgz#1a31c3d378850d2778dabb6374d036dcba4ba708"
  integrity sha1-GjHD03iFDSd42rtjdNA23LpLpwg=

"@types/babel__core@^7.1.14", "@types/babel__core@^7.18.0", "@types/babel__core@^7.20.5":
  version "7.20.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/babel__core/-/babel__core-7.20.5.tgz#3df15f27ba85319caa07ba08d0721889bb39c017"
  integrity sha1-PfFfJ7qFMZyqB7oI0HIYibs5wBc=
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  version "7.27.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/babel__generator/-/babel__generator-7.27.0.tgz#b5819294c51179957afaec341442f9341e4108a9"
  integrity sha1-tYGSlMUReZV6+uw0FEL5NB5BCKk=
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  version "7.4.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/babel__template/-/babel__template-7.4.4.tgz#5672513701c1b2199bc6dad636a9d7491586766f"
  integrity sha1-VnJRNwHBshmbxtrWNqnXSRWGdm8=
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*", "@types/babel__traverse@^7.0.6", "@types/babel__traverse@^7.18.0":
  version "7.20.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/babel__traverse/-/babel__traverse-7.20.7.tgz#968cdc2366ec3da159f61166428ee40f370e56c2"
  integrity sha1-lozcI2bsPaFZ9hFmQo7kDzcOVsI=
  dependencies:
    "@babel/types" "^7.20.7"

"@types/chai@^5.2.2":
  version "5.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/chai/-/chai-5.2.2.tgz#6f14cea18180ffc4416bc0fd12be05fdd73bdd6b"
  integrity sha1-bxTOoYGA/8RBa8D9Er4F/dc73Ws=
  dependencies:
    "@types/deep-eql" "*"

"@types/classnames@2.2.6":
  version "2.2.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/classnames/-/classnames-2.2.6.tgz#dbe8a666156d556ed018e15a4c65f08937c3f628"
  integrity sha1-2+imZhVtVW7QGOFaTGXwiTfD9ig=

"@types/d3-array@^3.0.3":
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-array/-/d3-array-3.2.1.tgz#1f6658e3d2006c4fceac53fde464166859f8b8c5"
  integrity sha1-H2ZY49IAbE/OrFP95GQWaFn4uMU=

"@types/d3-color@*":
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-color/-/d3-color-3.1.3.tgz#368c961a18de721da8200e80bf3943fb53136af2"
  integrity sha1-NoyWGhjech2oIA6AvzlD+1MTavI=

"@types/d3-ease@^3.0.0":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-ease/-/d3-ease-3.0.2.tgz#e28db1bfbfa617076f7770dd1d9a48eaa3b6c51b"
  integrity sha1-4o2xv7+mFwdvd3DdHZpI6qO2xRs=

"@types/d3-interpolate@^3.0.1":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-interpolate/-/d3-interpolate-3.0.4.tgz#412b90e84870285f2ff8a846c6eb60344f12a41c"
  integrity sha1-QSuQ6EhwKF8v+KhGxutgNE8SpBw=
  dependencies:
    "@types/d3-color" "*"

"@types/d3-path@*":
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-path/-/d3-path-3.1.1.tgz#f632b380c3aca1dba8e34aa049bcd6a4af23df8a"
  integrity sha1-9jKzgMOsoduo40qgSbzWpK8j34o=

"@types/d3-scale@^4.0.2":
  version "4.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-scale/-/d3-scale-4.0.9.tgz#57a2f707242e6fe1de81ad7bfcccaaf606179afb"
  integrity sha1-V6L3ByQub+Hega17/Myq9gYXmvs=
  dependencies:
    "@types/d3-time" "*"

"@types/d3-shape@^3.1.0":
  version "3.1.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-shape/-/d3-shape-3.1.7.tgz#2b7b423dc2dfe69c8c93596e673e37443348c555"
  integrity sha1-K3tCPcLf5pyMk1luZz43RDNIxVU=
  dependencies:
    "@types/d3-path" "*"

"@types/d3-time@*", "@types/d3-time@^3.0.0":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-time/-/d3-time-3.0.4.tgz#8472feecd639691450dd8000eb33edd444e1323f"
  integrity sha1-hHL+7NY5aRRQ3YAA6zPt1EThMj8=

"@types/d3-timer@^3.0.0":
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/d3-timer/-/d3-timer-3.0.2.tgz#70bbda77dc23aa727413e22e214afa3f0e852f70"
  integrity sha1-cLvad9wjqnJ0E+IuIUr6Pw6FL3A=

"@types/deep-eql@*":
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/deep-eql/-/deep-eql-4.0.2.tgz#334311971d3a07121e7eb91b684a605e7eea9cbd"
  integrity sha1-M0MRlx06BxIefrkbaEpgXn7qnL0=

"@types/doctrine@^0.0.9":
  version "0.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/doctrine/-/doctrine-0.0.9.tgz#d86a5f452a15e3e3113b99e39616a9baa0f9863f"
  integrity sha1-2GpfRSoV4+MRO5njlhapuqD5hj8=

"@types/estree@1.0.7":
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/estree/-/estree-1.0.7.tgz#4158d3105276773d5b7695cd4834b1722e4f37a8"
  integrity sha1-QVjTEFJ2dz1bdpXNSDSxci5PN6g=

"@types/estree@^1.0.0":
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/estree/-/estree-1.0.8.tgz#958b91c991b1867ced318bedea0e215ee050726e"
  integrity sha1-lYuRyZGxhnztMYvt6g4hXuBQcm4=

"@types/graceful-fs@^4.1.3":
  version "4.1.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/graceful-fs/-/graceful-fs-4.1.9.tgz#2a06bc0f68a20ab37b3e36aa238be6abdf49e8b4"
  integrity sha1-Kga8D2iiCrN7PjaqI4vmq99J6LQ=
  dependencies:
    "@types/node" "*"

"@types/istanbul-lib-coverage@*", "@types/istanbul-lib-coverage@^2.0.0", "@types/istanbul-lib-coverage@^2.0.1":
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz#7739c232a1fee9b4d3ce8985f314c0c6d33549d7"
  integrity sha1-dznCMqH+6bTTzomF8xTAxtM1Sdc=

"@types/istanbul-lib-report@*":
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz#53047614ae72e19fc0401d872de3ae2b4ce350bf"
  integrity sha1-UwR2FK5y4Z/AQB2HLeOuK0zjUL8=
  dependencies:
    "@types/istanbul-lib-coverage" "*"

"@types/istanbul-reports@^3.0.0":
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz#0f03e3d2f670fbdac586e34b433783070cc16f54"
  integrity sha1-DwPj0vZw+9rFhuNLQzeDBwzBb1Q=
  dependencies:
    "@types/istanbul-lib-report" "*"

"@types/jest@28.1.3":
  version "28.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/jest/-/jest-28.1.3.tgz#52f3f3e50ce59191ff5fbb1084896cc0cf30c9ce"
  integrity sha1-UvPz5QzlkZH/X7sQhIlswM8wyc4=
  dependencies:
    jest-matcher-utils "^28.0.0"
    pretty-format "^28.0.0"

"@types/json5@^0.0.29":
  version "0.0.29"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/json5/-/json5-0.0.29.tgz#ee28707ae94e11d2b827bcbe5270bcea7f3e71ee"
  integrity sha1-7ihweulOEdK4J7y+UnC86n8+ce4=

"@types/lodash.debounce@4.0.6":
  version "4.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/lodash.debounce/-/lodash.debounce-4.0.6.tgz#c5a2326cd3efc46566c47e4c0aa248dc0ee57d60"
  integrity sha1-xaIybNPvxGVmxH5MCqJI3A7lfWA=
  dependencies:
    "@types/lodash" "*"

"@types/lodash@*":
  version "4.17.18"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/lodash/-/lodash-4.17.18.tgz#4710e7db5b3857103764bf7b7b666414e6141baf"
  integrity sha1-RxDn21s4VxA3ZL97e2ZkFOYUG68=

"@types/lodash@4.17.16":
  version "4.17.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/lodash/-/lodash-4.17.16.tgz#94ae78fab4a38d73086e962d0b65c30d816bfb0a"
  integrity sha1-lK54+rSjjXMIbpYtC2XDDYFr+wo=

"@types/mdx@^2.0.0":
  version "2.0.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/mdx/-/mdx-2.0.13.tgz#68f6877043d377092890ff5b298152b0a21671bd"
  integrity sha1-aPaHcEPTdwkokP9bKYFSsKIWcb0=

"@types/node@*":
  version "24.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/node/-/node-24.0.3.tgz#f935910f3eece3a3a2f8be86b96ba833dc286cab"
  integrity sha1-+TWRDz7s46Oi+L6GuWuoM9wobKs=
  dependencies:
    undici-types "~7.8.0"

"@types/node@22.13.12":
  version "22.13.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/node/-/node-22.13.12.tgz#ee0e492300f157f60913aa20471b636effa3884b"
  integrity sha1-7g5JIwDxV/YJE6ogRxtjbv+jiEs=
  dependencies:
    undici-types "~6.20.0"

"@types/node@^12.7.1":
  version "12.20.55"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/node/-/node-12.20.55.tgz#c329cbd434c42164f846b909bd6f85b5537f6240"
  integrity sha1-wynL1DTEIWT4RrkJvW+FtVN/YkA=

"@types/normalize-package-data@^2.4.0":
  version "2.4.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz#56e2cc26c397c038fab0e3a917a12d5c5909e901"
  integrity sha1-VuLMJsOXwDj6sOOpF6EtXFkJ6QE=

"@types/parse-json@^4.0.0":
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/parse-json/-/parse-json-4.0.2.tgz#5950e50960793055845e956c427fc2b0d70c5239"
  integrity sha1-WVDlCWB5MFWEXpVsQn/CsNcMUjk=

"@types/prismjs@^1.26.0":
  version "1.26.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/prismjs/-/prismjs-1.26.5.tgz#72499abbb4c4ec9982446509d2f14fb8483869d6"
  integrity sha1-ckmau7TE7JmCRGUJ0vFPuEg4adY=

"@types/prop-types@*":
  version "15.7.15"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/prop-types/-/prop-types-15.7.15.tgz#e6e5a86d602beaca71ce5163fadf5f95d70931c7"
  integrity sha1-5uWobWAr6spxzlFj+t9fldcJMcc=

"@types/react-dom@^18.3.0":
  version "18.3.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/react-dom/-/react-dom-18.3.7.tgz#b89ddf2cd83b4feafcc4e2ea41afdfb95a0d194f"
  integrity sha1-uJ3fLNg7T+r8xOLqQa/fuVoNGU8=

"@types/react-infinite-scroller@1.2.5":
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/react-infinite-scroller/-/react-infinite-scroller-1.2.5.tgz#7c770be59465f3aaa1b86377d792d52de5e74047"
  integrity sha1-fHcL5ZRl86qhuGN315LVLeXnQEc=
  dependencies:
    "@types/react" "*"

"@types/react-transition-group@4.4.12":
  version "4.4.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/react-transition-group/-/react-transition-group-4.4.12.tgz#b5d76568485b02a307238270bfe96cb51ee2a044"
  integrity sha1-tddlaEhbAqMHI4Jwv+lstR7ioEQ=

"@types/react@*":
  version "19.1.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/react/-/react-19.1.8.tgz#ff8395f2afb764597265ced15f8dddb0720ae1c3"
  integrity sha1-/4OV8q+3ZFlyZc7RX43dsHIK4cM=
  dependencies:
    csstype "^3.0.2"

"@types/react@^18.3.3":
  version "18.3.23"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/react/-/react-18.3.23.tgz#86ae6f6b95a48c418fecdaccc8069e0fbb63696a"
  integrity sha1-hq5va5WkjEGP7NrMyAaeD7tjaWo=
  dependencies:
    "@types/prop-types" "*"
    csstype "^3.0.2"

"@types/recharts@^2.0.1":
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/recharts/-/recharts-2.0.1.tgz#8ca180f515daa6150a95fef7635ab28190f597bc"
  integrity sha1-jKGA9RXaphUKlf73Y1qygZD1l7w=
  dependencies:
    recharts "*"

"@types/resolve@^1.20.2":
  version "1.20.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/resolve/-/resolve-1.20.6.tgz#e6e60dad29c2c8c206c026e6dd8d6d1bdda850b8"
  integrity sha1-5uYNrSnCyMIGwCbm3Y1tG92oULg=

"@types/stack-utils@^2.0.0":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/stack-utils/-/stack-utils-2.0.3.tgz#6209321eb2c1712a7e7466422b8cb1fc0d9dd5d8"
  integrity sha1-YgkyHrLBcSp+dGZCK4yx/A2d1dg=

"@types/use-sync-external-store@^0.0.6":
  version "0.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/use-sync-external-store/-/use-sync-external-store-0.0.6.tgz#60be8d21baab8c305132eb9cb912ed497852aadc"
  integrity sha1-YL6NIbqrjDBRMuucuRLtSXhSqtw=

"@types/uuid@^9.0.1":
  version "9.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/uuid/-/uuid-9.0.8.tgz#7545ba4fc3c003d6c756f651f3bf163d8f0f29ba"
  integrity sha1-dUW6T8PAA9bHVvZR878WPY8PKbo=

"@types/yargs-parser@*":
  version "21.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/yargs-parser/-/yargs-parser-21.0.3.tgz#815e30b786d2e8f0dcd85fd5bcf5e1a04d008f15"
  integrity sha1-gV4wt4bS6PDc2F/VvPXhoE0AjxU=

"@types/yargs@^16.0.0":
  version "16.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/yargs/-/yargs-16.0.9.tgz#ba506215e45f7707e6cbcaf386981155b7ab956e"
  integrity sha1-ulBiFeRfdwfmy8rzhpgRVberlW4=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yargs@^17.0.8":
  version "17.0.33"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/yargs/-/yargs-17.0.33.tgz#8c32303da83eec050a84b3c7ae7b9f922d13e32d"
  integrity sha1-jDIwPag+7AUKhLPHrnufki0T4y0=
  dependencies:
    "@types/yargs-parser" "*"

"@types/yoga-layout@1.9.2":
  version "1.9.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@types/yoga-layout/-/yoga-layout-1.9.2.tgz#efaf9e991a7390dc081a0b679185979a83a9639a"
  integrity sha1-76+emRpzkNwIGgtnkYWXmoOpY5o=

"@typescript-eslint/eslint-plugin@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/eslint-plugin/-/eslint-plugin-8.34.0.tgz#96c9f818782fe24cd5883a5d517ca1826d3fa9c2"
  integrity sha1-lsn4GHgv4kzViDpdUXyhgm0/qcI=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.34.0"
    "@typescript-eslint/type-utils" "8.34.0"
    "@typescript-eslint/utils" "8.34.0"
    "@typescript-eslint/visitor-keys" "8.34.0"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/parser/-/parser-8.34.0.tgz#703270426ac529304ae6988482f487c856d9c13f"
  integrity sha1-cDJwQmrFKTBK5piEgvSHyFbZwT8=
  dependencies:
    "@typescript-eslint/scope-manager" "8.34.0"
    "@typescript-eslint/types" "8.34.0"
    "@typescript-eslint/typescript-estree" "8.34.0"
    "@typescript-eslint/visitor-keys" "8.34.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/project-service/-/project-service-8.34.0.tgz#449119b72fe9fae185013a6bdbaf1ffbfee6bcaf"
  integrity sha1-RJEZty/p+uGFATpr268f+/7mvK8=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.34.0"
    "@typescript-eslint/types" "^8.34.0"
    debug "^4.3.4"

"@typescript-eslint/project-service@8.34.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/project-service/-/project-service-8.34.1.tgz#20501f8b87202c45f5e70a5b24dcdcb8fe12d460"
  integrity sha1-IFAfi4cgLEX15wpbJNzcuP4S1GA=
  dependencies:
    "@typescript-eslint/tsconfig-utils" "^8.34.1"
    "@typescript-eslint/types" "^8.34.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.34.0.tgz#9fedaec02370cf79c018a656ab402eb00dc69e67"
  integrity sha1-n+2uwCNwz3nAGKZWq0AusA3Gnmc=
  dependencies:
    "@typescript-eslint/types" "8.34.0"
    "@typescript-eslint/visitor-keys" "8.34.0"

"@typescript-eslint/scope-manager@8.34.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/scope-manager/-/scope-manager-8.34.1.tgz#727ea43441f4d23d5c73d34195427d85042e5117"
  integrity sha1-cn6kNEH00j1cc9NBlUJ9hQQuURc=
  dependencies:
    "@typescript-eslint/types" "8.34.1"
    "@typescript-eslint/visitor-keys" "8.34.1"

"@typescript-eslint/tsconfig-utils@8.34.0", "@typescript-eslint/tsconfig-utils@^8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.0.tgz#97d0a24e89a355e9308cebc8e23f255669bf0979"
  integrity sha1-l9CiTomjVekwjOvI4j8lVmm/CXk=

"@typescript-eslint/tsconfig-utils@8.34.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.34.1.tgz#d6abb1b1e9f1f1c83ac92051c8fbf2dbc4dc9f5e"
  integrity sha1-1quxsenx8cg6ySBRyPvy28Tcn14=

"@typescript-eslint/tsconfig-utils@^8.34.1":
  version "8.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/tsconfig-utils/-/tsconfig-utils-8.35.0.tgz#6e05aeb999999e31d562ceb4fe144f3cbfbd670e"
  integrity sha1-bgWuuZmZnjHVYs60/hRPPL+9Zw4=

"@typescript-eslint/type-utils@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/type-utils/-/type-utils-8.34.0.tgz#03e7eb3776129dfd751ba1cac0c6ea4b0fab5ec6"
  integrity sha1-A+frN3YSnf11G6HKwMbqSw+rXsY=
  dependencies:
    "@typescript-eslint/typescript-estree" "8.34.0"
    "@typescript-eslint/utils" "8.34.0"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/types/-/types-8.34.0.tgz#18000f205c59c9aff7f371fc5426b764cf2890fb"
  integrity sha1-GAAPIFxZya/383H8VCa3ZM8okPs=

"@typescript-eslint/types@8.34.1", "@typescript-eslint/types@^8.34.0":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/types/-/types-8.34.1.tgz#565a46a251580dae674dac5aafa8eb14b8322a35"
  integrity sha1-VlpGolFYDa5nTaxar6jrFLgyKjU=

"@typescript-eslint/types@^8.34.1":
  version "8.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/types/-/types-8.35.0.tgz#e60d062907930e30008d796de5c4170f02618a93"
  integrity sha1-5g0GKQeTDjAAjXlt5cQXDwJhipM=

"@typescript-eslint/typescript-estree@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.34.0.tgz#c9f3feec511339ef64e9e4884516c3e558f1b048"
  integrity sha1-yfP+7FETOe9k6eSIRRbD5VjxsEg=
  dependencies:
    "@typescript-eslint/project-service" "8.34.0"
    "@typescript-eslint/tsconfig-utils" "8.34.0"
    "@typescript-eslint/types" "8.34.0"
    "@typescript-eslint/visitor-keys" "8.34.0"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/typescript-estree@8.34.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/typescript-estree/-/typescript-estree-8.34.1.tgz#befdb042a6bc44fdad27429b2d3b679c80daad71"
  integrity sha1-vv2wQqa8RP2tJ0KbLTtnnIDarXE=
  dependencies:
    "@typescript-eslint/project-service" "8.34.1"
    "@typescript-eslint/tsconfig-utils" "8.34.1"
    "@typescript-eslint/types" "8.34.1"
    "@typescript-eslint/visitor-keys" "8.34.1"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/utils/-/utils-8.34.0.tgz#7844beebc1153b4d3ec34135c2da53a91e076f8d"
  integrity sha1-eES+68EVO00+w0E1wtpTqR4Hb40=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.34.0"
    "@typescript-eslint/types" "8.34.0"
    "@typescript-eslint/typescript-estree" "8.34.0"

"@typescript-eslint/utils@^8.8.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/utils/-/utils-8.34.1.tgz#f98c9b0c5cae407e34f5131cac0f3a74347a398e"
  integrity sha1-+YybDFyuQH409RMcrA86dDR6OY4=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.34.1"
    "@typescript-eslint/types" "8.34.1"
    "@typescript-eslint/typescript-estree" "8.34.1"

"@typescript-eslint/visitor-keys@8.34.0":
  version "8.34.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.34.0.tgz#c7a149407be31d755dba71980617d638a40ac099"
  integrity sha1-x6FJQHvjHXVdunGYBhfWOKQKwJk=
  dependencies:
    "@typescript-eslint/types" "8.34.0"
    eslint-visitor-keys "^4.2.0"

"@typescript-eslint/visitor-keys@8.34.1":
  version "8.34.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@typescript-eslint/visitor-keys/-/visitor-keys-8.34.1.tgz#28a1987ea3542ccafb92aa792726a304b39531cf"
  integrity sha1-KKGYfqNULMr7kqp5JyajBLOVMc8=
  dependencies:
    "@typescript-eslint/types" "8.34.1"
    eslint-visitor-keys "^4.2.1"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@ungap/structured-clone/-/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

"@vitejs/plugin-react-swc@^3.5.0":
  version "3.10.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitejs/plugin-react-swc/-/plugin-react-swc-3.10.2.tgz#77fc039c049b02eb2deeafb3686d9fa07bf7585a"
  integrity sha1-d/wDnASbAust7q+zaG2foHv3WFo=
  dependencies:
    "@rolldown/pluginutils" "1.0.0-beta.11"
    "@swc/core" "^1.11.31"

"@vitejs/plugin-react@4.5.0":
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitejs/plugin-react/-/plugin-react-4.5.0.tgz#ef2bad6be3031af2b2105b7ab2754f710e890a32"
  integrity sha1-7yuta+MDGvKyEFt6snVPcQ6JCjI=
  dependencies:
    "@babel/core" "^7.26.10"
    "@babel/plugin-transform-react-jsx-self" "^7.25.9"
    "@babel/plugin-transform-react-jsx-source" "^7.25.9"
    "@rolldown/pluginutils" "1.0.0-beta.9"
    "@types/babel__core" "^7.20.5"
    react-refresh "^0.17.0"

"@vitest/coverage-v8@^3.0.9":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/coverage-v8/-/coverage-v8-3.2.3.tgz#866f6d9b7d394dc614f3f2186dbe46b1bc940813"
  integrity sha1-hm9tm305TcYU8/IYbb5GsbyUCBM=
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    "@bcoe/v8-coverage" "^1.0.2"
    ast-v8-to-istanbul "^0.3.3"
    debug "^4.4.1"
    istanbul-lib-coverage "^3.2.2"
    istanbul-lib-report "^3.0.1"
    istanbul-lib-source-maps "^5.0.6"
    istanbul-reports "^3.1.7"
    magic-string "^0.30.17"
    magicast "^0.3.5"
    std-env "^3.9.0"
    test-exclude "^7.0.1"
    tinyrainbow "^2.0.0"

"@vitest/expect@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/expect/-/expect-2.0.5.tgz#f3745a6a2c18acbea4d39f5935e913f40d26fa86"
  integrity sha1-83RaaiwYrL6k059ZNekT9A0m+oY=
  dependencies:
    "@vitest/spy" "2.0.5"
    "@vitest/utils" "2.0.5"
    chai "^5.1.1"
    tinyrainbow "^1.2.0"

"@vitest/expect@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/expect/-/expect-3.2.3.tgz#45be98d6036c6dedbbbc51abdeca3bbd1f12450d"
  integrity sha1-Rb6Y1gNsbe27vFGr3so7vR8SRQ0=
  dependencies:
    "@types/chai" "^5.2.2"
    "@vitest/spy" "3.2.3"
    "@vitest/utils" "3.2.3"
    chai "^5.2.0"
    tinyrainbow "^2.0.0"

"@vitest/mocker@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/mocker/-/mocker-3.2.3.tgz#95d8371182d0e9d1dee36bd3d698149e94fbe78a"
  integrity sha1-ldg3EYLQ6dHe42vT1pgUnpT754o=
  dependencies:
    "@vitest/spy" "3.2.3"
    estree-walker "^3.0.3"
    magic-string "^0.30.17"

"@vitest/pretty-format@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/pretty-format/-/pretty-format-2.0.5.tgz#91d2e6d3a7235c742e1a6cc50e7786e2f2979b1e"
  integrity sha1-kdLm06cjXHQuGmzFDneG4vKXmx4=
  dependencies:
    tinyrainbow "^1.2.0"

"@vitest/pretty-format@2.1.9":
  version "2.1.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/pretty-format/-/pretty-format-2.1.9.tgz#434ff2f7611689f9ce70cd7d567eceb883653fdf"
  integrity sha1-Q0/y92EWifnOcM19Vn7OuINlP98=
  dependencies:
    tinyrainbow "^1.2.0"

"@vitest/pretty-format@3.2.3", "@vitest/pretty-format@^3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/pretty-format/-/pretty-format-3.2.3.tgz#ddd30f689fdd8191dbfd0cce8ae769e5de6b7f23"
  integrity sha1-3dMPaJ/dgZHb/QzOiudp5d5rfyM=
  dependencies:
    tinyrainbow "^2.0.0"

"@vitest/runner@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/runner/-/runner-3.2.3.tgz#e45318d833c8bf8b9f292a700fc06a011f70d542"
  integrity sha1-5FMY2DPIv4ufKSpwD8BqAR9w1UI=
  dependencies:
    "@vitest/utils" "3.2.3"
    pathe "^2.0.3"
    strip-literal "^3.0.0"

"@vitest/snapshot@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/snapshot/-/snapshot-3.2.3.tgz#786dc1939174e1ac6b674d6fd3259bd4ea35a804"
  integrity sha1-eG3Bk5F04axrZ01v0yWb1Oo1qAQ=
  dependencies:
    "@vitest/pretty-format" "3.2.3"
    magic-string "^0.30.17"
    pathe "^2.0.3"

"@vitest/spy@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/spy/-/spy-2.0.5.tgz#590fc07df84a78b8e9dd976ec2090920084a2b9f"
  integrity sha1-WQ/AffhKeLjp3ZduwgkJIAhKK58=
  dependencies:
    tinyspy "^3.0.0"

"@vitest/spy@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/spy/-/spy-3.2.3.tgz#c91715ca4db58a1f0dec636d393a76cf9945b695"
  integrity sha1-yRcVyk21ih8N7GNtOTp2z5lFtpU=
  dependencies:
    tinyspy "^4.0.3"

"@vitest/ui@^3.0.9":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/ui/-/ui-3.2.3.tgz#d170137eb7bfe2dce8314d5c3ba58946e068e434"
  integrity sha1-0XATfre/4tzoMU1cO6WJRuBo5DQ=
  dependencies:
    "@vitest/utils" "3.2.3"
    fflate "^0.8.2"
    flatted "^3.3.3"
    pathe "^2.0.3"
    sirv "^3.0.1"
    tinyglobby "^0.2.14"
    tinyrainbow "^2.0.0"

"@vitest/utils@2.0.5":
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/utils/-/utils-2.0.5.tgz#6f8307a4b6bc6ceb9270007f73c67c915944e926"
  integrity sha1-b4MHpLa8bOuScAB/c8Z8kVlE6SY=
  dependencies:
    "@vitest/pretty-format" "2.0.5"
    estree-walker "^3.0.3"
    loupe "^3.1.1"
    tinyrainbow "^1.2.0"

"@vitest/utils@3.2.3":
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/utils/-/utils-3.2.3.tgz#388afbed1fb3c25ca64c5846a9afb904e3d63bf2"
  integrity sha1-OIr77R+zwlymTFhGqa+5BOPWO/I=
  dependencies:
    "@vitest/pretty-format" "3.2.3"
    loupe "^3.1.3"
    tinyrainbow "^2.0.0"

"@vitest/utils@^2.1.1":
  version "2.1.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vitest/utils/-/utils-2.1.9.tgz#4f2486de8a54acf7ecbf2c5c24ad7994a680a6c1"
  integrity sha1-TySG3opUrPfsvyxcJK15lKaApsE=
  dependencies:
    "@vitest/pretty-format" "2.1.9"
    loupe "^3.1.2"
    tinyrainbow "^1.2.0"

"@volar/language-core@2.4.14", "@volar/language-core@~2.4.11":
  version "2.4.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@volar/language-core/-/language-core-2.4.14.tgz#dac7573014d4f3bafb186cb16888ffea5698be71"
  integrity sha1-2sdXMBTU87r7GGyxaIj/6laYvnE=
  dependencies:
    "@volar/source-map" "2.4.14"

"@volar/source-map@2.4.14":
  version "2.4.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@volar/source-map/-/source-map-2.4.14.tgz#cdcecd533c2e767449b2414cc22327d2bda7ef95"
  integrity sha1-zc7NUzwudnRJskFMwiMn0r2n75U=

"@volar/typescript@^2.4.11":
  version "2.4.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@volar/typescript/-/typescript-2.4.14.tgz#b99a1025dd6a8b751e96627ebcb0739ceed0e5f1"
  integrity sha1-uZoQJd1qi3UelmJ+vLBznO7Q5fE=
  dependencies:
    "@volar/language-core" "2.4.14"
    path-browserify "^1.0.1"
    vscode-uri "^3.0.8"

"@vue/compiler-core@3.5.16":
  version "3.5.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vue/compiler-core/-/compiler-core-3.5.16.tgz#2f95f4f17c16c09c57bbf64399075b921506630b"
  integrity sha1-L5X08XwWwJxXu/ZDmQdbkhUGYws=
  dependencies:
    "@babel/parser" "^7.27.2"
    "@vue/shared" "3.5.16"
    entities "^4.5.0"
    estree-walker "^2.0.2"
    source-map-js "^1.2.1"

"@vue/compiler-dom@^3.5.0":
  version "3.5.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vue/compiler-dom/-/compiler-dom-3.5.16.tgz#151d8390252975c0b1a773029220fdfcfaa2d743"
  integrity sha1-FR2DkCUpdcCxp3MCkiD9/Pqi10M=
  dependencies:
    "@vue/compiler-core" "3.5.16"
    "@vue/shared" "3.5.16"

"@vue/compiler-vue2@^2.7.16":
  version "2.7.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vue/compiler-vue2/-/compiler-vue2-2.7.16.tgz#2ba837cbd3f1b33c2bc865fbe1a3b53fb611e249"
  integrity sha1-K6g3y9PxszwryGX74aO1P7YR4kk=
  dependencies:
    de-indent "^1.0.2"
    he "^1.2.0"

"@vue/language-core@2.2.0":
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vue/language-core/-/language-core-2.2.0.tgz#e48c54584f889f78b120ce10a050dfb316c7fcdf"
  integrity sha1-5IxUWE+In3ixIM4QoFDfsxbH/N8=
  dependencies:
    "@volar/language-core" "~2.4.11"
    "@vue/compiler-dom" "^3.5.0"
    "@vue/compiler-vue2" "^2.7.16"
    "@vue/shared" "^3.5.0"
    alien-signals "^0.4.9"
    minimatch "^9.0.3"
    muggle-string "^0.4.1"
    path-browserify "^1.0.1"

"@vue/shared@3.5.16", "@vue/shared@^3.5.0":
  version "3.5.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/@vue/shared/-/shared-3.5.16.tgz#d5ea7671182742192938a4b4cbf86ef12bef7418"
  integrity sha1-1ep2cRgnQhkpOKS0y/hu8SvvdBg=

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/acorn-jsx/-/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn-walk@^8.1.1:
  version "8.3.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/acorn-walk/-/acorn-walk-8.3.4.tgz#794dd169c3977edf4ba4ea47583587c5866236b7"
  integrity sha1-eU3RacOXft9LpOpHWDWHxYZiNrc=
  dependencies:
    acorn "^8.11.0"

acorn@^8.11.0, acorn@^8.14.0, acorn@^8.4.1, acorn@^8.9.0:
  version "8.15.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/acorn/-/acorn-8.15.0.tgz#a360898bc415edaac46c8241f6383975b930b816"
  integrity sha1-o2CJi8QV7arEbIJB9jg5dbkwuBY=

add-px-to-style@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/add-px-to-style/-/add-px-to-style-1.0.0.tgz#d0c135441fa8014a8137904531096f67f28f263a"
  integrity sha1-0ME1RB+oAUqBN5BFMQlvZ/KPJjo=

aggregate-error@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/aggregate-error/-/aggregate-error-3.1.0.tgz#92670ff50f5359bdb7a3e0d40d0ec30c5737687a"
  integrity sha1-kmcP9Q9TWb23o+DUDQ7DDFc3aHo=
  dependencies:
    clean-stack "^2.0.0"
    indent-string "^4.0.0"

ajv-draft-04@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv-draft-04/-/ajv-draft-04-1.0.0.tgz#3b64761b268ba0b9e668f0b41ba53fce0ad77fc8"
  integrity sha1-O2R2GyaLoLnmaPC0G6U/zgrXf8g=

ajv-formats@~3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv-formats/-/ajv-formats-3.0.1.tgz#3d5dc762bca17679c3c2ea7e90ad6b7532309578"
  integrity sha1-PV3HYryhdnnDwup+kK1rdTIwlXg=
  dependencies:
    ajv "^8.0.0"

ajv@^6.12.4:
  version "6.12.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv/-/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.0:
  version "8.17.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv/-/ajv-8.17.1.tgz#37d9a5c776af6bc92d7f4f9510eba4c0a60d11a6"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ajv@~8.12.0:
  version "8.12.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv/-/ajv-8.12.0.tgz#d1a0527323e22f53562c567c00991577dfbe19d1"
  integrity sha1-0aBScyPiL1NWLFZ8AJkVd9++GdE=
  dependencies:
    fast-deep-equal "^3.1.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.2.2"

ajv@~8.13.0:
  version "8.13.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ajv/-/ajv-8.13.0.tgz#a3939eaec9fb80d217ddf0c3376948c023f28c91"
  integrity sha1-o5Oersn7gNIX3fDDN2lIwCPyjJE=
  dependencies:
    fast-deep-equal "^3.1.3"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"
    uri-js "^4.4.1"

alien-signals@^0.4.9:
  version "0.4.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/alien-signals/-/alien-signals-0.4.14.tgz#9ff8f72a272300a51692f54bd9bbbada78fbf539"
  integrity sha1-n/j3KicjAKUWkvVL2bu62nj79Tk=

ansi-colors@^4.1.1, ansi-colors@^4.1.3:
  version "4.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-colors/-/ansi-colors-4.1.3.tgz#37611340eb2243e70cc604cad35d63270d48781b"
  integrity sha1-N2ETQOsiQ+cMxgTK011jJw1IeBs=

ansi-escapes@^4.2.1, ansi-escapes@^4.3.0:
  version "4.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-escapes/-/ansi-escapes-4.3.2.tgz#6b2291d1db7d98b6521d5f1efa42d0f3a9feb65e"
  integrity sha1-ayKR0dt9mLZSHV8e+kLQ86n+tl4=
  dependencies:
    type-fest "^0.21.3"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-regex/-/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-regex@^6.0.1:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-regex/-/ansi-regex-6.1.0.tgz#95ec409c69619d6cb1b8b34f14b660ef28ebd654"
  integrity sha1-lexAnGlhnWyxuLNPFLZg7yjr1lQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-styles/-/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

ansi-styles@^5.0.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-styles/-/ansi-styles-5.2.0.tgz#07449690ad45777d1924ac2abb2fc8895dba836b"
  integrity sha1-B0SWkK1Fd30ZJKwquy/IiV26g2s=

ansi-styles@^6.1.0:
  version "6.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ansi-styles/-/ansi-styles-6.2.1.tgz#0e62320cf99c21afff3b3012192546aacbfb05c5"
  integrity sha1-DmIyDPmcIa//OzASGSVGqsv7BcU=

any-promise@^1.0.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/any-promise/-/any-promise-1.3.0.tgz#abc6afeedcea52e809cdc0376aed3ce39635d17f"
  integrity sha1-q8av7tzqUugJzcA3au0845Y10X8=

anymatch@^3.0.3, anymatch@~3.1.2:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/anymatch/-/anymatch-3.1.3.tgz#790c58b19ba1720a84205b57c618d5ad8524973e"
  integrity sha1-eQxYsZuhcgqEIFtXxhjVrYUklz4=
  dependencies:
    normalize-path "^3.0.0"
    picomatch "^2.0.4"

arg@^4.1.0:
  version "4.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/arg/-/arg-4.1.3.tgz#269fc7ad5b8e42cb63c896d5666017261c144089"
  integrity sha1-Jp/HrVuOQstjyJbVZmAXJhwUQIk=

arg@^5.0.2:
  version "5.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/arg/-/arg-5.0.2.tgz#c81433cc427c92c4dcf4865142dbca6f15acd59c"
  integrity sha1-yBQzzEJ8ksTc9IZRQtvKbxWs1Zw=

argparse@^1.0.7, argparse@~1.0.9:
  version "1.0.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/argparse/-/argparse-1.0.10.tgz#bcd6791ea5ae09725e17e5ad988134cd40b3d911"
  integrity sha1-vNZ5HqWuCXJeF+WtmIE0zUCz2RE=
  dependencies:
    sprintf-js "~1.0.2"

argparse@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/argparse/-/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

aria-query@5.3.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/aria-query/-/aria-query-5.3.0.tgz#650c569e41ad90b51b3d7df5e5eed1c7549c103e"
  integrity sha1-ZQxWnkGtkLUbPX315e7Rx1ScED4=
  dependencies:
    dequal "^2.0.3"

aria-query@^5.0.0:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/aria-query/-/aria-query-5.3.2.tgz#93f81a43480e33a338f19163a3d10a50c01dcd59"
  integrity sha1-k/gaQ0gOM6M48ZFjo9EKUMAdzVk=

array-buffer-byte-length@^1.0.1, array-buffer-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array-buffer-byte-length/-/array-buffer-byte-length-1.0.2.tgz#384d12a37295aec3769ab022ad323a18a51ccf8b"
  integrity sha1-OE0So3KVrsN2mrAirTI6GKUcz4s=
  dependencies:
    call-bound "^1.0.3"
    is-array-buffer "^3.0.5"

array-includes@^3.1.4, array-includes@^3.1.6:
  version "3.1.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array-includes/-/array-includes-3.1.9.tgz#1f0ccaa08e90cdbc3eb433210f903ad0f17c3f3a"
  integrity sha1-HwzKoI6Qzbw+tDMhD5A60PF8Pzo=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-abstract "^1.24.0"
    es-object-atoms "^1.1.1"
    get-intrinsic "^1.3.0"
    is-string "^1.1.1"
    math-intrinsics "^1.1.0"

array-parallel@~0.1.3:
  version "0.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array-parallel/-/array-parallel-0.1.3.tgz#8f785308926ed5aa478c47e64d1b334b6c0c947d"
  integrity sha1-j3hTCJJu1apHjEfmTRszS2wMlH0=

array-series@~0.1.5:
  version "0.1.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array-series/-/array-series-0.1.5.tgz#df5d37bfc5c2ef0755e2aa4f92feae7d4b5a972f"
  integrity sha1-3103v8XC7wdV4qpPkv6ufUtaly8=

array-union@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array-union/-/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

array.prototype.flat@^1.2.5, array.prototype.flat@^1.3.1:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array.prototype.flat/-/array.prototype.flat-1.3.3.tgz#534aaf9e6e8dd79fb6b9a9917f839ef1ec63afe5"
  integrity sha1-U0qvnm6N15+2uamRf4Oe8exjr+U=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

array.prototype.flatmap@^1.2.5:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/array.prototype.flatmap/-/array.prototype.flatmap-1.3.3.tgz#712cc792ae70370ae40586264629e33aab5dd38b"
  integrity sha1-cSzHkq5wNwrkBYYmRinjOqtd04s=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-shim-unscopables "^1.0.2"

arraybuffer.prototype.slice@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/arraybuffer.prototype.slice/-/arraybuffer.prototype.slice-1.0.4.tgz#9d760d84dbdd06d0cbf92c8849615a1a7ab3183c"
  integrity sha1-nXYNhNvdBtDL+SyISWFaGnqzGDw=
  dependencies:
    array-buffer-byte-length "^1.0.1"
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    is-array-buffer "^3.0.4"

assertion-error@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/assertion-error/-/assertion-error-2.0.1.tgz#f641a196b335690b1070bf00b6e7593fec190bf7"
  integrity sha1-9kGhlrM1aQsQcL8AtudZP+wZC/c=

ast-types@^0.16.1:
  version "0.16.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ast-types/-/ast-types-0.16.1.tgz#7a9da1617c9081bc121faafe91711b4c8bb81da2"
  integrity sha1-ep2hYXyQgbwSH6r+kXEbTIu4HaI=
  dependencies:
    tslib "^2.0.1"

ast-v8-to-istanbul@^0.3.3:
  version "0.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ast-v8-to-istanbul/-/ast-v8-to-istanbul-0.3.3.tgz#697101c116cff6b51c0e668ba6352e7e41fe8dd5"
  integrity sha1-aXEBwRbP9rUcDmaLpjUufkH+jdU=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.25"
    estree-walker "^3.0.3"
    js-tokens "^9.0.1"

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/astral-regex/-/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

async-function@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/async-function/-/async-function-1.0.0.tgz#509c9fca60eaf85034c6829838188e4e4c8ffb2b"
  integrity sha1-UJyfymDq+FA0xoKYOBiOTkyP+ys=

async@^3.2.0:
  version "3.2.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/async/-/async-3.2.6.tgz#1b0728e14929d51b85b449b7f06e27c1145e38ce"
  integrity sha1-Gwco4Ukp1RuFtEm38G4nwRReOM4=

at-least-node@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/at-least-node/-/at-least-node-1.0.0.tgz#602cd4b46e844ad4effc92a8011a3c46e0238dc2"
  integrity sha1-YCzUtG6EStTv/JKoARo8RuAjjcI=

auto-bind@4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/auto-bind/-/auto-bind-4.0.0.tgz#e3589fc6c2da8f7ca43ba9f84fa52a744fc997fb"
  integrity sha1-41ifxsLaj3ykO6n4T6UqdE/Jl/s=

autoprefixer@^10.4.19:
  version "10.4.21"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/autoprefixer/-/autoprefixer-10.4.21.tgz#77189468e7a8ad1d9a37fbc08efc9f480cf0a95d"
  integrity sha1-dxiUaOeorR2aN/vAjvyfSAzwqV0=
  dependencies:
    browserslist "^4.24.4"
    caniuse-lite "^1.0.30001702"
    fraction.js "^4.3.7"
    normalize-range "^0.1.2"
    picocolors "^1.1.1"
    postcss-value-parser "^4.2.0"

available-typed-arrays@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/available-typed-arrays/-/available-typed-arrays-1.0.7.tgz#a5cc375d6a03c2efc87a553f3e0b1522def14846"
  integrity sha1-pcw3XWoDwu/IelU/PgsVIt7xSEY=
  dependencies:
    possible-typed-array-names "^1.0.0"

aws-sdk@^2.840.0:
  version "2.1692.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/aws-sdk/-/aws-sdk-2.1692.0.tgz#9dac5f7bfcc5ab45825cc8591b12753aa7d2902c"
  integrity sha1-naxfe/zFq0WCXMhZGxJ1OqfSkCw=
  dependencies:
    buffer "4.9.2"
    events "1.1.1"
    ieee754 "1.1.13"
    jmespath "0.16.0"
    querystring "0.2.0"
    sax "1.2.1"
    url "0.10.3"
    util "^0.12.4"
    uuid "8.0.0"
    xml2js "0.6.2"

babel-jest@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/babel-jest/-/babel-jest-29.7.0.tgz#f4369919225b684c56085998ac63dbd05be020d5"
  integrity sha1-9DaZGSJbaExWCFmYrGPb0FvgINU=
  dependencies:
    "@jest/transform" "^29.7.0"
    "@types/babel__core" "^7.1.14"
    babel-plugin-istanbul "^6.1.1"
    babel-preset-jest "^29.6.3"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    slash "^3.0.0"

babel-plugin-istanbul@^6.1.1:
  version "6.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz#fa88ec59232fd9b4e36dbbc540a8ec9a9b47da73"
  integrity sha1-+ojsWSMv2bTjbbvFQKjsmptH2nM=
  dependencies:
    "@babel/helper-plugin-utils" "^7.0.0"
    "@istanbuljs/load-nyc-config" "^1.0.0"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-instrument "^5.0.4"
    test-exclude "^6.0.0"

babel-plugin-jest-hoist@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz#aadbe943464182a8922c3c927c3067ff40d24626"
  integrity sha1-qtvpQ0ZBgqiSLDySfDBn/0DSRiY=
  dependencies:
    "@babel/template" "^7.3.3"
    "@babel/types" "^7.3.3"
    "@types/babel__core" "^7.1.14"
    "@types/babel__traverse" "^7.0.6"

babel-preset-current-node-syntax@^1.0.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz#9a929eafece419612ef4ae4f60b1862ebad8ef30"
  integrity sha1-mpKer+zkGWEu9K5PYLGGLrrY7zA=
  dependencies:
    "@babel/plugin-syntax-async-generators" "^7.8.4"
    "@babel/plugin-syntax-bigint" "^7.8.3"
    "@babel/plugin-syntax-class-properties" "^7.12.13"
    "@babel/plugin-syntax-class-static-block" "^7.14.5"
    "@babel/plugin-syntax-import-attributes" "^7.24.7"
    "@babel/plugin-syntax-import-meta" "^7.10.4"
    "@babel/plugin-syntax-json-strings" "^7.8.3"
    "@babel/plugin-syntax-logical-assignment-operators" "^7.10.4"
    "@babel/plugin-syntax-nullish-coalescing-operator" "^7.8.3"
    "@babel/plugin-syntax-numeric-separator" "^7.10.4"
    "@babel/plugin-syntax-object-rest-spread" "^7.8.3"
    "@babel/plugin-syntax-optional-catch-binding" "^7.8.3"
    "@babel/plugin-syntax-optional-chaining" "^7.8.3"
    "@babel/plugin-syntax-private-property-in-object" "^7.14.5"
    "@babel/plugin-syntax-top-level-await" "^7.14.5"

babel-preset-jest@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz#fa05fa510e7d493896d7b0dd2033601c840f171c"
  integrity sha1-+gX6UQ59STiW17DdIDNgHIQPFxw=
  dependencies:
    babel-plugin-jest-hoist "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/balanced-match/-/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

base64-js@^1.0.2:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/base64-js/-/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

better-opn@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/better-opn/-/better-opn-3.0.2.tgz#f96f35deaaf8f34144a4102651babcf00d1d8817"
  integrity sha1-+W813qr480FEpBAmUbq88A0diBc=
  dependencies:
    open "^8.0.4"

better-path-resolve@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/better-path-resolve/-/better-path-resolve-1.0.0.tgz#13a35a1104cdd48a7b74bf8758f96a1ee613f99d"
  integrity sha1-E6NaEQTN1Ip7dL+HWPlqHuYT+Z0=
  dependencies:
    is-windows "^1.0.0"

binary-extensions@^2.0.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/binary-extensions/-/binary-extensions-2.3.0.tgz#f6e14a97858d327252200242d4ccfe522c445522"
  integrity sha1-9uFKl4WNMnJSIAJC1Mz+UixEVSI=

brace-expansion@^1.1.7:
  version "1.1.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/brace-expansion/-/brace-expansion-1.1.12.tgz#ab9b454466e5a8cc3a187beaad580412a9c5b843"
  integrity sha1-q5tFRGblqMw6GHvqrVgEEqnFuEM=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/brace-expansion/-/brace-expansion-2.0.2.tgz#54fc53237a613d854c7bd37463aad17df87214e7"
  integrity sha1-VPxTI3phPYVMe9N0Y6rRffhyFOc=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3, braces@~3.0.2:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/braces/-/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

browser-assert@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/browser-assert/-/browser-assert-1.2.1.tgz#9aaa5a2a8c74685c2ae05bfe46efd606f068c200"
  integrity sha1-mqpaKox0aFwq4Fv+Ru/WBvBowgA=

browserslist-to-esbuild@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/browserslist-to-esbuild/-/browserslist-to-esbuild-2.1.1.tgz#50dc4c55a6889ba22c7b1bd820032f81b822faf0"
  integrity sha1-UNxMVaaIm6IsexvYIAMvgbgi+vA=
  dependencies:
    meow "^13.0.0"

browserslist@^4.22.2, browserslist@^4.23.1, browserslist@^4.24.0, browserslist@^4.24.4:
  version "4.25.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/browserslist/-/browserslist-4.25.0.tgz#986aa9c6d87916885da2b50d8eb577ac8d133b2c"
  integrity sha1-mGqpxth5FohdorUNjrV3rI0TOyw=
  dependencies:
    caniuse-lite "^1.0.30001718"
    electron-to-chromium "^1.5.160"
    node-releases "^2.0.19"
    update-browserslist-db "^1.1.3"

bser@2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/bser/-/bser-2.1.1.tgz#e6787da20ece9d07998533cfd9de6f5c38f4bc05"
  integrity sha1-5nh9og7OnQeZhTPP2d5vXDj0vAU=
  dependencies:
    node-int64 "^0.4.0"

buffer-builder@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/buffer-builder/-/buffer-builder-0.2.0.tgz#3322cd307d8296dab1f604618593b261a3fade8f"
  integrity sha1-MyLNMH2Cltqx9gRhhZOyYaP63o8=

buffer-from@^1.0.0:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/buffer-from/-/buffer-from-1.1.2.tgz#2b146a6fd72e80b4f55d255f35ed59a3a9a41bd5"
  integrity sha1-KxRqb9cugLT1XSVfNe1Zo6mkG9U=

buffer@4.9.2:
  version "4.9.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/buffer/-/buffer-4.9.2.tgz#230ead344002988644841ab0244af8c44bbe3ef8"
  integrity sha1-Iw6tNEACmIZEhBqwJEr4xEu+Pvg=
  dependencies:
    base64-js "^1.0.2"
    ieee754 "^1.1.4"
    isarray "^1.0.0"

builtin-modules@^3.3.0:
  version "3.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/builtin-modules/-/builtin-modules-3.3.0.tgz#cae62812b89801e9656336e46223e030386be7b6"
  integrity sha1-yuYoEriYAellYzbkYiPgMDhr57Y=

cac@^6.7.14:
  version "6.7.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cac/-/cac-6.7.14.tgz#804e1e6f506ee363cb0e3ccbb09cad5dd9870959"
  integrity sha1-gE4eb1Bu42PLDjzLsJytXdmHCVk=

call-bind-apply-helpers@^1.0.0, call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

call-bind@^1.0.7, call-bind@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/call-bind/-/call-bind-1.0.8.tgz#0736a9660f537e3388826f440d5ec45f744eaa4c"
  integrity sha1-BzapZg9TfjOIgm9EDV7EX3ROqkw=
  dependencies:
    call-bind-apply-helpers "^1.0.0"
    es-define-property "^1.0.0"
    get-intrinsic "^1.2.4"
    set-function-length "^1.2.2"

call-bound@^1.0.2, call-bound@^1.0.3, call-bound@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/call-bound/-/call-bound-1.0.4.tgz#238de935d2a2a692928c538c7ccfa91067fd062a"
  integrity sha1-I43pNdKippKSjFOMfM+pEGf9Bio=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    get-intrinsic "^1.3.0"

caller-callsite@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/caller-callsite/-/caller-callsite-4.1.0.tgz#3e33cb1d910e7b09332d59a3503b9af7462f7295"
  integrity sha1-PjPLHZEOewkzLVmjUDua90YvcpU=
  dependencies:
    callsites "^3.1.0"

caller-path@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/caller-path/-/caller-path-3.0.1.tgz#bc932ecec3f943e10c2f8922146e23b132f932e4"
  integrity sha1-vJMuzsP5Q+EML4kiFG4jsTL5MuQ=
  dependencies:
    caller-callsite "^4.1.0"

callsites@^3.0.0, callsites@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/callsites/-/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

camelcase-css@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/camelcase-css/-/camelcase-css-2.0.1.tgz#ee978f6947914cc30c6b44741b6ed1df7f043fd5"
  integrity sha1-7pePaUeRTMMMa0R0G27R338EP9U=

camelcase@^5.3.1:
  version "5.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/camelcase/-/camelcase-5.3.1.tgz#e3c9b31569e106811df242f715725a1f4c494320"
  integrity sha1-48mzFWnhBoEd8kL3FXJaH0xJQyA=

camelcase@^6.2.0:
  version "6.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/camelcase/-/camelcase-6.3.0.tgz#5685b95eb209ac9c0c177467778c9c84df58ba9a"
  integrity sha1-VoW5XrIJrJwMF3Rnd4ychN9Yupo=

caniuse-lite@^1.0.30001702, caniuse-lite@^1.0.30001718:
  version "1.0.30001723"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/caniuse-lite/-/caniuse-lite-1.0.30001723.tgz#c4f3174f02089720736e1887eab345e09bb10944"
  integrity sha1-xPMXTwIIlyBzbhiH6rNF4JuxCUQ=

chai@^5.1.1, chai@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chai/-/chai-5.2.0.tgz#1358ee106763624114addf84ab02697e411c9c05"
  integrity sha1-E1juEGdjYkEUrd+EqwJpfkEcnAU=
  dependencies:
    assertion-error "^2.0.1"
    check-error "^2.1.1"
    deep-eql "^5.0.1"
    loupe "^3.1.0"
    pathval "^2.0.0"

chalk@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chalk/-/chalk-3.0.0.tgz#3f73c2bf526591f574cc492c51e2456349f844e4"
  integrity sha1-P3PCv1JlkfV0zEksUeJFY0n4ROQ=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

chalk@^4.0.0, chalk@^4.1.0, chalk@^4.1.1, chalk@^4.1.2:
  version "4.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chalk/-/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

char-regex@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/char-regex/-/char-regex-1.0.2.tgz#d744358226217f981ed58f479b1d6bcc29545dcf"
  integrity sha1-10Q1giYhf5ge1Y9Hmx1rzClUXc8=

chardet@^0.7.0:
  version "0.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chardet/-/chardet-0.7.0.tgz#90094849f0937f2eedc2425d0d28a9e5f0cbad9e"
  integrity sha1-kAlISfCTfy7twkJdDSip5fDLrZ4=

check-error@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/check-error/-/check-error-2.1.1.tgz#87eb876ae71ee388fa0471fe423f494be1d96ccc"
  integrity sha1-h+uHauce44j6BHH+Qj9JS+HZbMw=

chokidar@^3.5.1, chokidar@^3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chokidar/-/chokidar-3.6.0.tgz#197c6cc669ef2a8dc5e7b4d97ee4e092c3eb0d5b"
  integrity sha1-GXxsxmnvKo3F57TZfuTgksPrDVs=
  dependencies:
    anymatch "~3.1.2"
    braces "~3.0.2"
    glob-parent "~5.1.2"
    is-binary-path "~2.1.0"
    is-glob "~4.0.1"
    normalize-path "~3.0.0"
    readdirp "~3.6.0"
  optionalDependencies:
    fsevents "~2.3.2"

chromatic@^11.15.0:
  version "11.29.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chromatic/-/chromatic-11.29.0.tgz#da556dbd3b043e8c6a3134d1afa3bb4ad7317410"
  integrity sha1-2lVtvTsEPoxqMTTRr6O7StcxdBA=

chrome-launcher@0.15.2:
  version "0.15.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chrome-launcher/-/chrome-launcher-0.15.2.tgz#4e6404e32200095fdce7f6a1e1004f9bd36fa5da"
  integrity sha1-TmQE4yIACV/c5/ah4QBPm9Nvpdo=
  dependencies:
    "@types/node" "*"
    escape-string-regexp "^4.0.0"
    is-wsl "^2.2.0"
    lighthouse-logger "^1.0.0"

chrome-remote-interface@^0.32.1:
  version "0.32.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/chrome-remote-interface/-/chrome-remote-interface-0.32.2.tgz#4c494b9d074997b45d49137232df48a355189278"
  integrity sha1-TElLnQdJl7RdSRNyMt9Io1UYkng=
  dependencies:
    commander "2.11.x"
    ws "^7.2.0"

ci-info@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ci-info/-/ci-info-2.0.0.tgz#67a9e964be31a51e15e5010d58e6f12834002f46"
  integrity sha1-Z6npZL4xpR4V5QENWObxKDQAL0Y=

ci-info@^3.2.0, ci-info@^3.3.0, ci-info@^3.7.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ci-info/-/ci-info-3.9.0.tgz#4279a62028a7b1f262f3473fc9605f5e218c59b4"
  integrity sha1-QnmmICinsfJi80c/yWBfXiGMWbQ=

ci-info@^4.0.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ci-info/-/ci-info-4.2.0.tgz#cbd21386152ebfe1d56f280a3b5feccbd96764c7"
  integrity sha1-y9IThhUuv+HVbygKO1/sy9lnZMc=

cjs-module-lexer@^1.0.0:
  version "1.4.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz#0f79731eb8cfe1ec72acd4066efac9d61991b00d"
  integrity sha1-D3lzHrjP4exyrNQGbvrJ1hmRsA0=

class-variance-authority@0.7.1:
  version "0.7.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/class-variance-authority/-/class-variance-authority-0.7.1.tgz#4008a798a0e4553a781a57ac5177c9fb5d043787"
  integrity sha1-QAinmKDkVTp4GlesUXfJ+10EN4c=
  dependencies:
    clsx "^2.1.1"

classnames@2.2.6:
  version "2.2.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/classnames/-/classnames-2.2.6.tgz#43935bffdd291f326dad0a205309b38d00f650ce"
  integrity sha1-Q5Nb/90pHzJtrQogUwmzjQD2UM4=

clean-regexp@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/clean-regexp/-/clean-regexp-1.0.0.tgz#8df7c7aae51fd36874e8f8d05b9180bc11a3fed7"
  integrity sha1-jffHquUf02h06PjQW5GAvBGj/tc=
  dependencies:
    escape-string-regexp "^1.0.5"

clean-stack@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/clean-stack/-/clean-stack-2.2.0.tgz#ee8472dbb129e727b31e8a10a427dee9dfe4008b"
  integrity sha1-7oRy27Ep5yezHooQpCfe6d/kAIs=

cli-boxes@^2.2.0:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cli-boxes/-/cli-boxes-2.2.1.tgz#ddd5035d25094fce220e9cab40a45840a440318f"
  integrity sha1-3dUDXSUJT84iDpyrQKRYQKRAMY8=

cli-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cli-cursor/-/cli-cursor-3.1.0.tgz#264305a7ae490d1d03bf0c9ba7c925d1753af307"
  integrity sha1-JkMFp65JDR0Dvwybp8kl0XU68wc=
  dependencies:
    restore-cursor "^3.1.0"

cli-truncate@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cli-truncate/-/cli-truncate-2.1.0.tgz#c39e28bf05edcde5be3b98992a22deed5a2b93c7"
  integrity sha1-w54ovwXtzeW+O5iZKiLe7Vork8c=
  dependencies:
    slice-ansi "^3.0.0"
    string-width "^4.2.0"

cliui@^7.0.2:
  version "7.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cliui/-/cliui-7.0.4.tgz#a0265ee655476fc807aea9df3df8df7783808b4f"
  integrity sha1-oCZe5lVHb8gHrqnfPfjfd4OAi08=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.0"
    wrap-ansi "^7.0.0"

cliui@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cliui/-/cliui-8.0.1.tgz#0c04b075db02cbfe60dc8e6cf2f5486b1a3608aa"
  integrity sha1-DASwddsCy/5g3I5s8vVIaxo2CKo=
  dependencies:
    string-width "^4.2.0"
    strip-ansi "^6.0.1"
    wrap-ansi "^7.0.0"

clsx@2.1.1, clsx@^2.0.0, clsx@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/clsx/-/clsx-2.1.1.tgz#eed397c9fd8bd882bfb18deab7102049a2f32999"
  integrity sha1-7tOXyf2L2IK/sY3qtxAgSaLzKZk=

co@^4.6.0:
  version "4.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/co/-/co-4.6.0.tgz#6ea6bdf3d853ae54ccb8e47bfa0bf3f9031fb184"
  integrity sha1-bqa989hTrlTMuOR7+gvz+QMfsYQ=

code-excerpt@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/code-excerpt/-/code-excerpt-3.0.0.tgz#fcfb6748c03dba8431c19f5474747fad3f250f10"
  integrity sha1-/PtnSMA9uoQxwZ9UdHR/rT8lDxA=
  dependencies:
    convert-to-spaces "^1.0.1"

collect-v8-coverage@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz#c0b29bcd33bcd0779a1344c2136051e6afd3d9e9"
  integrity sha1-wLKbzTO80HeaE0TCE2BR5q/T2ek=

color-convert@^2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/color-convert/-/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-convert@~0.5.0:
  version "0.5.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/color-convert/-/color-convert-0.5.3.tgz#bdb6c69ce660fadffe0b0007cc447e1b9f7282bd"
  integrity sha1-vbbGnOZg+t/+CwAHzER+G59ygr0=

color-diff@^1.1.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/color-diff/-/color-diff-1.4.0.tgz#f63c7020c4819b3f7bc379e61d8d19eabf6e1b8a"
  integrity sha1-9jxwIMSBmz97w3nmHY0Z6r9uG4o=

color-name@~1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/color-name/-/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colorjs.io@^0.5.0:
  version "0.5.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/colorjs.io/-/colorjs.io-0.5.2.tgz#63b20139b007591ebc3359932bef84628eb3fcef"
  integrity sha1-Y7IBObAHWR68M1mTK++EYo6z/O8=

commander@2.11.x:
  version "2.11.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/commander/-/commander-2.11.0.tgz#157152fd1e7a6c8d98a5b715cf376df928004563"
  integrity sha1-FXFS/R56bI2YpbcVzzdt+SgARWM=

commander@^4.0.0:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/commander/-/commander-4.1.1.tgz#9fd602bd936294e9e9ef46a3f4d6964044b18068"
  integrity sha1-n9YCvZNilOnp70aj9NaWQESxgGg=

commander@^8.0.0:
  version "8.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/commander/-/commander-8.3.0.tgz#4837ea1b2da67b9c616a67afbb0fafee567bca66"
  integrity sha1-SDfqGy2me5xhamevuw+v7lZ7ymY=

commander@^9.3.0:
  version "9.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/commander/-/commander-9.5.0.tgz#bc08d1eb5cedf7ccb797a96199d41c7bc3e60d30"
  integrity sha1-vAjR61zt98y3l6lhmdQce8PmDTA=

commondir@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/commondir/-/commondir-1.0.1.tgz#ddd800da0c66127393cca5950ea968a3aaf1253b"
  integrity sha1-3dgA2gxmEnOTzKWVDqloo6rxJTs=

compare-versions@^6.1.1:
  version "6.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/compare-versions/-/compare-versions-6.1.1.tgz#7af3cc1099ba37d244b3145a9af5201b629148a9"
  integrity sha1-evPMEJm6N9JEsxRamvUgG2KRSKk=

concat-map@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/concat-map/-/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

concat-stream@^1.6.2:
  version "1.6.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/concat-stream/-/concat-stream-1.6.2.tgz#904bdf194cd3122fc675c77fc4ac3d4ff0fd1a34"
  integrity sha1-kEvfGUzTEi/Gdcd/xKw9T/D9GjQ=
  dependencies:
    buffer-from "^1.0.0"
    inherits "^2.0.3"
    readable-stream "^2.2.2"
    typedarray "^0.0.6"

confbox@^0.1.8:
  version "0.1.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/confbox/-/confbox-0.1.8.tgz#820d73d3b3c82d9bd910652c5d4d599ef8ff8b06"
  integrity sha1-gg1z07PILZvZEGUsXU1Znvj/iwY=

confbox@^0.2.1:
  version "0.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/confbox/-/confbox-0.2.2.tgz#8652f53961c74d9e081784beed78555974a9c110"
  integrity sha1-hlL1OWHHTZ4IF4S+7XhVWXSpwRA=

convert-source-map@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/convert-source-map/-/convert-source-map-2.0.0.tgz#4b560f649fc4e918dd0ab75cf4961e8bc882d82a"
  integrity sha1-S1YPZJ/E6RjdCrdc9JYei8iC2Co=

convert-to-spaces@^1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/convert-to-spaces/-/convert-to-spaces-1.0.2.tgz#7e3e48bbe6d997b1417ddca2868204b4d3d85715"
  integrity sha1-fj5Iu+bZl7FBfdyihoIEtNPYVxU=

copyfiles@2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/copyfiles/-/copyfiles-2.4.1.tgz#d2dcff60aaad1015f09d0b66e7f0f1c5cd3c5da5"
  integrity sha1-0tz/YKqtEBXwnQtm5/Dxxc08XaU=
  dependencies:
    glob "^7.0.5"
    minimatch "^3.0.3"
    mkdirp "^1.0.4"
    noms "0.0.0"
    through2 "^2.0.1"
    untildify "^4.0.0"
    yargs "^16.1.0"

core-util-is@~1.0.0:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/core-util-is/-/core-util-is-1.0.3.tgz#a6042d3634c2b27e9328f837b965fac83808db85"
  integrity sha1-pgQtNjTCsn6TKPg3uWX6yDgI24U=

cosmiconfig@^7.0.0:
  version "7.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cosmiconfig/-/cosmiconfig-7.1.0.tgz#1443b9afa596b670082ea46cbd8f6a62b84635f6"
  integrity sha1-FEO5r6WWtnAILqRsvY9qYrhGNfY=
  dependencies:
    "@types/parse-json" "^4.0.0"
    import-fresh "^3.2.1"
    parse-json "^5.0.0"
    path-type "^4.0.0"
    yaml "^1.10.0"

cosmiconfig@^8.1.3:
  version "8.3.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cosmiconfig/-/cosmiconfig-8.3.6.tgz#060a2b871d66dba6c8538ea1118ba1ac16f5fae3"
  integrity sha1-Bgorhx1m26bIU46hEYuhrBb1+uM=
  dependencies:
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"
    path-type "^4.0.0"

create-jest@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/create-jest/-/create-jest-29.7.0.tgz#a355c5b3cb1e1af02ba177fe7afd7feee49a5320"
  integrity sha1-o1XFs8seGvAroXf+ev1/7uSaUyA=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    exit "^0.1.2"
    graceful-fs "^4.2.9"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    prompts "^2.0.1"

create-require@^1.1.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/create-require/-/create-require-1.1.1.tgz#c1d7e8f1e5f6cfc9ff65f9cd352d37348756c333"
  integrity sha1-wdfo8eX2z8n/ZfnNNS03NIdWwzM=

cross-spawn@^7.0.2, cross-spawn@^7.0.3, cross-spawn@^7.0.5, cross-spawn@^7.0.6:
  version "7.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cross-spawn/-/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

crypto-random-string@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/crypto-random-string/-/crypto-random-string-2.0.0.tgz#ef2a7a966ec11083388369baa02ebead229b30d5"
  integrity sha1-7yp6lm7BEIM4g2m6oC6+rSKbMNU=

css-blank-pseudo@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/css-blank-pseudo/-/css-blank-pseudo-6.0.2.tgz#50db072d4fb5b40c2df9ffe5ca5fbb9b19c77fc8"
  integrity sha1-UNsHLU+1tAwt+f/lyl+7mxnHf8g=
  dependencies:
    postcss-selector-parser "^6.0.13"

css-has-pseudo@^6.0.5:
  version "6.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/css-has-pseudo/-/css-has-pseudo-6.0.5.tgz#372e7293ef9bb901ec0bdce85a6fc1365012fa2c"
  integrity sha1-Ny5yk++buQHsC9zoWm/BNlAS+iw=
  dependencies:
    "@csstools/selector-specificity" "^3.1.1"
    postcss-selector-parser "^6.0.13"
    postcss-value-parser "^4.2.0"

css-prefers-color-scheme@^9.0.1:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/css-prefers-color-scheme/-/css-prefers-color-scheme-9.0.1.tgz#30fcb94cc38b639b66fb99e1882ffd97f741feaa"
  integrity sha1-MPy5TMOLY5tm+5nhiC/9l/dB/qo=

css.escape@^1.5.1:
  version "1.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/css.escape/-/css.escape-1.5.1.tgz#42e27d4fa04ae32f931a4b4d4191fa9cddee97cb"
  integrity sha1-QuJ9T6BK4y+TGktNQZH6nN3ul8s=

cssdb@^8.1.0:
  version "8.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cssdb/-/cssdb-8.3.0.tgz#940becad497b8509ad822a28fb0cfe54c969ccfe"
  integrity sha1-lAvsrUl7hQmtgioo+wz+VMlpzP4=

cssesc@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/cssesc/-/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@2.6.10:
  version "2.6.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/csstype/-/csstype-2.6.10.tgz#e63af50e66d7c266edb6b32909cfd0aabe03928b"
  integrity sha1-5jr1DmbXwmbttrMpCc/Qqr4Dkos=

csstype@^3.0.2:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/csstype/-/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

"d3-array@2 - 3", "d3-array@2.10.0 - 3", d3-array@^3.1.6:
  version "3.2.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-array/-/d3-array-3.2.4.tgz#15fec33b237f97ac5d7c986dc77da273a8ed0bb5"
  integrity sha1-Ff7DOyN/l6xdfJhtx32ic6jtC7U=
  dependencies:
    internmap "1 - 2"

"d3-color@1 - 3":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-color/-/d3-color-3.1.0.tgz#395b2833dfac71507f12ac2f7af23bf819de24e2"
  integrity sha1-OVsoM9+scVB/EqwvevI7+BneJOI=

d3-ease@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-ease/-/d3-ease-3.0.1.tgz#9658ac38a2140d59d346160f1f6c30fda0bd12f4"
  integrity sha1-llisOKIUDVnTRhYPH2ww/aC9EvQ=

"d3-format@1 - 3":
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-format/-/d3-format-3.1.0.tgz#9260e23a28ea5cb109e93b21a06e24e2ebd55641"
  integrity sha1-kmDiOijqXLEJ6TshoG4k4uvVVkE=

"d3-interpolate@1.2.0 - 3", d3-interpolate@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-interpolate/-/d3-interpolate-3.0.1.tgz#3c47aa5b32c5b3dfb56ef3fd4342078a632b400d"
  integrity sha1-PEeqWzLFs9+1bvP9Q0IHimMrQA0=
  dependencies:
    d3-color "1 - 3"

d3-path@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-path/-/d3-path-3.1.0.tgz#22df939032fb5a71ae8b1800d61ddb7851c42526"
  integrity sha1-It+TkDL7WnGuixgA1h3beFHEJSY=

d3-scale@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-scale/-/d3-scale-4.0.2.tgz#82b38e8e8ff7080764f8dcec77bd4be393689396"
  integrity sha1-grOOjo/3CAdk+Nzsd71L45Nok5Y=
  dependencies:
    d3-array "2.10.0 - 3"
    d3-format "1 - 3"
    d3-interpolate "1.2.0 - 3"
    d3-time "2.1.1 - 3"
    d3-time-format "2 - 4"

d3-shape@^3.1.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-shape/-/d3-shape-3.2.0.tgz#a1a839cbd9ba45f28674c69d7f855bcf91dfc6a5"
  integrity sha1-oag5y9m6RfKGdMadf4Vbz5HfxqU=
  dependencies:
    d3-path "^3.1.0"

"d3-time-format@2 - 4":
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-time-format/-/d3-time-format-4.1.0.tgz#7ab5257a5041d11ecb4fe70a5c7d16a195bb408a"
  integrity sha1-erUlelBB0R7LT+cKXH0WoZW7QIo=
  dependencies:
    d3-time "1 - 3"

"d3-time@1 - 3", "d3-time@2.1.1 - 3", d3-time@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-time/-/d3-time-3.1.0.tgz#9310db56e992e3c0175e1ef385e545e48a9bb5c7"
  integrity sha1-kxDbVumS48AXXh7zheVF5Iqbtcc=
  dependencies:
    d3-array "2 - 3"

d3-timer@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/d3-timer/-/d3-timer-3.0.1.tgz#6284d2a2708285b1abb7e201eda4380af35e63b0"
  integrity sha1-YoTSonCChbGrt+IB7aQ4CvNeY7A=

data-view-buffer@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/data-view-buffer/-/data-view-buffer-1.0.2.tgz#211a03ba95ecaf7798a8c7198d79536211f88570"
  integrity sha1-IRoDupXsr3eYqMcZjXlTYhH4hXA=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-length@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/data-view-byte-length/-/data-view-byte-length-1.0.2.tgz#9e80f7ca52453ce3e93d25a35318767ea7704735"
  integrity sha1-noD3ylJFPOPpPSWjUxh2fqdwRzU=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-data-view "^1.0.2"

data-view-byte-offset@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/data-view-byte-offset/-/data-view-byte-offset-1.0.1.tgz#068307f9b71ab76dbbe10291389e020856606191"
  integrity sha1-BoMH+bcat2274QKROJ4CCFZgYZE=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-data-view "^1.0.1"

date-fns-jalali@4.1.0-0:
  version "4.1.0-0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/date-fns-jalali/-/date-fns-jalali-4.1.0-0.tgz#9c7fb286004fab267a300d3e9f1ada9f10b4b6b0"
  integrity sha1-nH+yhgBPqyZ6MA0+nxranxC0trA=

date-fns@4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/date-fns/-/date-fns-4.1.0.tgz#64b3d83fff5aa80438f5b1a633c2e83b8a1c2d14"
  integrity sha1-ZLPYP/9aqAQ49bGmM8LoO4ocLRQ=

dayjs@1.11.12:
  version "1.11.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dayjs/-/dayjs-1.11.12.tgz#5245226cc7f40a15bf52e0b99fd2a04669ccac1d"
  integrity sha1-UkUibMf0ChW/UuC5n9KgRmnMrB0=

de-indent@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/de-indent/-/de-indent-1.0.2.tgz#b2038e846dc33baa5796128d0804b455b8c1e21d"
  integrity sha1-sgOOhG3DO6pXlhKNCAS0VbjB4h0=

debug@^2.6.9:
  version "2.6.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/debug/-/debug-2.6.9.tgz#5d128515df134ff327e90a4c93f4e077a536341f"
  integrity sha1-XRKFFd8TT/Mn6QpMk/Tgd6U2NB8=
  dependencies:
    ms "2.0.0"

debug@^3.1.0, debug@^3.2.7:
  version "3.2.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/debug/-/debug-3.2.7.tgz#72580b7e9145fb39b6676f9c5e5fb100b934179a"
  integrity sha1-clgLfpFF+zm2Z2+cXl+xALk0F5o=
  dependencies:
    ms "^2.1.1"

debug@^4.1.0, debug@^4.1.1, debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.4.0, debug@^4.4.1:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/debug/-/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

decimal.js-light@^2.5.1:
  version "2.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/decimal.js-light/-/decimal.js-light-2.5.1.tgz#134fd32508f19e208f4fb2f8dac0d2626a867934"
  integrity sha1-E0/TJQjxniCPT7L42sDSYmqGeTQ=

dedent@^1.0.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dedent/-/dedent-1.6.0.tgz#79d52d6389b1ffa67d2bcef59ba51847a9d503b2"
  integrity sha1-edUtY4mx/6Z9K871m6UYR6nVA7I=

deep-eql@^5.0.1:
  version "5.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/deep-eql/-/deep-eql-5.0.2.tgz#4b756d8d770a9257300825d52a2c2cff99c3a341"
  integrity sha1-S3VtjXcKklcwCCXVKiws/5nDo0E=

deep-is@^0.1.3:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/deep-is/-/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

deepmerge@^4.2.2:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/deepmerge/-/deepmerge-4.3.1.tgz#44b5f2147cd3b00d4b56137685966f26fd25dd4a"
  integrity sha1-RLXyFHzTsA1LVhN2hZZvJv0l3Uo=

define-data-property@^1.0.1, define-data-property@^1.1.4:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/define-data-property/-/define-data-property-1.1.4.tgz#894dc141bb7d3060ae4366f6a0107e68fbe48c5e"
  integrity sha1-iU3BQbt9MGCuQ2b2oBB+aPvkjF4=
  dependencies:
    es-define-property "^1.0.0"
    es-errors "^1.3.0"
    gopd "^1.0.1"

define-lazy-prop@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz#3f7ae421129bcaaac9bc74905c98a0009ec9ee7f"
  integrity sha1-P3rkIRKbyqrJvHSQXJigAJ7J7n8=

define-properties@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/define-properties/-/define-properties-1.2.1.tgz#10781cc616eb951a80a034bafcaa7377f6af2b6c"
  integrity sha1-EHgcxhbrlRqAoDS6/Kpzd/avK2w=
  dependencies:
    define-data-property "^1.0.1"
    has-property-descriptors "^1.0.0"
    object-keys "^1.1.1"

del@^6.0.0:
  version "6.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/del/-/del-6.1.1.tgz#3b70314f1ec0aa325c6b14eb36b95786671edb7a"
  integrity sha1-O3AxTx7AqjJcaxTrNrlXhmce23o=
  dependencies:
    globby "^11.0.1"
    graceful-fs "^4.2.4"
    is-glob "^4.0.1"
    is-path-cwd "^2.2.0"
    is-path-inside "^3.0.2"
    p-map "^4.0.0"
    rimraf "^3.0.2"
    slash "^3.0.0"

dequal@^2.0.2, dequal@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dequal/-/dequal-2.0.3.tgz#2644214f1997d39ed0ee0ece72335490a7ac67be"
  integrity sha1-JkQhTxmX057Q7g7OcjNUkKesZ74=

detect-indent@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/detect-indent/-/detect-indent-6.1.0.tgz#592485ebbbf6b3b1ab2be175c8393d04ca0d57e6"
  integrity sha1-WSSF67v2s7GrK+F1yDk9BMoNV+Y=

detect-newline@^3.0.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/detect-newline/-/detect-newline-3.1.0.tgz#576f5dfc63ae1a192ff192d8ad3af6308991b651"
  integrity sha1-V29d/GOuGhkv8ZLYrTr2MImRtlE=

detect-node-es@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/detect-node-es/-/detect-node-es-1.1.0.tgz#163acdf643330caa0b4cd7c21e7ee7755d6fa493"
  integrity sha1-FjrN9kMzDKoLTNfCHn7ndV1vpJM=

didyoumean@^1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/didyoumean/-/didyoumean-1.2.2.tgz#989346ffe9e839b4555ecf5666edea0d3e8ad037"
  integrity sha1-mJNG/+noObRVXs9WZu3qDT6K0Dc=

diff-sequences@^28.1.1:
  version "28.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/diff-sequences/-/diff-sequences-28.1.1.tgz#9989dc731266dc2903457a70e996f3a041913ac6"
  integrity sha1-mYnccxJm3CkDRXpw6ZbzoEGROsY=

diff-sequences@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/diff-sequences/-/diff-sequences-29.6.3.tgz#4deaf894d11407c51efc8418012f9e70b84ea921"
  integrity sha1-Ter4lNEUB8Ue/IQYAS+ecLhOqSE=

diff@^4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/diff/-/diff-4.0.2.tgz#60f3aecb89d5fae520c11aa19efc2bb982aade7d"
  integrity sha1-YPOuy4nV+uUgwRqhnvwruYKq3n0=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dir-glob/-/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

dlv@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dlv/-/dlv-1.1.3.tgz#5c198a8a11453596e751494d49874bc7732f2e79"
  integrity sha1-XBmKihFFNZbnUUlNSYdLx3MvLnk=

doctrine@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/doctrine/-/doctrine-2.1.0.tgz#5cd01fc101621b42c4cd7f5d1a66243716d3f39d"
  integrity sha1-XNAfwQFiG0LEzX9dGmYkNxbT850=
  dependencies:
    esutils "^2.0.2"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/doctrine/-/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

dom-accessibility-api@^0.5.9:
  version "0.5.16"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dom-accessibility-api/-/dom-accessibility-api-0.5.16.tgz#5a7429e6066eb3664d911e33fb0e45de8eb08453"
  integrity sha1-WnQp5gZus2ZNkR4z+w5F3o6whFM=

dom-accessibility-api@^0.6.3:
  version "0.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dom-accessibility-api/-/dom-accessibility-api-0.6.3.tgz#993e925cc1d73f2c662e7d75dd5a5445259a8fd8"
  integrity sha1-mT6SXMHXPyxmLn113VpURSWaj9g=

dom-css@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dom-css/-/dom-css-2.1.0.tgz#fdbc2d5a015d0a3e1872e11472bbd0e7b9e6a202"
  integrity sha1-/bwtWgFdCj4YcuEUcrvQ57nmogI=
  dependencies:
    add-px-to-style "1.0.0"
    prefix-style "2.0.1"
    to-camel-case "1.0.0"

dom-helpers@^5.0.1:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dom-helpers/-/dom-helpers-5.2.1.tgz#d9400536b2bf8225ad98fe052e029451ac40e902"
  integrity sha1-2UAFNrK/giWtmP4FLgKUUaxA6QI=
  dependencies:
    "@babel/runtime" "^7.8.7"
    csstype "^3.0.2"

dot-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dot-case/-/dot-case-3.0.4.tgz#9b2b670d00a431667a8a75ba29cd1b98809ce751"
  integrity sha1-mytnDQCkMWZ6inW6Kc0bmICc51E=
  dependencies:
    no-case "^3.0.4"
    tslib "^2.0.3"

dotenv@16.4.7:
  version "16.4.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dotenv/-/dotenv-16.4.7.tgz#0e20c5b82950140aa99be360a8a5f52335f53c26"
  integrity sha1-DiDFuClQFAqpm+NgqKX1IzX1PCY=

dunder-proto@^1.0.0, dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/dunder-proto/-/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

eastasianwidth@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eastasianwidth/-/eastasianwidth-0.2.0.tgz#696ce2ec0aa0e6ea93a397ffcf24aa7840c827cb"
  integrity sha1-aWzi7Aqg5uqTo5f/zySqeEDIJ8s=

electron-to-chromium@^1.5.160:
  version "1.5.168"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/electron-to-chromium/-/electron-to-chromium-1.5.168.tgz#ad2bad79e34ba3ba8d79ba809c1e0756685c3f4f"
  integrity sha1-rSuteeNLo7qNebqAnB4HVmhcP08=

emittery@^0.13.1:
  version "0.13.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/emittery/-/emittery-0.13.1.tgz#c04b8c3457490e0847ae51fced3af52d338e3dad"
  integrity sha1-wEuMNFdJDghHrlH87Tr1LTOOPa0=

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/emoji-regex/-/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

emoji-regex@^9.2.2:
  version "9.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/emoji-regex/-/emoji-regex-9.2.2.tgz#840c8803b0d8047f4ff0cf963176b32d4ef3ed72"
  integrity sha1-hAyIA7DYBH9P8M+WMXazLU7z7XI=

enquirer@^2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/enquirer/-/enquirer-2.4.1.tgz#93334b3fbd74fc7097b224ab4a8fb7e40bf4ae56"
  integrity sha1-kzNLP710/HCXsiSrSo+35Av0rlY=
  dependencies:
    ansi-colors "^4.1.1"
    strip-ansi "^6.0.1"

entities@^4.4.0, entities@^4.5.0:
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/entities/-/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48"
  integrity sha1-XSaOpecRPsdMTQM7eepaNaSI+0g=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/error-ex/-/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

es-abstract@^1.23.2, es-abstract@^1.23.5, es-abstract@^1.23.6, es-abstract@^1.23.9, es-abstract@^1.24.0:
  version "1.24.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-abstract/-/es-abstract-1.24.0.tgz#c44732d2beb0acc1ed60df840869e3106e7af328"
  integrity sha1-xEcy0r6wrMHtYN+ECGnjEG568yg=
  dependencies:
    array-buffer-byte-length "^1.0.2"
    arraybuffer.prototype.slice "^1.0.4"
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    data-view-buffer "^1.0.2"
    data-view-byte-length "^1.0.2"
    data-view-byte-offset "^1.0.1"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    es-set-tostringtag "^2.1.0"
    es-to-primitive "^1.3.0"
    function.prototype.name "^1.1.8"
    get-intrinsic "^1.3.0"
    get-proto "^1.0.1"
    get-symbol-description "^1.1.0"
    globalthis "^1.0.4"
    gopd "^1.2.0"
    has-property-descriptors "^1.0.2"
    has-proto "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    internal-slot "^1.1.0"
    is-array-buffer "^3.0.5"
    is-callable "^1.2.7"
    is-data-view "^1.0.2"
    is-negative-zero "^2.0.3"
    is-regex "^1.2.1"
    is-set "^2.0.3"
    is-shared-array-buffer "^1.0.4"
    is-string "^1.1.1"
    is-typed-array "^1.1.15"
    is-weakref "^1.1.1"
    math-intrinsics "^1.1.0"
    object-inspect "^1.13.4"
    object-keys "^1.1.1"
    object.assign "^4.1.7"
    own-keys "^1.0.1"
    regexp.prototype.flags "^1.5.4"
    safe-array-concat "^1.1.3"
    safe-push-apply "^1.0.0"
    safe-regex-test "^1.1.0"
    set-proto "^1.0.0"
    stop-iteration-iterator "^1.1.0"
    string.prototype.trim "^1.2.10"
    string.prototype.trimend "^1.0.9"
    string.prototype.trimstart "^1.0.8"
    typed-array-buffer "^1.0.3"
    typed-array-byte-length "^1.0.3"
    typed-array-byte-offset "^1.0.4"
    typed-array-length "^1.0.7"
    unbox-primitive "^1.1.0"
    which-typed-array "^1.1.19"

es-define-property@^1.0.0, es-define-property@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-define-property/-/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-errors/-/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-module-lexer@^1.7.0:
  version "1.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-module-lexer/-/es-module-lexer-1.7.0.tgz#9159601561880a85f2734560a9099b2c31e5372a"
  integrity sha1-kVlgFWGICoXyc0VgqQmbLDHlNyo=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-object-atoms/-/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

es-shim-unscopables@^1.0.2:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-shim-unscopables/-/es-shim-unscopables-1.1.0.tgz#438df35520dac5d105f3943d927549ea3b00f4b5"
  integrity sha1-Q43zVSDaxdEF85Q9knVJ6jsA9LU=
  dependencies:
    hasown "^2.0.2"

es-to-primitive@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-to-primitive/-/es-to-primitive-1.3.0.tgz#96c89c82cc49fd8794a24835ba3e1ff87f214e18"
  integrity sha1-lsicgsxJ/YeUokg1uj4f+H8hThg=
  dependencies:
    is-callable "^1.2.7"
    is-date-object "^1.0.5"
    is-symbol "^1.0.4"

es-toolkit@^1.39.3:
  version "1.39.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/es-toolkit/-/es-toolkit-1.39.5.tgz#ee2a78a66aafb76c7345af0ea8c06722c78ef1fd"
  integrity sha1-7ip4pmqvt2xzRa8OqMBnIseO8f0=

esbuild-register@^3.5.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esbuild-register/-/esbuild-register-3.6.0.tgz#cf270cfa677baebbc0010ac024b823cbf723a36d"
  integrity sha1-zycM+md7rrvAAQrAJLgjy/cjo20=
  dependencies:
    debug "^4.3.4"

"esbuild@^0.18.0 || ^0.19.0 || ^0.20.0 || ^0.21.0 || ^0.22.0 || ^0.23.0 || ^0.24.0 || ^0.25.0", esbuild@^0.25.0, esbuild@~0.25.0:
  version "0.25.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esbuild/-/esbuild-0.25.5.tgz#71075054993fdfae76c66586f9b9c1f8d7edd430"
  integrity sha1-cQdQVJk/3652xmWG+bnB+Nft1DA=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.5"
    "@esbuild/android-arm" "0.25.5"
    "@esbuild/android-arm64" "0.25.5"
    "@esbuild/android-x64" "0.25.5"
    "@esbuild/darwin-arm64" "0.25.5"
    "@esbuild/darwin-x64" "0.25.5"
    "@esbuild/freebsd-arm64" "0.25.5"
    "@esbuild/freebsd-x64" "0.25.5"
    "@esbuild/linux-arm" "0.25.5"
    "@esbuild/linux-arm64" "0.25.5"
    "@esbuild/linux-ia32" "0.25.5"
    "@esbuild/linux-loong64" "0.25.5"
    "@esbuild/linux-mips64el" "0.25.5"
    "@esbuild/linux-ppc64" "0.25.5"
    "@esbuild/linux-riscv64" "0.25.5"
    "@esbuild/linux-s390x" "0.25.5"
    "@esbuild/linux-x64" "0.25.5"
    "@esbuild/netbsd-arm64" "0.25.5"
    "@esbuild/netbsd-x64" "0.25.5"
    "@esbuild/openbsd-arm64" "0.25.5"
    "@esbuild/openbsd-x64" "0.25.5"
    "@esbuild/sunos-x64" "0.25.5"
    "@esbuild/win32-arm64" "0.25.5"
    "@esbuild/win32-ia32" "0.25.5"
    "@esbuild/win32-x64" "0.25.5"

esbuild@^0.21.3:
  version "0.21.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esbuild/-/esbuild-0.21.5.tgz#9ca301b120922959b766360d8ac830da0d02997d"
  integrity sha1-nKMBsSCSKVm3ZjYNisgw2g0CmX0=
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.21.5"
    "@esbuild/android-arm" "0.21.5"
    "@esbuild/android-arm64" "0.21.5"
    "@esbuild/android-x64" "0.21.5"
    "@esbuild/darwin-arm64" "0.21.5"
    "@esbuild/darwin-x64" "0.21.5"
    "@esbuild/freebsd-arm64" "0.21.5"
    "@esbuild/freebsd-x64" "0.21.5"
    "@esbuild/linux-arm" "0.21.5"
    "@esbuild/linux-arm64" "0.21.5"
    "@esbuild/linux-ia32" "0.21.5"
    "@esbuild/linux-loong64" "0.21.5"
    "@esbuild/linux-mips64el" "0.21.5"
    "@esbuild/linux-ppc64" "0.21.5"
    "@esbuild/linux-riscv64" "0.21.5"
    "@esbuild/linux-s390x" "0.21.5"
    "@esbuild/linux-x64" "0.21.5"
    "@esbuild/netbsd-x64" "0.21.5"
    "@esbuild/openbsd-x64" "0.21.5"
    "@esbuild/sunos-x64" "0.21.5"
    "@esbuild/win32-arm64" "0.21.5"
    "@esbuild/win32-ia32" "0.21.5"
    "@esbuild/win32-x64" "0.21.5"

escalade@^3.1.1, escalade@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/escalade/-/escalade-3.2.0.tgz#011a3f69856ba189dffa7dc8fcce99d2a87903e5"
  integrity sha1-ARo/aYVroYnf+n3I/M6Z0qh5A+U=

escape-string-regexp@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz#1b61c0562190a8dff6ae3bb2cf0200ca130b86d4"
  integrity sha1-G2HAViGQqN/2rjuyzwIAyhMLhtQ=

escape-string-regexp@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz#a30304e99daa32e23b2fd20f51babd07cffca344"
  integrity sha1-owME6Z2qMuI7L9IPUbq9B8/8o0Q=

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-config-prettier@8.5.0:
  version "8.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-config-prettier/-/eslint-config-prettier-8.5.0.tgz#5a81680ec934beca02c7b1a61cf8ca34b66feab1"
  integrity sha1-WoFoDsk0vsoCx7GmHPjKNLZv6rE=

eslint-import-resolver-node@^0.3.6:
  version "0.3.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-import-resolver-node/-/eslint-import-resolver-node-0.3.9.tgz#d4eaac52b8a2e7c3cd1903eb00f7e053356118ac"
  integrity sha1-1OqsUrii58PNGQPrAPfgUzVhGKw=
  dependencies:
    debug "^3.2.7"
    is-core-module "^2.13.0"
    resolve "^1.22.4"

eslint-module-utils@^2.7.3:
  version "2.12.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-module-utils/-/eslint-module-utils-2.12.0.tgz#fe4cfb948d61f49203d7b08871982b65b9af0b0b"
  integrity sha1-/kz7lI1h9JID17CIcZgrZbmvCws=
  dependencies:
    debug "^3.2.7"

eslint-plugin-import@2.26.0:
  version "2.26.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-import/-/eslint-plugin-import-2.26.0.tgz#f812dc47be4f2b72b478a021605a59fc6fe8b88b"
  integrity sha1-+BLcR75PK3K0eKAhYFpZ/G/ouIs=
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flat "^1.2.5"
    debug "^2.6.9"
    doctrine "^2.1.0"
    eslint-import-resolver-node "^0.3.6"
    eslint-module-utils "^2.7.3"
    has "^1.0.3"
    is-core-module "^2.8.1"
    is-glob "^4.0.3"
    minimatch "^3.1.2"
    object.values "^1.1.5"
    resolve "^1.22.0"
    tsconfig-paths "^3.14.1"

eslint-plugin-local-rules@3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-local-rules/-/eslint-plugin-local-rules-3.0.2.tgz#84c02ea1d604ecb00970779ad27f00738ff361ae"
  integrity sha1-hMAuodYE7LAJcHea0n8Ac4/zYa4=

eslint-plugin-prettier@5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-prettier/-/eslint-plugin-prettier-5.0.0.tgz#6887780ed95f7708340ec79acfdf60c35b9be57a"
  integrity sha1-aId4Dtlfdwg0Dseaz99gw1ub5Xo=
  dependencies:
    prettier-linter-helpers "^1.0.0"
    synckit "^0.8.5"

eslint-plugin-react-hooks@4.5.0:
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-4.5.0.tgz#5f762dfedf8b2cf431c689f533c9d3fa5dcf25ad"
  integrity sha1-X3Yt/t+LLPQxxon1M8nT+l3PJa0=

eslint-plugin-react@7.29.4:
  version "7.29.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-react/-/eslint-plugin-react-7.29.4.tgz#4717de5227f55f3801a5fd51a16a4fa22b5914d2"
  integrity sha1-RxfeUif1XzgBpf1RoWpPoitZFNI=
  dependencies:
    array-includes "^3.1.4"
    array.prototype.flatmap "^1.2.5"
    doctrine "^2.1.0"
    estraverse "^5.3.0"
    jsx-ast-utils "^2.4.1 || ^3.0.0"
    minimatch "^3.1.2"
    object.entries "^1.1.5"
    object.fromentries "^2.0.5"
    object.hasown "^1.1.0"
    object.values "^1.1.5"
    prop-types "^15.8.1"
    resolve "^2.0.0-next.3"
    semver "^6.3.0"
    string.prototype.matchall "^4.0.6"

eslint-plugin-storybook@0.12.0:
  version "0.12.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-storybook/-/eslint-plugin-storybook-0.12.0.tgz#09ae667a7c569787d961b6f83337b2deee9daea8"
  integrity sha1-Ca5menxWl4fZYbb4Mzey3u6drqg=
  dependencies:
    "@storybook/csf" "^0.1.11"
    "@typescript-eslint/utils" "^8.8.1"
    ts-dedent "^2.2.0"

eslint-plugin-unicorn@42.0.0:
  version "42.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-plugin-unicorn/-/eslint-plugin-unicorn-42.0.0.tgz#47d60c00c263ad743403b052db689e39acbacff1"
  integrity sha1-R9YMAMJjrXQ0A7BS22ieOay6z/E=
  dependencies:
    "@babel/helper-validator-identifier" "^7.15.7"
    ci-info "^3.3.0"
    clean-regexp "^1.0.0"
    eslint-utils "^3.0.0"
    esquery "^1.4.0"
    indent-string "^4.0.0"
    is-builtin-module "^3.1.0"
    lodash "^4.17.21"
    pluralize "^8.0.0"
    read-pkg-up "^7.0.1"
    regexp-tree "^0.1.24"
    safe-regex "^2.1.1"
    semver "^7.3.5"
    strip-indent "^3.0.0"

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-scope/-/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-utils@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-utils/-/eslint-utils-3.0.0.tgz#8aebaface7345bb33559db0a1f13a1d2d48c3672"
  integrity sha1-iuuvrOc0W7M1WdsKHxOh0tSMNnI=
  dependencies:
    eslint-visitor-keys "^2.0.0"

eslint-visitor-keys@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz#f65328259305927392c938ed44eb0a5c9b2bd303"
  integrity sha1-9lMoJZMFknOSyTjtROsKXJsr0wM=

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.0, eslint-visitor-keys@^4.2.1:
  version "4.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz#4cfea60fe7dd0ad8e816e1ed026c1d5251b512c1"
  integrity sha1-TP6mD+fdCtjoFuHtAmwdUlG1EsE=

eslint@8.57.0:
  version "8.57.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eslint/-/eslint-8.57.0.tgz#c786a6fd0e0b68941aaf624596fb987089195668"
  integrity sha1-x4am/Q4LaJQar2JFlvuYcIkZVmg=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.0"
    "@humanwhocodes/config-array" "^0.11.14"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/espree/-/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esprima@^4.0.0, esprima@~4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esprima/-/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

esquery@^1.4.0, esquery@^1.4.2:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esquery/-/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esrecurse/-/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0, estraverse@^5.3.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/estraverse/-/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/estree-walker/-/estree-walker-2.0.2.tgz#52f010178c2a4c117a7757cfe942adb7d2da4cac"
  integrity sha1-UvAQF4wqTBF6d1fP6UKtt9LaTKw=

estree-walker@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/estree-walker/-/estree-walker-3.0.3.tgz#67c3e549ec402a487b4fc193d1953a524752340d"
  integrity sha1-Z8PlSexAKkh7T8GT0ZU6UkdSNA0=
  dependencies:
    "@types/estree" "^1.0.0"

esutils@^2.0.2:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/esutils/-/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

eventemitter3@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/eventemitter3/-/eventemitter3-5.0.1.tgz#53f5ffd0a492ac800721bb42c66b841de96423c4"
  integrity sha1-U/X/0KSSrIAHIbtCxmuEHelkI8Q=

events@1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/events/-/events-1.1.1.tgz#9ebdb7635ad099c70dcc4c2a1f5004288e8bd924"
  integrity sha1-nr23Y1rQmccNzEwqH1AEKI6L2SQ=

execa@^5.0.0:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/execa/-/execa-5.1.1.tgz#f80ad9cbf4298f7bd1d4c9555c21e93741c411dd"
  integrity sha1-+ArZy/Qpj3vR1MlVXCHpN0HEEd0=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.0"
    human-signals "^2.1.0"
    is-stream "^2.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^4.0.1"
    onetime "^5.1.2"
    signal-exit "^3.0.3"
    strip-final-newline "^2.0.0"

execa@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/execa/-/execa-7.2.0.tgz#657e75ba984f42a70f38928cedc87d6f2d4fe4e9"
  integrity sha1-ZX51uphPQqcPOJKM7ch9by1P5Ok=
  dependencies:
    cross-spawn "^7.0.3"
    get-stream "^6.0.1"
    human-signals "^4.3.0"
    is-stream "^3.0.0"
    merge-stream "^2.0.0"
    npm-run-path "^5.1.0"
    onetime "^6.0.0"
    signal-exit "^3.0.7"
    strip-final-newline "^3.0.0"

exit@^0.1.2:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/exit/-/exit-0.1.2.tgz#0632638f8d877cc82107d30a0fff1a17cba1cd0c"
  integrity sha1-BjJjj42HfMghB9MKD/8aF8uhzQw=

expect-type@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/expect-type/-/expect-type-1.2.1.tgz#af76d8b357cf5fa76c41c09dafb79c549e75f71f"
  integrity sha1-r3bYs1fPX6dsQcCdr7ecVJ519x8=

expect@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/expect/-/expect-29.7.0.tgz#578874590dcb3214514084c08115d8aee61e11bc"
  integrity sha1-V4h0WQ3LMhRRQITAgRXYruYeEbw=
  dependencies:
    "@jest/expect-utils" "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"

exsolve@^1.0.1:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/exsolve/-/exsolve-1.0.5.tgz#1f5b6b4fe82ad6b28a173ccb955a635d77859dcf"
  integrity sha1-H1trT+gq1rKKFzzLlVpjXXeFnc8=

extendable-error@^0.1.5:
  version "0.1.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/extendable-error/-/extendable-error-0.1.7.tgz#60b9adf206264ac920058a7395685ae4670c2b96"
  integrity sha1-YLmt8gYmSskgBYpzlWha5GcMK5Y=

external-editor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/external-editor/-/external-editor-3.1.0.tgz#cb03f740befae03ea4d283caed2741a83f335495"
  integrity sha1-ywP3QL764D6k0oPK7SdBqD8zVJU=
  dependencies:
    chardet "^0.7.0"
    iconv-lite "^0.4.24"
    tmp "^0.0.33"

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-diff@^1.1.2:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-diff/-/fast-diff-1.3.0.tgz#ece407fa550a64d638536cd727e129c61616e0f0"
  integrity sha1-7OQH+lUKZNY4U2zXJ+EpxhYW4PA=

fast-glob@^3.2.7, fast-glob@^3.2.9, fast-glob@^3.3.2:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-glob/-/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0, fast-json-stable-stringify@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-uri@^3.0.1:
  version "3.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fast-uri/-/fast-uri-3.0.6.tgz#88f130b77cfaea2378d56bf970dea21257a68748"
  integrity sha1-iPEwt3z66iN41Wv5cN6iElemh0g=

fastq@^1.6.0:
  version "1.19.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fastq/-/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

fb-watchman@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fb-watchman/-/fb-watchman-2.0.2.tgz#e9524ee6b5c77e9e5001af0f85f3adbb8623255c"
  integrity sha1-6VJO5rXHfp5QAa8PhfOtu4YjJVw=
  dependencies:
    bser "2.1.1"

fdir@^6.4.4:
  version "6.4.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fdir/-/fdir-6.4.6.tgz#2b268c0232697063111bbf3f64810a2a741ba281"
  integrity sha1-KyaMAjJpcGMRG78/ZIEKKnQbooE=

fflate@^0.8.2:
  version "0.8.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fflate/-/fflate-0.8.2.tgz#fc8631f5347812ad6028bbe4a2308b2792aa1dea"
  integrity sha1-/IYx9TR4Eq1gKLvkojCLJ5KqHeo=

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/file-entry-cache/-/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

filesize@^10.0.12:
  version "10.1.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/filesize/-/filesize-10.1.6.tgz#31194da825ac58689c0bce3948f33ce83aabd361"
  integrity sha1-MRlNqCWsWGicC845SPM86Dqr02E=

fill-range@^7.1.1:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fill-range/-/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-cache-dir@^3.2.0:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/find-cache-dir/-/find-cache-dir-3.3.2.tgz#b30c5b6eff0730731aea9bbd9dbecbd80256d64b"
  integrity sha1-swxbbv8HMHMa6pu9nb7L2AJW1ks=
  dependencies:
    commondir "^1.0.1"
    make-dir "^3.0.2"
    pkg-dir "^4.1.0"

find-free-port-sync@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/find-free-port-sync/-/find-free-port-sync-1.0.0.tgz#10c9007655b6b65a7900e79d391e8da21e31cc19"
  integrity sha1-EMkAdlW2tlp5AOedOR6Noh4xzBk=

find-up@^4.0.0, find-up@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/find-up/-/find-up-4.1.0.tgz#97afe7d6cdc0bc5928584b7c8d7b16e8a9aa5d19"
  integrity sha1-l6/n1s3AvFkoWEt8jXsW6KmqXRk=
  dependencies:
    locate-path "^5.0.0"
    path-exists "^4.0.0"

find-up@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/find-up/-/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/flat-cache/-/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flatted@^3.2.9, flatted@^3.3.3:
  version "3.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/flatted/-/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

focus-lock@^1.3.5, focus-lock@^1.3.6:
  version "1.3.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/focus-lock/-/focus-lock-1.3.6.tgz#955eec1e10591d56f679258edb94aedb11d691cd"
  integrity sha1-lV7sHhBZHVb2eSWO25Su2xHWkc0=
  dependencies:
    tslib "^2.0.3"

for-each@^0.3.3, for-each@^0.3.5:
  version "0.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/for-each/-/for-each-0.3.5.tgz#d650688027826920feeb0af747ee7b9421a41d47"
  integrity sha1-1lBogCeCaSD+6wr3R+57lCGkHUc=
  dependencies:
    is-callable "^1.2.7"

foreground-child@^3.1.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/foreground-child/-/foreground-child-3.3.1.tgz#32e8e9ed1b68a3497befb9ac2b6adf92a638576f"
  integrity sha1-Mujp7Rtoo0l777msK2rfkqY4V28=
  dependencies:
    cross-spawn "^7.0.6"
    signal-exit "^4.0.1"

fraction.js@^4.3.7:
  version "4.3.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fraction.js/-/fraction.js-4.3.7.tgz#06ca0085157e42fda7f9e726e79fefc4068840f7"
  integrity sha1-BsoAhRV+Qv2n+ecm55/vxAaIQPc=

fs-extra@^11.1.0, fs-extra@^11.2.0, fs-extra@~11.3.0:
  version "11.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fs-extra/-/fs-extra-11.3.0.tgz#0daced136bbaf65a555a326719af931adc7a314d"
  integrity sha1-DaztE2u69lpVWjJnGa+TGtx6MU0=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs-extra@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fs-extra/-/fs-extra-7.0.1.tgz#4f189c44aa123b895f722804f55ea23eadc348e9"
  integrity sha1-TxicRKoSO4lfcigE9V6iPq3DSOk=
  dependencies:
    graceful-fs "^4.1.2"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^8.1.0:
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fs-extra/-/fs-extra-8.1.0.tgz#49d43c45a88cd9677668cb7be1b46efdb8d2e1c0"
  integrity sha1-SdQ8RaiM2Wd2aMt74bRu/bjS4cA=
  dependencies:
    graceful-fs "^4.2.0"
    jsonfile "^4.0.0"
    universalify "^0.1.0"

fs-extra@^9.1.0:
  version "9.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fs-extra/-/fs-extra-9.1.0.tgz#5954460c764a8da2094ba3554bf839e6b9a7c86d"
  integrity sha1-WVRGDHZKjaIJS6NVS/g55rmnyG0=
  dependencies:
    at-least-node "^1.0.0"
    graceful-fs "^4.2.0"
    jsonfile "^6.0.1"
    universalify "^2.0.0"

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fs.realpath/-/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

fsevents@^2.3.2, fsevents@~2.3.2, fsevents@~2.3.3:
  version "2.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/function-bind/-/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

function.prototype.name@^1.1.6, function.prototype.name@^1.1.8:
  version "1.1.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/function.prototype.name/-/function.prototype.name-1.1.8.tgz#e68e1df7b259a5c949eeef95cdbde53edffabb78"
  integrity sha1-5o4d97JZpclJ7u+Vzb3lPt/6u3g=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    functions-have-names "^1.2.3"
    hasown "^2.0.2"
    is-callable "^1.2.7"

functions-have-names@^1.2.3:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/functions-have-names/-/functions-have-names-1.2.3.tgz#0404fe4ee2ba2f607f0e0ec3c80bae994133b834"
  integrity sha1-BAT+TuK6L2B/Dg7DyAuumUEzuDQ=

gensync@^1.0.0-beta.2:
  version "1.0.0-beta.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/gensync/-/gensync-1.0.0-beta.2.tgz#32a6ee76c3d7f52d46b2b1ae5d93fea8580a25e0"
  integrity sha1-MqbudsPX9S1GsrGuXZP+qFgKJeA=

get-caller-file@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-caller-file/-/get-caller-file-2.0.5.tgz#4f94412a82db32f36e3b0b9741f8a97feb031f7e"
  integrity sha1-T5RBKoLbMvNuOwuXQfipf+sDH34=

get-intrinsic@^1.2.4, get-intrinsic@^1.2.5, get-intrinsic@^1.2.6, get-intrinsic@^1.2.7, get-intrinsic@^1.3.0:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-intrinsic/-/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-package-type@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-package-type/-/get-package-type-0.1.0.tgz#8de2d803cff44df3bc6c456e6668b36c3926e11a"
  integrity sha1-jeLYA8/0TfO8bEVuZmizbDkm4Ro=

get-proto@^1.0.0, get-proto@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-proto/-/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

get-stream@^6.0.0, get-stream@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-stream/-/get-stream-6.0.1.tgz#a262d8eef67aced57c2852ad6167526a43cbf7b7"
  integrity sha1-omLY7vZ6ztV8KFKtYWdSakPL97c=

get-symbol-description@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-symbol-description/-/get-symbol-description-1.1.0.tgz#7bdd54e0befe8ffc9f3b4e203220d9f1e881b6ee"
  integrity sha1-e91U4L7+j/yfO04gMiDZ8eiBtu4=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"

get-tsconfig@^4.7.5:
  version "4.10.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/get-tsconfig/-/get-tsconfig-4.10.1.tgz#d34c1c01f47d65a606c37aa7a177bc3e56ab4b2e"
  integrity sha1-00wcAfR9ZaYGw3qnoXe8PlarSy4=
  dependencies:
    resolve-pkg-maps "^1.0.0"

glob-parent@^5.1.2, glob-parent@~5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/glob-parent/-/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/glob-parent/-/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^10.0.0, glob@^10.3.10, glob@^10.4.1:
  version "10.4.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/glob/-/glob-10.4.5.tgz#f4d9f0b90ffdbab09c9d77f5f29b4262517b0956"
  integrity sha1-9NnwuQ/9urCcnXf18ptCYlF7CVY=
  dependencies:
    foreground-child "^3.1.0"
    jackspeak "^3.1.2"
    minimatch "^9.0.4"
    minipass "^7.1.2"
    package-json-from-dist "^1.0.0"
    path-scurry "^1.11.1"

glob@^7.0.0, glob@^7.0.5, glob@^7.1.3, glob@^7.1.4:
  version "7.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/glob/-/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

globals@^11.1.0:
  version "11.12.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/globals/-/globals-11.12.0.tgz#ab8795338868a0babd8525758018c2a7eb95c42e"
  integrity sha1-q4eVM4hooLq9hSV1gBjCp+uVxC4=

globals@^13.19.0:
  version "13.24.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/globals/-/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globalthis@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/globalthis/-/globalthis-1.0.4.tgz#7430ed3a975d97bfb59bcce41f5cabbafa651236"
  integrity sha1-dDDtOpddl7+1m8zkH1yruvplEjY=
  dependencies:
    define-properties "^1.2.1"
    gopd "^1.0.1"

globby@^11.0.0, globby@^11.0.1:
  version "11.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/globby/-/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

gm@^1.23.1:
  version "1.25.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/gm/-/gm-1.25.1.tgz#a2755c550eda321a540de543f7a76d69797f6e98"
  integrity sha1-onVcVQ7aMhpUDeVD96dtaXl/bpg=
  dependencies:
    array-parallel "~0.1.3"
    array-series "~0.1.5"
    cross-spawn "^7.0.5"
    debug "^3.1.0"

gopd@^1.0.1, gopd@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/gopd/-/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

graceful-fs@^4.1.2, graceful-fs@^4.1.5, graceful-fs@^4.1.6, graceful-fs@^4.2.0, graceful-fs@^4.2.4, graceful-fs@^4.2.9:
  version "4.2.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/graceful-fs/-/graceful-fs-4.2.11.tgz#4183e4e8bf08bb6e05bbb2f7d2e0c8f712ca40e3"
  integrity sha1-QYPk6L8Iu24Fu7L30uDI9xLKQOM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/graphemer/-/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

happy-dom@^17.4.4:
  version "17.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/happy-dom/-/happy-dom-17.6.3.tgz#2caa37058e6f9605f1b8b7ae03045253e6089477"
  integrity sha1-LKo3BY5vlgXxuLeuAwRSU+YIlHc=
  dependencies:
    webidl-conversions "^7.0.0"
    whatwg-mimetype "^3.0.0"

has-bigints@^1.0.2:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-bigints/-/has-bigints-1.1.0.tgz#28607e965ac967e03cd2a2c70a2636a1edad49fe"
  integrity sha1-KGB+llrJZ+A80qLHCiY2oe2tSf4=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-flag/-/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

has-property-descriptors@^1.0.0, has-property-descriptors@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz#963ed7d071dc7bf5f084c5bfbe0d1b6222586854"
  integrity sha1-lj7X0HHce/XwhMW/vg0bYiJYaFQ=
  dependencies:
    es-define-property "^1.0.0"

has-proto@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-proto/-/has-proto-1.2.0.tgz#5de5a6eabd95fdffd9818b43055e8065e39fe9d5"
  integrity sha1-XeWm6r2V/f/ZgYtDBV6AZeOf6dU=
  dependencies:
    dunder-proto "^1.0.0"

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-symbols/-/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has-tostringtag/-/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

has@^1.0.3:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/has/-/has-1.0.4.tgz#2eb2860e000011dae4f1406a86fe80e530fb2ec6"
  integrity sha1-LrKGDgAAEdrk8UBqhv6A5TD7LsY=

hasown@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/hasown/-/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

he@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/he/-/he-1.2.0.tgz#84ae65fa7eafb165fddb61566ae14baf05664f0f"
  integrity sha1-hK5l+n6vsWX922FWauFLrwVmTw8=

history@^5.3.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/history/-/history-5.3.0.tgz#1548abaa245ba47992f063a0783db91ef201c73b"
  integrity sha1-FUirqiRbpHmS8GOgeD25HvIBxzs=
  dependencies:
    "@babel/runtime" "^7.7.6"

hoist-non-react-statics@*:
  version "3.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz#ece0acaf71d62c2969c2ec59feff42a4b1a85b45"
  integrity sha1-7OCsr3HWLClpwuxZ/v9CpLGoW0U=
  dependencies:
    react-is "^16.7.0"

hosted-git-info@^2.1.4:
  version "2.8.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/hosted-git-info/-/hosted-git-info-2.8.9.tgz#dffc0bf9a21c02209090f2aa69429e1414daf3f9"
  integrity sha1-3/wL+aIcAiCQkPKqaUKeFBTa8/k=

html-escaper@^2.0.0:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/html-escaper/-/html-escaper-2.0.2.tgz#dfd60027da36a36dfcbe236262c00a5822681453"
  integrity sha1-39YAJ9o2o238viNiYsAKWCJoFFM=

human-id@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/human-id/-/human-id-4.1.1.tgz#2801fbd61b9a5c1c9170f332802db6408a39a4b0"
  integrity sha1-KAH71huaXByRcPMygC22QIo5pLA=

human-signals@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/human-signals/-/human-signals-2.1.0.tgz#dc91fcba42e4d06e4abaed33b3e7a3c02f514ea0"
  integrity sha1-3JH8ukLk0G5Kuu0zs+ejwC9RTqA=

human-signals@^4.3.0:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/human-signals/-/human-signals-4.3.1.tgz#ab7f811e851fca97ffbd2c1fe9a958964de321b2"
  integrity sha1-q3+BHoUfypf/vSwf6alYlk3jIbI=

iconv-lite@^0.4.24:
  version "0.4.24"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/iconv-lite/-/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

ieee754@1.1.13:
  version "1.1.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ieee754/-/ieee754-1.1.13.tgz#ec168558e95aa181fd87d37f55c32bbcb6708b84"
  integrity sha1-7BaFWOlaoYH9h9N/VcMrvLZwi4Q=

ieee754@^1.1.4:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ieee754/-/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ignore/-/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0:
  version "7.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ignore/-/ignore-7.0.5.tgz#4cb5f6cd7d4c7ab0365738c7aea888baa6d7efd9"
  integrity sha1-TLX2zX1MerA2VzjHrqiIuqbX79k=

immer@^10.0.3, immer@^10.1.1:
  version "10.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/immer/-/immer-10.1.1.tgz#206f344ea372d8ea176891545ee53ccc062db7bc"
  integrity sha1-IG80TqNy2OoXaJFUXuU8zAYtt7w=

immutable@^5.0.2:
  version "5.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/immutable/-/immutable-5.1.3.tgz#e6486694c8b76c37c063cca92399fa64098634d4"
  integrity sha1-5khmlMi3bDfAY8ypI5n6ZAmGNNQ=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/import-fresh/-/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

import-jsx@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/import-jsx/-/import-jsx-4.0.1.tgz#30d5d336f3f52ed32b62690997f26e23c252a258"
  integrity sha1-MNXTNvP1LtMrYmkJl/JuI8JSolg=
  dependencies:
    "@babel/core" "^7.5.5"
    "@babel/plugin-proposal-object-rest-spread" "^7.5.5"
    "@babel/plugin-transform-destructuring" "^7.5.0"
    "@babel/plugin-transform-react-jsx" "^7.3.0"
    caller-path "^3.0.1"
    find-cache-dir "^3.2.0"
    make-dir "^3.0.2"
    resolve-from "^3.0.0"
    rimraf "^3.0.0"

import-lazy@~4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/import-lazy/-/import-lazy-4.0.0.tgz#e8eb627483a0a43da3c03f3e35548be5cb0cc153"
  integrity sha1-6OtidIOgpD2jwD8+NVSL5csMwVM=

import-local@^3.0.2:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/import-local/-/import-local-3.2.0.tgz#c3d5c745798c02a6f8b897726aba5100186ee260"
  integrity sha1-w9XHRXmMAqb4uJdyarpRABhu4mA=
  dependencies:
    pkg-dir "^4.2.0"
    resolve-cwd "^3.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/imurmurhash/-/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

indent-string@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/indent-string/-/indent-string-4.0.0.tgz#624f8f4497d619b2d9768531d58f4122854d7251"
  integrity sha1-Yk+PRJfWGbLZdoUx1Y9BIoVNclE=

inflight@^1.0.4:
  version "1.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/inflight/-/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2, inherits@^2.0.1, inherits@^2.0.3, inherits@~2.0.1, inherits@~2.0.3:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/inherits/-/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ink@^3.2.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ink/-/ink-3.2.0.tgz#434793630dc57d611c8fe8fffa1db6b56f1a16bb"
  integrity sha1-Q0eTYw3FfWEcj+j/+h22tW8aFrs=
  dependencies:
    ansi-escapes "^4.2.1"
    auto-bind "4.0.0"
    chalk "^4.1.0"
    cli-boxes "^2.2.0"
    cli-cursor "^3.1.0"
    cli-truncate "^2.1.0"
    code-excerpt "^3.0.0"
    indent-string "^4.0.0"
    is-ci "^2.0.0"
    lodash "^4.17.20"
    patch-console "^1.0.0"
    react-devtools-core "^4.19.1"
    react-reconciler "^0.26.2"
    scheduler "^0.20.2"
    signal-exit "^3.0.2"
    slice-ansi "^3.0.0"
    stack-utils "^2.0.2"
    string-width "^4.2.2"
    type-fest "^0.12.0"
    widest-line "^3.1.0"
    wrap-ansi "^6.2.0"
    ws "^7.5.5"
    yoga-layout-prebuilt "^1.9.6"

internal-slot@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/internal-slot/-/internal-slot-1.1.0.tgz#1eac91762947d2f7056bc838d93e13b2e9604961"
  integrity sha1-HqyRdilH0vcFa8g42T4TsulgSWE=
  dependencies:
    es-errors "^1.3.0"
    hasown "^2.0.2"
    side-channel "^1.1.0"

"internmap@1 - 2":
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/internmap/-/internmap-2.0.3.tgz#6685f23755e43c524e251d29cbc97248e3061009"
  integrity sha1-ZoXyN1XkPFJOJR0py8lySOMGEAk=

interpret@^1.0.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/interpret/-/interpret-1.4.0.tgz#665ab8bc4da27a774a40584e812e3e0fa45b1a1e"
  integrity sha1-Zlq4vE2iendKQFhOgS4+D6RbGh4=

is-arguments@^1.0.4:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-arguments/-/is-arguments-1.2.0.tgz#ad58c6aecf563b78ef2bf04df540da8f5d7d8e1b"
  integrity sha1-rVjGrs9WO3jvK/BN9UDaj119jhs=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-array-buffer@^3.0.4, is-array-buffer@^3.0.5:
  version "3.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-array-buffer/-/is-array-buffer-3.0.5.tgz#65742e1e687bd2cc666253068fd8707fe4d44280"
  integrity sha1-ZXQuHmh70sxmYlMGj9hwf+TUQoA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-arrayish/-/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-async-function@^2.0.0:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-async-function/-/is-async-function-2.1.1.tgz#3e69018c8e04e73b738793d020bfe884b9fd3523"
  integrity sha1-PmkBjI4E5ztzh5PQIL/ohLn9NSM=
  dependencies:
    async-function "^1.0.0"
    call-bound "^1.0.3"
    get-proto "^1.0.1"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-bigint@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-bigint/-/is-bigint-1.1.0.tgz#dda7a3445df57a42583db4228682eba7c4170672"
  integrity sha1-3aejRF31ekJYPbQihoLrp8QXBnI=
  dependencies:
    has-bigints "^1.0.2"

is-binary-path@~2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-binary-path/-/is-binary-path-2.1.0.tgz#ea1f7f3b80f064236e83470f86c09c254fb45b09"
  integrity sha1-6h9/O4DwZCNug0cPhsCcJU+0Wwk=
  dependencies:
    binary-extensions "^2.0.0"

is-boolean-object@^1.2.1:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-boolean-object/-/is-boolean-object-1.2.2.tgz#7067f47709809a393c71ff5bb3e135d8a9215d9e"
  integrity sha1-cGf0dwmAmjk8cf9bs+E12KkhXZ4=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-builtin-module@^3.1.0:
  version "3.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-builtin-module/-/is-builtin-module-3.2.1.tgz#f03271717d8654cfcaf07ab0463faa3571581169"
  integrity sha1-8DJxcX2GVM/K8HqwRj+qNXFYEWk=
  dependencies:
    builtin-modules "^3.3.0"

is-callable@^1.2.7:
  version "1.2.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-callable/-/is-callable-1.2.7.tgz#3bc2a85ea742d9e36205dcacdd72ca1fdc51b055"
  integrity sha1-O8KoXqdC2eNiBdys3XLKH9xRsFU=

is-ci@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-ci/-/is-ci-2.0.0.tgz#6bc6334181810e04b5c22b3d589fdca55026404c"
  integrity sha1-a8YzQYGBDgS1wis9WJ/cpVAmQEw=
  dependencies:
    ci-info "^2.0.0"

is-core-module@^2.13.0, is-core-module@^2.16.0, is-core-module@^2.8.1:
  version "2.16.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-core-module/-/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-data-view@^1.0.1, is-data-view@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-data-view/-/is-data-view-1.0.2.tgz#bae0a41b9688986c2188dda6657e56b8f9e63b8e"
  integrity sha1-uuCkG5aImGwhiN2mZX5WuPnmO44=
  dependencies:
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    is-typed-array "^1.1.13"

is-date-object@^1.0.5, is-date-object@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-date-object/-/is-date-object-1.1.0.tgz#ad85541996fc7aa8b2729701d27b7319f95d82f7"
  integrity sha1-rYVUGZb8eqiycpcB0ntzGfldgvc=
  dependencies:
    call-bound "^1.0.2"
    has-tostringtag "^1.0.2"

is-docker@^2.0.0, is-docker@^2.1.1:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-docker/-/is-docker-2.2.1.tgz#33eeabe23cfe86f14bde4408a02c0cfb853acdaa"
  integrity sha1-M+6r4jz+hvFL3kQIoCwM+4U6zao=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-extglob/-/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-finalizationregistry@^1.1.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-finalizationregistry/-/is-finalizationregistry-1.1.1.tgz#eefdcdc6c94ddd0674d9c85887bf93f944a97c90"
  integrity sha1-7v3NxslN3QZ02chYh7+T+USpfJA=
  dependencies:
    call-bound "^1.0.3"

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-generator-fn@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-generator-fn/-/is-generator-fn-2.1.0.tgz#7d140adc389aaf3011a8f2a2a4cfa6faadffb118"
  integrity sha1-fRQK3DiarzARqPKipM+m+q3/sRg=

is-generator-function@^1.0.10, is-generator-function@^1.0.7:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-generator-function/-/is-generator-function-1.1.0.tgz#bf3eeda931201394f57b5dba2800f91a238309ca"
  integrity sha1-vz7tqTEgE5T1e126KAD5GiODCco=
  dependencies:
    call-bound "^1.0.3"
    get-proto "^1.0.0"
    has-tostringtag "^1.0.2"
    safe-regex-test "^1.1.0"

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3, is-glob@~4.0.1:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-glob/-/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-map@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-map/-/is-map-2.0.3.tgz#ede96b7fe1e270b3c4465e3a465658764926d62e"
  integrity sha1-7elrf+HicLPERl46RlZYdkkm1i4=

is-negative-zero@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-negative-zero/-/is-negative-zero-2.0.3.tgz#ced903a027aca6381b777a5743069d7376a49747"
  integrity sha1-ztkDoCespjgbd3pXQwadc3akl0c=

is-number-object@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-number-object/-/is-number-object-1.1.1.tgz#144b21e95a1bc148205dcc2814a9134ec41b2541"
  integrity sha1-FEsh6VobwUggXcwoFKkTTsQbJUE=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-number@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-number/-/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-cwd@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-path-cwd/-/is-path-cwd-2.2.0.tgz#67d43b82664a7b5191fd9119127eb300048a9fdb"
  integrity sha1-Z9Q7gmZKe1GR/ZEZEn6zAASKn9s=

is-path-inside@^3.0.2, is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-path-inside/-/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-regex@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-regex/-/is-regex-1.2.1.tgz#76d70a3ed10ef9be48eb577887d74205bf0cad22"
  integrity sha1-dtcKPtEO+b5I61d4h9dCBb8MrSI=
  dependencies:
    call-bound "^1.0.2"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

is-set@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-set/-/is-set-2.0.3.tgz#8ab209ea424608141372ded6e0cb200ef1d9d01d"
  integrity sha1-irIJ6kJGCBQTct7W4MsgDvHZ0B0=

is-shared-array-buffer@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-shared-array-buffer/-/is-shared-array-buffer-1.0.4.tgz#9b67844bd9b7f246ba0708c3a93e34269c774f6f"
  integrity sha1-m2eES9m38ka6BwjDqT40Jpx3T28=
  dependencies:
    call-bound "^1.0.3"

is-stream@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-stream/-/is-stream-2.0.1.tgz#fac1e3d53b97ad5a9d0ae9cef2389f5810a5c077"
  integrity sha1-+sHj1TuXrVqdCunO8jifWBClwHc=

is-stream@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-stream/-/is-stream-3.0.0.tgz#e6bfd7aa6bef69f4f472ce9bb681e3e57b4319ac"
  integrity sha1-5r/XqmvvafT0cs6btoHj5XtDGaw=

is-string@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-string/-/is-string-1.1.1.tgz#92ea3f3d5c5b6e039ca8677e5ac8d07ea773cbb9"
  integrity sha1-kuo/PVxbbgOcqGd+WsjQfqdzy7k=
  dependencies:
    call-bound "^1.0.3"
    has-tostringtag "^1.0.2"

is-subdir@^1.1.1:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-subdir/-/is-subdir-1.2.0.tgz#b791cd28fab5202e91a08280d51d9d7254fd20d4"
  integrity sha1-t5HNKPq1IC6RoIKA1R2dclT9INQ=
  dependencies:
    better-path-resolve "1.0.0"

is-symbol@^1.0.4, is-symbol@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-symbol/-/is-symbol-1.1.1.tgz#f47761279f532e2b05a7024a7506dbbedacd0634"
  integrity sha1-9HdhJ59TLisFpwJKdQbbvtrNBjQ=
  dependencies:
    call-bound "^1.0.2"
    has-symbols "^1.1.0"
    safe-regex-test "^1.1.0"

is-typed-array@^1.1.13, is-typed-array@^1.1.14, is-typed-array@^1.1.15, is-typed-array@^1.1.3:
  version "1.1.15"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-typed-array/-/is-typed-array-1.1.15.tgz#4bfb4a45b61cee83a5a46fba778e4e8d59c0ce0b"
  integrity sha1-S/tKRbYc7oOlpG+6d45OjVnAzgs=
  dependencies:
    which-typed-array "^1.1.16"

is-weakmap@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-weakmap/-/is-weakmap-2.0.2.tgz#bf72615d649dfe5f699079c54b83e47d1ae19cfd"
  integrity sha1-v3JhXWSd/l9pkHnFS4PkfRrhnP0=

is-weakref@^1.0.2, is-weakref@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-weakref/-/is-weakref-1.1.1.tgz#eea430182be8d64174bd96bffbc46f21bf3f9293"
  integrity sha1-7qQwGCvo1kF0vZa/+8RvIb8/kpM=
  dependencies:
    call-bound "^1.0.3"

is-weakset@^2.0.3:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-weakset/-/is-weakset-2.0.4.tgz#c9f5deb0bc1906c6d6f1027f284ddf459249daca"
  integrity sha1-yfXesLwZBsbW8QJ/KE3fRZJJ2so=
  dependencies:
    call-bound "^1.0.3"
    get-intrinsic "^1.2.6"

is-windows@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-windows/-/is-windows-1.0.2.tgz#d1850eb9791ecd18e6182ce12a30f396634bb19d"
  integrity sha1-0YUOuXkezRjmGCzhKjDzlmNLsZ0=

is-wsl@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/is-wsl/-/is-wsl-2.2.0.tgz#74a4c76e77ca9fd3f932f290c17ea326cd157271"
  integrity sha1-dKTHbnfKn9P5MvKQwX6jJs0VcnE=
  dependencies:
    is-docker "^2.0.0"

isarray@0.0.1:
  version "0.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/isarray/-/isarray-0.0.1.tgz#8a18acfca9a8f4177e09abfc6038939b05d1eedf"
  integrity sha1-ihis/Kmo9Bd+Cav8YDiTmwXR7t8=

isarray@^1.0.0, isarray@~1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/isarray/-/isarray-1.0.0.tgz#bb935d48582cba168c06834957a54a3e07124f11"
  integrity sha1-u5NdSFgsuhaMBoNJV6VKPgcSTxE=

isarray@^2.0.5:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/isarray/-/isarray-2.0.5.tgz#8af1e4c1221244cc62459faf38940d4e644a5723"
  integrity sha1-ivHkwSISRMxiRZ+vOJQNTmRKVyM=

isexe@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/isexe/-/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

istanbul-lib-coverage@^3.0.0, istanbul-lib-coverage@^3.2.0, istanbul-lib-coverage@^3.2.2:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz#2d166c4b0644d43a39f04bf6c2edd1e585f31756"
  integrity sha1-LRZsSwZE1Do58Ev2wu3R5YXzF1Y=

istanbul-lib-instrument@^5.0.4:
  version "5.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz#d10c8885c2125574e1c231cacadf955675e1ce3d"
  integrity sha1-0QyIhcISVXThwjHKyt+VVnXhzj0=
  dependencies:
    "@babel/core" "^7.12.3"
    "@babel/parser" "^7.14.7"
    "@istanbuljs/schema" "^0.1.2"
    istanbul-lib-coverage "^3.2.0"
    semver "^6.3.0"

istanbul-lib-instrument@^6.0.0:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz#fa15401df6c15874bcb2105f773325d78c666765"
  integrity sha1-+hVAHfbBWHS8shBfdzMl14xmZ2U=
  dependencies:
    "@babel/core" "^7.23.9"
    "@babel/parser" "^7.23.9"
    "@istanbuljs/schema" "^0.1.3"
    istanbul-lib-coverage "^3.2.0"
    semver "^7.5.4"

istanbul-lib-report@^3.0.0, istanbul-lib-report@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz#908305bac9a5bd175ac6a74489eafd0fc2445a7d"
  integrity sha1-kIMFusmlvRdaxqdEier9D8JEWn0=
  dependencies:
    istanbul-lib-coverage "^3.0.0"
    make-dir "^4.0.0"
    supports-color "^7.1.0"

istanbul-lib-source-maps@^4.0.0:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz#895f3a709fcfba34c6de5a42939022f3e4358551"
  integrity sha1-iV86cJ/PujTG3lpCk5Ai8+Q1hVE=
  dependencies:
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"
    source-map "^0.6.1"

istanbul-lib-source-maps@^5.0.6:
  version "5.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-lib-source-maps/-/istanbul-lib-source-maps-5.0.6.tgz#acaef948df7747c8eb5fbf1265cb980f6353a441"
  integrity sha1-rK75SN93R8jrX78SZcuYD2NTpEE=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.23"
    debug "^4.1.1"
    istanbul-lib-coverage "^3.0.0"

istanbul-reports@^3.1.3, istanbul-reports@^3.1.7:
  version "3.1.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/istanbul-reports/-/istanbul-reports-3.1.7.tgz#daed12b9e1dca518e15c056e1e537e741280fa0b"
  integrity sha1-2u0SueHcpRjhXAVuHlN+dBKA+gs=
  dependencies:
    html-escaper "^2.0.0"
    istanbul-lib-report "^3.0.0"

jackspeak@^3.1.2:
  version "3.4.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jackspeak/-/jackspeak-3.4.3.tgz#8833a9d89ab4acde6188942bd1c53b6390ed5a8a"
  integrity sha1-iDOp2Jq0rN5hiJQr0cU7Y5DtWoo=
  dependencies:
    "@isaacs/cliui" "^8.0.2"
  optionalDependencies:
    "@pkgjs/parseargs" "^0.11.0"

jest-changed-files@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-changed-files/-/jest-changed-files-29.7.0.tgz#1c06d07e77c78e1585d020424dedc10d6e17ac3a"
  integrity sha1-HAbQfnfHjhWF0CBCTe3BDW4XrDo=
  dependencies:
    execa "^5.0.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"

jest-circus@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-circus/-/jest-circus-29.7.0.tgz#b6817a45fcc835d8b16d5962d0c026473ee3668a"
  integrity sha1-toF6RfzINdixbVli0MAmRz7jZoo=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/expect" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    co "^4.6.0"
    dedent "^1.0.0"
    is-generator-fn "^2.0.0"
    jest-each "^29.7.0"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-runtime "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    p-limit "^3.1.0"
    pretty-format "^29.7.0"
    pure-rand "^6.0.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-cli@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-cli/-/jest-cli-29.7.0.tgz#5592c940798e0cae677eec169264f2d839a37995"
  integrity sha1-VZLJQHmODK5nfuwWkmTy2DmjeZU=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    create-jest "^29.7.0"
    exit "^0.1.2"
    import-local "^3.0.2"
    jest-config "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    yargs "^17.3.1"

jest-config@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-config/-/jest-config-29.7.0.tgz#bcbda8806dbcc01b1e316a46bb74085a84b0245f"
  integrity sha1-vL2ogG28wBseMWpGu3QIWoSwJF8=
  dependencies:
    "@babel/core" "^7.11.6"
    "@jest/test-sequencer" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-jest "^29.7.0"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    deepmerge "^4.2.2"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-circus "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-get-type "^29.6.3"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-runner "^29.7.0"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    micromatch "^4.0.4"
    parse-json "^5.2.0"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    strip-json-comments "^3.1.1"

jest-diff@^28.1.3:
  version "28.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-diff/-/jest-diff-28.1.3.tgz#948a192d86f4e7a64c5264ad4da4877133d8792f"
  integrity sha1-lIoZLYb056ZMUmStTaSHcTPYeS8=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^28.1.1"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-diff@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-diff/-/jest-diff-29.7.0.tgz#017934a66ebb7ecf6f205e84699be10afd70458a"
  integrity sha1-AXk0pm67fs9vIF6EaZvhCv1wRYo=
  dependencies:
    chalk "^4.0.0"
    diff-sequences "^29.6.3"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-docblock@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-docblock/-/jest-docblock-29.7.0.tgz#8fddb6adc3cdc955c93e2a87f61cfd350d5d119a"
  integrity sha1-j922rcPNyVXJPiqH9hz9NQ1dEZo=
  dependencies:
    detect-newline "^3.0.0"

jest-each@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-each/-/jest-each-29.7.0.tgz#162a9b3f2328bdd991beaabffbb74745e56577d1"
  integrity sha1-FiqbPyMovdmRvqq/+7dHReVld9E=
  dependencies:
    "@jest/types" "^29.6.3"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    jest-util "^29.7.0"
    pretty-format "^29.7.0"

jest-environment-node@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-environment-node/-/jest-environment-node-29.7.0.tgz#0b93e111dda8ec120bc8300e6d1fb9576e164376"
  integrity sha1-C5PhEd2o7BILyDAObR+5V24WQ3Y=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-mock "^29.7.0"
    jest-util "^29.7.0"

jest-get-type@^28.0.2:
  version "28.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-get-type/-/jest-get-type-28.0.2.tgz#34622e628e4fdcd793d46db8a242227901fcf203"
  integrity sha1-NGIuYo5P3NeT1G24okIieQH88gM=

jest-get-type@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-get-type/-/jest-get-type-29.6.3.tgz#36f499fdcea197c1045a127319c0481723908fd1"
  integrity sha1-NvSZ/c6hl8EEWhJzGcBIFyOQj9E=

jest-haste-map@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-haste-map/-/jest-haste-map-29.7.0.tgz#3c2396524482f5a0506376e6c858c3bbcc17b104"
  integrity sha1-PCOWUkSC9aBQY3bmyFjDu8wXsQQ=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/graceful-fs" "^4.1.3"
    "@types/node" "*"
    anymatch "^3.0.3"
    fb-watchman "^2.0.0"
    graceful-fs "^4.2.9"
    jest-regex-util "^29.6.3"
    jest-util "^29.7.0"
    jest-worker "^29.7.0"
    micromatch "^4.0.4"
    walker "^1.0.8"
  optionalDependencies:
    fsevents "^2.3.2"

jest-leak-detector@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz#5b7ec0dadfdfec0ca383dc9aa016d36b5ea4c728"
  integrity sha1-W37A2t/f7Ayjg9yaoBbTa16kxyg=
  dependencies:
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-matcher-utils@^28.0.0:
  version "28.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-matcher-utils/-/jest-matcher-utils-28.1.3.tgz#5a77f1c129dd5ba3b4d7fc20728806c78893146e"
  integrity sha1-WnfxwSndW6O01/wgcogGx4iTFG4=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^28.1.3"
    jest-get-type "^28.0.2"
    pretty-format "^28.1.3"

jest-matcher-utils@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz#ae8fec79ff249fd592ce80e3ee474e83a6c44f12"
  integrity sha1-ro/sef8kn9WSzoDj7kdOg6bETxI=
  dependencies:
    chalk "^4.0.0"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    pretty-format "^29.7.0"

jest-message-util@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-message-util/-/jest-message-util-29.7.0.tgz#8bc392e204e95dfe7564abbe72a404e28e51f7f3"
  integrity sha1-i8OS4gTpXf51ZKu+cqQE4o5R9/M=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    "@jest/types" "^29.6.3"
    "@types/stack-utils" "^2.0.0"
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    micromatch "^4.0.4"
    pretty-format "^29.7.0"
    slash "^3.0.0"
    stack-utils "^2.0.3"

jest-mock@^27.3.0:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-mock/-/jest-mock-27.5.1.tgz#19948336d49ef4d9c52021d34ac7b5f36ff967d6"
  integrity sha1-GZSDNtSe9NnFICHTSse182/5Z9Y=
  dependencies:
    "@jest/types" "^27.5.1"
    "@types/node" "*"

jest-mock@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-mock/-/jest-mock-29.7.0.tgz#4e836cf60e99c6fcfabe9f99d017f3fdd50a6347"
  integrity sha1-ToNs9g6Zxvz6vp+Z0Bfz/dUKY0c=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    jest-util "^29.7.0"

jest-pnp-resolver@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz#930b1546164d4ad5937d5540e711d4d38d4cad2e"
  integrity sha1-kwsVRhZNStWTfVVA5xHU041MrS4=

jest-regex-util@^29.6.3:
  version "29.6.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-regex-util/-/jest-regex-util-29.6.3.tgz#4a556d9c776af68e1c5f48194f4d0327d24e8a52"
  integrity sha1-SlVtnHdq9o4cX0gZT00DJ9JOilI=

jest-resolve-dependencies@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz#1b04f2c095f37fc776ff40803dc92921b1e88428"
  integrity sha1-GwTywJXzf8d2/0CAPckpIbHohCg=
  dependencies:
    jest-regex-util "^29.6.3"
    jest-snapshot "^29.7.0"

jest-resolve@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-resolve/-/jest-resolve-29.7.0.tgz#64d6a8992dd26f635ab0c01e5eef4399c6bcbc30"
  integrity sha1-ZNaomS3Sb2NasMAeXu9Dmca8vDA=
  dependencies:
    chalk "^4.0.0"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-pnp-resolver "^1.2.2"
    jest-util "^29.7.0"
    jest-validate "^29.7.0"
    resolve "^1.20.0"
    resolve.exports "^2.0.0"
    slash "^3.0.0"

jest-runner@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-runner/-/jest-runner-29.7.0.tgz#809af072d408a53dcfd2e849a4c976d3132f718e"
  integrity sha1-gJrwctQIpT3P0uhJpMl20xMvcY4=
  dependencies:
    "@jest/console" "^29.7.0"
    "@jest/environment" "^29.7.0"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    emittery "^0.13.1"
    graceful-fs "^4.2.9"
    jest-docblock "^29.7.0"
    jest-environment-node "^29.7.0"
    jest-haste-map "^29.7.0"
    jest-leak-detector "^29.7.0"
    jest-message-util "^29.7.0"
    jest-resolve "^29.7.0"
    jest-runtime "^29.7.0"
    jest-util "^29.7.0"
    jest-watcher "^29.7.0"
    jest-worker "^29.7.0"
    p-limit "^3.1.0"
    source-map-support "0.5.13"

jest-runtime@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-runtime/-/jest-runtime-29.7.0.tgz#efecb3141cf7d3767a3a0cc8f7c9990587d3d817"
  integrity sha1-7+yzFBz303Z6OgzI98mZBYfT2Bc=
  dependencies:
    "@jest/environment" "^29.7.0"
    "@jest/fake-timers" "^29.7.0"
    "@jest/globals" "^29.7.0"
    "@jest/source-map" "^29.6.3"
    "@jest/test-result" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    cjs-module-lexer "^1.0.0"
    collect-v8-coverage "^1.0.0"
    glob "^7.1.3"
    graceful-fs "^4.2.9"
    jest-haste-map "^29.7.0"
    jest-message-util "^29.7.0"
    jest-mock "^29.7.0"
    jest-regex-util "^29.6.3"
    jest-resolve "^29.7.0"
    jest-snapshot "^29.7.0"
    jest-util "^29.7.0"
    slash "^3.0.0"
    strip-bom "^4.0.0"

jest-snapshot@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-snapshot/-/jest-snapshot-29.7.0.tgz#c2c574c3f51865da1bb329036778a69bf88a6be5"
  integrity sha1-wsV0w/UYZdobsykDZ3imm/iKa+U=
  dependencies:
    "@babel/core" "^7.11.6"
    "@babel/generator" "^7.7.2"
    "@babel/plugin-syntax-jsx" "^7.7.2"
    "@babel/plugin-syntax-typescript" "^7.7.2"
    "@babel/types" "^7.3.3"
    "@jest/expect-utils" "^29.7.0"
    "@jest/transform" "^29.7.0"
    "@jest/types" "^29.6.3"
    babel-preset-current-node-syntax "^1.0.0"
    chalk "^4.0.0"
    expect "^29.7.0"
    graceful-fs "^4.2.9"
    jest-diff "^29.7.0"
    jest-get-type "^29.6.3"
    jest-matcher-utils "^29.7.0"
    jest-message-util "^29.7.0"
    jest-util "^29.7.0"
    natural-compare "^1.4.0"
    pretty-format "^29.7.0"
    semver "^7.5.3"

jest-util@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-util/-/jest-util-29.7.0.tgz#23c2b62bfb22be82b44de98055802ff3710fc0bc"
  integrity sha1-I8K2K/sivoK0TemAVYAv83EPwLw=
  dependencies:
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    chalk "^4.0.0"
    ci-info "^3.2.0"
    graceful-fs "^4.2.9"
    picomatch "^2.2.3"

jest-validate@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-validate/-/jest-validate-29.7.0.tgz#7bf705511c64da591d46b15fce41400d52147d9c"
  integrity sha1-e/cFURxk2lkdRrFfzkFADVIUfZw=
  dependencies:
    "@jest/types" "^29.6.3"
    camelcase "^6.2.0"
    chalk "^4.0.0"
    jest-get-type "^29.6.3"
    leven "^3.1.0"
    pretty-format "^29.7.0"

jest-watcher@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-watcher/-/jest-watcher-29.7.0.tgz#7810d30d619c3a62093223ce6bb359ca1b28a2f2"
  integrity sha1-eBDTDWGcOmIJMiPOa7NZyhsoovI=
  dependencies:
    "@jest/test-result" "^29.7.0"
    "@jest/types" "^29.6.3"
    "@types/node" "*"
    ansi-escapes "^4.2.1"
    chalk "^4.0.0"
    emittery "^0.13.1"
    jest-util "^29.7.0"
    string-length "^4.0.1"

jest-worker@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest-worker/-/jest-worker-29.7.0.tgz#acad073acbbaeb7262bd5389e1bcf43e10058d4a"
  integrity sha1-rK0HOsu663JivVOJ4bz0PhAFjUo=
  dependencies:
    "@types/node" "*"
    jest-util "^29.7.0"
    merge-stream "^2.0.0"
    supports-color "^8.0.0"

jest@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jest/-/jest-29.7.0.tgz#994676fc24177f088f1c5e3737f5697204ff2613"
  integrity sha1-mUZ2/CQXfwiPHF43N/VpcgT/JhM=
  dependencies:
    "@jest/core" "^29.7.0"
    "@jest/types" "^29.6.3"
    import-local "^3.0.2"
    jest-cli "^29.7.0"

jiti@^1.21.6:
  version "1.21.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jiti/-/jiti-1.21.7.tgz#9dd81043424a3d28458b193d965f0d18a2300ba9"
  integrity sha1-ndgQQ0JKPShFixk9ll8NGKIwC6k=

jju@~1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jju/-/jju-1.4.0.tgz#a3abe2718af241a2b2904f84a625970f389ae32a"
  integrity sha1-o6vicYryQaKykE+EpiWXDzia4yo=

jmespath@0.16.0:
  version "0.16.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jmespath/-/jmespath-0.16.0.tgz#b15b0a85dfd4d930d43e69ed605943c802785076"
  integrity sha1-sVsKhd/U2TDUPmntYFlDyAJ4UHY=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/js-tokens/-/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-tokens@^9.0.1:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/js-tokens/-/js-tokens-9.0.1.tgz#2ec43964658435296f6761b34e10671c2d9527f4"
  integrity sha1-LsQ5ZGWENSlvZ2GzThBnHC2VJ/Q=

js-yaml@^3.13.1, js-yaml@^3.6.1:
  version "3.14.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/js-yaml/-/js-yaml-3.14.1.tgz#dae812fdb3825fa306609a8717383c50c36a0537"
  integrity sha1-2ugS/bOCX6MGYJqHFzg8UMNqBTc=
  dependencies:
    argparse "^1.0.7"
    esprima "^4.0.0"

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/js-yaml/-/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

jsdoc-type-pratt-parser@^4.0.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jsdoc-type-pratt-parser/-/jsdoc-type-pratt-parser-4.1.0.tgz#ff6b4a3f339c34a6c188cbf50a16087858d22113"
  integrity sha1-/2tKPzOcNKbBiMv1ChYIeFjSIRM=

jsesc@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jsesc/-/jsesc-3.1.0.tgz#74d335a234f67ed19907fdadfac7ccf9d409825d"
  integrity sha1-dNM1ojT2ftGZB/2t+sfM+dQJgl0=

json-buffer@3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json-buffer/-/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

json5@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json5/-/json5-1.0.2.tgz#63d98d60f21b313b77c4d6da18bfa69d80e1d593"
  integrity sha1-Y9mNYPIbMTt3xNbaGL+mnYDh1ZM=
  dependencies:
    minimist "^1.2.0"

json5@^2.2.2, json5@^2.2.3:
  version "2.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/json5/-/json5-2.2.3.tgz#78cd6f1a19bdc12b73db5ad0c61efd66c1e29283"
  integrity sha1-eM1vGhm9wStz21rQxh79ZsHikoM=

jsonfile@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jsonfile/-/jsonfile-4.0.0.tgz#8771aae0799b64076b76640fca058f9c10e33ecb"
  integrity sha1-h3Gq4HmbZAdrdmQPygWPnBDjPss=
  optionalDependencies:
    graceful-fs "^4.1.6"

jsonfile@^6.0.1, jsonfile@^6.1.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jsonfile/-/jsonfile-6.1.0.tgz#bc55b2634793c679ec6403094eb13698a6ec0aae"
  integrity sha1-vFWyY0eTxnnsZAMJTrE2mKbsCq4=
  dependencies:
    universalify "^2.0.0"
  optionalDependencies:
    graceful-fs "^4.1.6"

"jsx-ast-utils@^2.4.1 || ^3.0.0":
  version "3.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/jsx-ast-utils/-/jsx-ast-utils-3.3.5.tgz#4766bd05a8e2a11af222becd19e15575e52a853a"
  integrity sha1-R2a9BajioRryIr7NGeFVdeUqhTo=
  dependencies:
    array-includes "^3.1.6"
    array.prototype.flat "^1.3.1"
    object.assign "^4.1.4"
    object.values "^1.1.6"

keyv@^4.5.3:
  version "4.5.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/keyv/-/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

kleur@^3.0.3:
  version "3.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/kleur/-/kleur-3.0.3.tgz#a79c9ecc86ee1ce3fa6206d1216c501f147fc07e"
  integrity sha1-p5yezIbuHOP6YgbRIWxQHxR/wH4=

kolorist@^1.8.0:
  version "1.8.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/kolorist/-/kolorist-1.8.0.tgz#edddbbbc7894bc13302cdf740af6374d4a04743c"
  integrity sha1-7d27vHiUvBMwLN90CvY3TUoEdDw=

leven@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/leven/-/leven-3.1.0.tgz#77891de834064cccba82ae7842bb6b14a13ed7f2"
  integrity sha1-d4kd6DQGTMy6gq54QrtrFKE+1/I=

levn@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/levn/-/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lighthouse-logger@^1.0.0:
  version "1.4.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lighthouse-logger/-/lighthouse-logger-1.4.2.tgz#aef90f9e97cd81db367c7634292ee22079280aaa"
  integrity sha1-rvkPnpfNgds2fHY0KS7iIHkoCqo=
  dependencies:
    debug "^2.6.9"
    marky "^1.2.2"

lilconfig@^3.0.0, lilconfig@^3.1.3:
  version "3.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lilconfig/-/lilconfig-3.1.3.tgz#a1bcfd6257f9585bf5ae14ceeebb7b559025e4c4"
  integrity sha1-obz9Ylf5WFv1rhTO7rt7VZAl5MQ=

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lines-and-columns/-/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

local-pkg@^1.0.0:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/local-pkg/-/local-pkg-1.1.1.tgz#f5fe74a97a3bd3c165788ee08ca9fbe998dc58dd"
  integrity sha1-9f50qXo708FleI7gjKn76ZjcWN0=
  dependencies:
    mlly "^1.7.4"
    pkg-types "^2.0.1"
    quansync "^0.2.8"

locate-path@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/locate-path/-/locate-path-5.0.0.tgz#1afba396afd676a6d42504d0a67a3a7eb9f62aa0"
  integrity sha1-Gvujlq/WdqbUJQTQpno6frn2KqA=
  dependencies:
    p-locate "^4.1.0"

locate-path@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/locate-path/-/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.debounce@4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lodash.debounce/-/lodash.debounce-4.0.8.tgz#82d79bff30a67c4005ffd5e2515300ad9ca4d7af"
  integrity sha1-gteb/zCmfEAF/9XiUVMArZyk168=

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lodash.merge/-/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.startcase@^4.4.0:
  version "4.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lodash.startcase/-/lodash.startcase-4.4.0.tgz#9436e34ed26093ed7ffae1936144350915d9add8"
  integrity sha1-lDbjTtJgk+1/+uGTYUQ1CRXZrdg=

lodash@4.17.21, lodash@^4.17.20, lodash@^4.17.21, lodash@^4.17.3, lodash@~4.17.15:
  version "4.17.21"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lodash/-/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

loki@^0.35.1:
  version "0.35.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/loki/-/loki-0.35.1.tgz#2675c698786563c220101b2d4cc0251c4c3b0a41"
  integrity sha1-JnXGmHhlY8IgEBstTMAlHEw7CkE=
  dependencies:
    "@loki/integration-react" "^0.35.1"
    "@loki/integration-react-native" "^0.35.0"
    "@loki/integration-vue" "^0.35.0"
    "@loki/runner" "^0.35.0"
    "@loki/target-chrome-app" "^0.35.0"
    "@loki/target-chrome-docker" "^0.35.0"
    "@loki/target-native-android-emulator" "^0.35.0"
    "@loki/target-native-ios-simulator" "^0.35.0"

looks-same@^4.0.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/looks-same/-/looks-same-4.1.0.tgz#fa9593350dcddc79999fe130689860f9fb6afff2"
  integrity sha1-+pWTNQ3N3HmZn+EwaJhg+ftq//I=
  dependencies:
    color-diff "^1.1.0"
    concat-stream "^1.6.2"
    lodash "^4.17.3"
    parse-color "^1.0.0"
    pngjs "^3.3.3"

loose-envify@^1.1.0, loose-envify@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/loose-envify/-/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

lottie-react@^2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lottie-react/-/lottie-react-2.4.1.tgz#4bd3f2a8a5e48edbd43c05ca5080fdd50f049d31"
  integrity sha1-S9PyqKXkjtvUPAXKUID91Q8EnTE=
  dependencies:
    lottie-web "^5.10.2"

lottie-web@^5.10.2:
  version "5.13.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lottie-web/-/lottie-web-5.13.0.tgz#441d3df217cc8ba302338c3f168e1a3af0f221d3"
  integrity sha1-RB098hfMi6MCM4w/Fo4aOvDyIdM=

loupe@^3.1.0, loupe@^3.1.1, loupe@^3.1.2, loupe@^3.1.3:
  version "3.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/loupe/-/loupe-3.1.4.tgz#784a0060545cb38778ffb19ccde44d7870d5fdd9"
  integrity sha1-eEoAYFRcs4d4/7GczeRNeHDV/dk=

lower-case@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lower-case/-/lower-case-2.0.2.tgz#6fa237c63dbdc4a82ca0fd882e4722dc5e634e28"
  integrity sha1-b6I3xj29xKgsoP2ILkci3F5jTig=
  dependencies:
    tslib "^2.0.3"

lru-cache@^10.2.0:
  version "10.4.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lru-cache/-/lru-cache-10.4.3.tgz#410fc8a17b70e598013df257c2446b7f3383f119"
  integrity sha1-QQ/IoXtw5ZgBPfJXwkRrfzOD8Rk=

lru-cache@^5.1.1:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lru-cache/-/lru-cache-5.1.1.tgz#1da27e6710271947695daf6848e847f01d84b920"
  integrity sha1-HaJ+ZxAnGUdpXa9oSOhH8B2EuSA=
  dependencies:
    yallist "^3.0.2"

lru-cache@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lru-cache/-/lru-cache-6.0.0.tgz#6d6fe6570ebd96aaf90fcad1dafa3b2566db3a94"
  integrity sha1-bW/mVw69lqr5D8rR2vo7JWbbOpQ=
  dependencies:
    yallist "^4.0.0"

lz-string@^1.5.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/lz-string/-/lz-string-1.5.0.tgz#c1ab50f77887b712621201ba9fd4e3a6ed099941"
  integrity sha1-watQ93iHtxJiEgG6n9Tjpu0JmUE=

magic-string@^0.27.0:
  version "0.27.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/magic-string/-/magic-string-0.27.0.tgz#e4a3413b4bab6d98d2becffd48b4a257effdbbf3"
  integrity sha1-5KNBO0urbZjSvs/9SLSiV+/9u/M=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.4.13"

magic-string@^0.30.0, magic-string@^0.30.17:
  version "0.30.17"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/magic-string/-/magic-string-0.30.17.tgz#450a449673d2460e5bbcfba9a61916a1714c7453"
  integrity sha1-RQpElnPSRg5bvPupphkWoXFMdFM=
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

magicast@^0.3.5:
  version "0.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/magicast/-/magicast-0.3.5.tgz#8301c3c7d66704a0771eb1bad74274f0ec036739"
  integrity sha1-gwHDx9ZnBKB3HrG610J08OwDZzk=
  dependencies:
    "@babel/parser" "^7.25.4"
    "@babel/types" "^7.25.4"
    source-map-js "^1.2.0"

make-dir@^3.0.2:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/make-dir/-/make-dir-3.1.0.tgz#415e967046b3a7f1d185277d84aa58203726a13f"
  integrity sha1-QV6WcEazp/HRhSd9hKpYIDcmoT8=
  dependencies:
    semver "^6.0.0"

make-dir@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/make-dir/-/make-dir-4.0.0.tgz#c3c2307a771277cd9638305f915c29ae741b614e"
  integrity sha1-w8IwencSd82WODBfkVwprnQbYU4=
  dependencies:
    semver "^7.5.3"

make-error@^1.1.1:
  version "1.3.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/make-error/-/make-error-1.3.6.tgz#2eb2e37ea9b67c4891f684a1394799af484cf7a2"
  integrity sha1-LrLjfqm2fEiR9oShOUeZr0hM96I=

makeerror@1.0.12:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/makeerror/-/makeerror-1.0.12.tgz#3e5dd2079a82e812e983cc6610c4a2cb0eaa801a"
  integrity sha1-Pl3SB5qC6BLpg8xmEMSiyw6qgBo=
  dependencies:
    tmpl "1.0.5"

map-or-similar@^1.5.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/map-or-similar/-/map-or-similar-1.5.0.tgz#6de2653174adfb5d9edc33c69d3e92a1b76faf08"
  integrity sha1-beJlMXSt+12e3DPGnT6Sobdvrwg=

marky@^1.2.2:
  version "1.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/marky/-/marky-1.3.0.tgz#422b63b0baf65022f02eda61a238eccdbbc14997"
  integrity sha1-QitjsLr2UCLwLtphojjszbvBSZc=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/math-intrinsics/-/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

memoizerific@^1.11.3:
  version "1.11.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/memoizerific/-/memoizerific-1.11.3.tgz#7c87a4646444c32d75438570905f2dbd1b1a805a"
  integrity sha1-fIekZGREwy11Q4VwkF8tvRsagFo=
  dependencies:
    map-or-similar "^1.5.0"

meow@^13.0.0:
  version "13.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/meow/-/meow-13.2.0.tgz#6b7d63f913f984063b3cc261b6e8800c4cd3474f"
  integrity sha1-a31j+RP5hAY7PMJhtuiADEzTR08=

merge-stream@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/merge-stream/-/merge-stream-2.0.0.tgz#52823629a14dd00c9770fb6ad47dc6310f2c1f60"
  integrity sha1-UoI2KaFN0AyXcPtq1H3GMQ8sH2A=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/merge2/-/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.4, micromatch@^4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/micromatch/-/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

mime-db@1.52.0:
  version "1.52.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mime-db/-/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.35:
  version "2.1.35"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mime-types/-/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

mimic-fn@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mimic-fn/-/mimic-fn-2.1.0.tgz#7ed2c2ccccaf84d3ffcb7a69b57711fc2083401b"
  integrity sha1-ftLCzMyvhNP/y3pptXcR/CCDQBs=

mimic-fn@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mimic-fn/-/mimic-fn-4.0.0.tgz#60a90550d5cb0b239cca65d893b1a53b29871ecc"
  integrity sha1-YKkFUNXLCyOcymXYk7GlOymHHsw=

min-indent@^1.0.0, min-indent@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/min-indent/-/min-indent-1.0.1.tgz#a63f681673b30571fbe8bc25686ae746eefa9869"
  integrity sha1-pj9oFnOzBXH76LwlaGrnRu76mGk=

minimatch@^3.0.3, minimatch@^3.0.4, minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/minimatch/-/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.3, minimatch@^9.0.4:
  version "9.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/minimatch/-/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

minimatch@~3.0.3:
  version "3.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/minimatch/-/minimatch-3.0.8.tgz#5e6a59bd11e2ab0de1cfb843eb2d82e546c321c1"
  integrity sha1-XmpZvRHiqw3hz7hD6y2C5UbDIcE=
  dependencies:
    brace-expansion "^1.1.7"

minimist@^1.2.0, minimist@^1.2.6, minimist@^1.2.7:
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/minimist/-/minimist-1.2.8.tgz#c1a464e7693302e082a075cee0c057741ac4772c"
  integrity sha1-waRk52kzAuCCoHXO4MBXdBrEdyw=

"minipass@^5.0.0 || ^6.0.2 || ^7.0.0", minipass@^7.1.2:
  version "7.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/minipass/-/minipass-7.1.2.tgz#93a9626ce5e5e66bd4db86849e7515e92340a707"
  integrity sha1-k6libOXl5mvU24aEnnUV6SNApwc=

mkdirp@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mkdirp/-/mkdirp-1.0.4.tgz#3eb5ed62622756d79a5f0e2a221dfebad75c2f7e"
  integrity sha1-PrXtYmInVteaXw4qIh3+utdcL34=

mlly@^1.7.4:
  version "1.7.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mlly/-/mlly-1.7.4.tgz#3d7295ea2358ec7a271eaa5d000a0f84febe100f"
  integrity sha1-PXKV6iNY7HonHqpdAAoPhP6+EA8=
  dependencies:
    acorn "^8.14.0"
    pathe "^2.0.1"
    pkg-types "^1.3.0"
    ufo "^1.5.4"

mri@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mri/-/mri-1.2.0.tgz#6721480fec2a11a4889861115a48b6cbe7cc8f0b"
  integrity sha1-ZyFID+wqEaSImGERWki2y+fMjws=

mrmime@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mrmime/-/mrmime-2.0.1.tgz#bc3e87f7987853a54c9850eeb1f1078cd44adddc"
  integrity sha1-vD6H95h4U6VMmFDusfEHjNRK3dw=

ms@2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ms/-/ms-2.0.0.tgz#5608aeadfc00be6c2901df5f9861788de0d597c8"
  integrity sha1-VgiurfwAvmwpAd9fmGF4jeDVl8g=

ms@^2.1.1, ms@^2.1.3:
  version "2.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ms/-/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

muggle-string@^0.4.1:
  version "0.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/muggle-string/-/muggle-string-0.4.1.tgz#3b366bd43b32f809dc20659534dd30e7c8a0d328"
  integrity sha1-OzZr1Dsy+AncIGWVNN0w58ig0yg=

mz@^2.7.0:
  version "2.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/mz/-/mz-2.7.0.tgz#95008057a56cafadc2bc63dde7f9ff6955948e32"
  integrity sha1-lQCAV6Vsr63CvGPd5/n/aVWUjjI=
  dependencies:
    any-promise "^1.0.0"
    object-assign "^4.0.1"
    thenify-all "^1.0.0"

nanoid@^3.3.11:
  version "3.3.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/nanoid/-/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/natural-compare/-/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

no-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/no-case/-/no-case-3.0.4.tgz#d361fd5c9800f558551a8369fc0dcd4662b6124d"
  integrity sha1-02H9XJgA9VhVGoNp/A3NRmK2Ek0=
  dependencies:
    lower-case "^2.0.2"
    tslib "^2.0.3"

node-int64@^0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/node-int64/-/node-int64-0.4.0.tgz#87a9065cdb355d3182d8f94ce11188b825c68a3b"
  integrity sha1-h6kGXNs1XTGC2PlM4RGIuCXGijs=

node-releases@^2.0.19:
  version "2.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/node-releases/-/node-releases-2.0.19.tgz#9e445a52950951ec4d177d843af370b411caf314"
  integrity sha1-nkRaUpUJUexNF32EOvNwtBHK8xQ=

noms@0.0.0:
  version "0.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/noms/-/noms-0.0.0.tgz#da8ebd9f3af9d6760919b27d9cdc8092a7332859"
  integrity sha1-2o69nzr51nYJGbJ9nNyAkqczKFk=
  dependencies:
    inherits "^2.0.1"
    readable-stream "~1.0.31"

normalize-package-data@^2.5.0:
  version "2.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/normalize-package-data/-/normalize-package-data-2.5.0.tgz#e66db1838b200c1dfc233225d12cb36520e234a8"
  integrity sha1-5m2xg4sgDB38IzIl0SyzZSDiNKg=
  dependencies:
    hosted-git-info "^2.1.4"
    resolve "^1.10.0"
    semver "2 || 3 || 4 || 5"
    validate-npm-package-license "^3.0.1"

normalize-path@^3.0.0, normalize-path@~3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/normalize-path/-/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

normalize-range@^0.1.2:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/normalize-range/-/normalize-range-0.1.2.tgz#2d10c06bdfd312ea9777695a4d28439456b75942"
  integrity sha1-LRDAa9/TEuqXd2laTShDlFa3WUI=

npm-run-path@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/npm-run-path/-/npm-run-path-4.0.1.tgz#b7ecd1e5ed53da8e37a55e1c2269e0b97ed748ea"
  integrity sha1-t+zR5e1T2o43pV4cImnguX7XSOo=
  dependencies:
    path-key "^3.0.0"

npm-run-path@^5.1.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/npm-run-path/-/npm-run-path-5.3.0.tgz#e23353d0ebb9317f174e93417e4a4d82d0249e9f"
  integrity sha1-4jNT0Ou5MX8XTpNBfkpNgtAknp8=
  dependencies:
    path-key "^4.0.0"

object-assign@^4.0.1, object-assign@^4.1.1:
  version "4.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object-assign/-/object-assign-4.1.1.tgz#2109adc7965887cfc05cbbd442cac8bfbb360863"
  integrity sha1-IQmtx5ZYh8/AXLvUQsrIv7s2CGM=

object-hash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object-hash/-/object-hash-3.0.0.tgz#73f97f753e7baffc0e2cc9d6e079079744ac82e9"
  integrity sha1-c/l/dT57r/wOLMnW4HkHl0Ssguk=

object-inspect@^1.13.3, object-inspect@^1.13.4:
  version "1.13.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object-inspect/-/object-inspect-1.13.4.tgz#8375265e21bc20d0fa582c22e1b13485d6e00213"
  integrity sha1-g3UmXiG8IND6WCwi4bE0hdbgAhM=

object-keys@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object-keys/-/object-keys-1.1.1.tgz#1c47f272df277f3b1daf061677d9c82e2322c60e"
  integrity sha1-HEfyct8nfzsdrwYWd9nILiMixg4=

object.assign@^4.1.4, object.assign@^4.1.7:
  version "4.1.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object.assign/-/object.assign-4.1.7.tgz#8c14ca1a424c6a561b0bb2a22f66f5049a945d3d"
  integrity sha1-jBTKGkJMalYbC7KiL2b1BJqUXT0=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"
    has-symbols "^1.1.0"
    object-keys "^1.1.1"

object.entries@^1.1.5:
  version "1.1.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object.entries/-/object.entries-1.1.9.tgz#e4770a6a1444afb61bd39f984018b5bede25f8b3"
  integrity sha1-5HcKahREr7Yb05+YQBi1vt4l+LM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    define-properties "^1.2.1"
    es-object-atoms "^1.1.1"

object.fromentries@^2.0.5:
  version "2.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object.fromentries/-/object.fromentries-2.0.8.tgz#f7195d8a9b97bd95cbc1999ea939ecd1a2b00c65"
  integrity sha1-9xldipuXvZXLwZmeqTns0aKwDGU=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.hasown@^1.1.0:
  version "1.1.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object.hasown/-/object.hasown-1.1.4.tgz#e270ae377e4c120cdcb7656ce66884a6218283dc"
  integrity sha1-4nCuN35MEgzct2Vs5miEpiGCg9w=
  dependencies:
    define-properties "^1.2.1"
    es-abstract "^1.23.2"
    es-object-atoms "^1.0.0"

object.values@^1.1.5, object.values@^1.1.6:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/object.values/-/object.values-1.2.1.tgz#deed520a50809ff7f75a7cfd4bc64c7a038c6216"
  integrity sha1-3u1SClCAn/f3Wnz9S8ZMegOMYhY=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

once@^1.3.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/once/-/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

onetime@^5.1.0, onetime@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/onetime/-/onetime-5.1.2.tgz#d0e96ebb56b07476df1dd9c4806e5237985ca45e"
  integrity sha1-0Oluu1awdHbfHdnEgG5SN5hcpF4=
  dependencies:
    mimic-fn "^2.1.0"

onetime@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/onetime/-/onetime-6.0.0.tgz#7c24c18ed1fd2e9bca4bd26806a33613c77d34b4"
  integrity sha1-fCTBjtH9LpvKS9JoBqM2E8d9NLQ=
  dependencies:
    mimic-fn "^4.0.0"

open@^8.0.4:
  version "8.4.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/open/-/open-8.4.2.tgz#5b5ffe2a8f793dcd2aad73e550cb87b59cb084f9"
  integrity sha1-W1/+Ko95Pc0qrXPlUMuHtZywhPk=
  dependencies:
    define-lazy-prop "^2.0.0"
    is-docker "^2.1.1"
    is-wsl "^2.2.0"

optionator@^0.9.3:
  version "0.9.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/optionator/-/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

os-tmpdir@~1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/os-tmpdir/-/os-tmpdir-1.0.2.tgz#bbe67406c79aa85c5cfec766fe5734555dfa1274"
  integrity sha1-u+Z0BseaqFxc/sdm/lc0VV36EnQ=

outdent@^0.5.0:
  version "0.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/outdent/-/outdent-0.5.0.tgz#9e10982fdc41492bb473ad13840d22f9655be2ff"
  integrity sha1-nhCYL9xBSSu0c60ThA0i+WVb4v8=

own-keys@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/own-keys/-/own-keys-1.0.1.tgz#e4006910a2bf913585289676eebd6f390cf51358"
  integrity sha1-5ABpEKK/kTWFKJZ27r1vOQz1E1g=
  dependencies:
    get-intrinsic "^1.2.6"
    object-keys "^1.1.1"
    safe-push-apply "^1.0.0"

p-filter@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-filter/-/p-filter-2.1.0.tgz#1b1472562ae7a0f742f0f3d3d3718ea66ff9c09c"
  integrity sha1-GxRyVirnoPdC8PPT03GOpm/5wJw=
  dependencies:
    p-map "^2.0.0"

p-limit@^2.2.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-limit/-/p-limit-2.3.0.tgz#3dd33c647a214fdfffd835933eb086da0dc21db1"
  integrity sha1-PdM8ZHohT9//2DWTPrCG2g3CHbE=
  dependencies:
    p-try "^2.0.0"

p-limit@^3.0.2, p-limit@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-limit/-/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-locate/-/p-locate-4.1.0.tgz#a3428bb7088b3a60292f66919278b7c297ad4f07"
  integrity sha1-o0KLtwiLOmApL2aRkni3wpetTwc=
  dependencies:
    p-limit "^2.2.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-locate/-/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

p-map@^2.0.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-map/-/p-map-2.1.0.tgz#310928feef9c9ecc65b68b17693018a665cea175"
  integrity sha1-MQko/u+cnsxltosXaTAYpmXOoXU=

p-map@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-map/-/p-map-4.0.0.tgz#bb2f95a5eda2ec168ec9274e06a747c3e2904d2b"
  integrity sha1-uy+Vpe2i7BaOySdOBqdHw+KQTSs=
  dependencies:
    aggregate-error "^3.0.0"

p-try@^2.0.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/p-try/-/p-try-2.2.0.tgz#cb2868540e313d61de58fafbe35ce9004d5540e6"
  integrity sha1-yyhoVA4xPWHeWPr741zpAE1VQOY=

package-json-from-dist@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz#4f1471a010827a86f94cfd9b0727e36d267de505"
  integrity sha1-TxRxoBCCeob5TP2bByfjbSZ95QU=

package-manager-detector@^0.2.0:
  version "0.2.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/package-manager-detector/-/package-manager-detector-0.2.11.tgz#3af0b34f99d86d24af0a0620603d2e1180d05c9c"
  integrity sha1-OvCzT5nYbSSvCgYgYD0uEYDQXJw=
  dependencies:
    quansync "^0.2.7"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/parent-module/-/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-color@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/parse-color/-/parse-color-1.0.0.tgz#7b748b95a83f03f16a94f535e52d7f3d94658619"
  integrity sha1-e3SLlag/A/FqlPU15S1/PZRlhhk=
  dependencies:
    color-convert "~0.5.0"

parse-json@^5.0.0, parse-json@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/parse-json/-/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

patch-console@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/patch-console/-/patch-console-1.0.0.tgz#19b9f028713feb8a3c023702a8cc8cb9f7466f9d"
  integrity sha1-GbnwKHE/64o8AjcCqMyMufdGb50=

path-browserify@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-browserify/-/path-browserify-1.0.1.tgz#d98454a9c3753d5790860f16f68867b9e46be1fd"
  integrity sha1-2YRUqcN1PVeQhg8W9ohnueRr4f0=

path-exists@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-exists/-/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-is-absolute/-/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.0.0, path-key@^3.1.0:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-key/-/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-key@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-key/-/path-key-4.0.0.tgz#295588dc3aee64154f877adb9d780b81c554bf18"
  integrity sha1-KVWI3DruZBVPh3rbnXgLgcVUvxg=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-parse/-/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

path-scurry@^1.11.1:
  version "1.11.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-scurry/-/path-scurry-1.11.1.tgz#7960a668888594a0720b12a911d1a742ab9f11d2"
  integrity sha1-eWCmaIiFlKByCxKpEdGnQqufEdI=
  dependencies:
    lru-cache "^10.2.0"
    minipass "^5.0.0 || ^6.0.2 || ^7.0.0"

path-type@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/path-type/-/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

pathe@^2.0.1, pathe@^2.0.3:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pathe/-/pathe-2.0.3.tgz#3ecbec55421685b70a9da872b2cff3e1cbed1716"
  integrity sha1-PsvsVUIWhbcKnahyss/z4cvtFxY=

pathval@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pathval/-/pathval-2.0.0.tgz#7e2550b422601d4f6b8e26f1301bc8f15a741a25"
  integrity sha1-fiVQtCJgHU9rjibxMBvI8Vp0GiU=

performance-now@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/performance-now/-/performance-now-2.1.0.tgz#6309f4e0e5fa913ec1c69307ae364b4b377c9e7b"
  integrity sha1-Ywn04OX6kT7BxpMHrjZLSzd8nns=

picocolors@^1.1.0, picocolors@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/picocolors/-/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.0.4, picomatch@^2.2.1, picomatch@^2.2.3, picomatch@^2.3.1:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/picomatch/-/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

picomatch@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/picomatch/-/picomatch-4.0.2.tgz#77c742931e8f3b8820946c76cd0c1f13730d1dab"
  integrity sha1-d8dCkx6PO4gglGx2zQwfE3MNHas=

pify@^2.3.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pify/-/pify-2.3.0.tgz#ed141a6ac043a849ea588498e7dca8b15330e90c"
  integrity sha1-7RQaasBDqEnqWISY59yosVMw6Qw=

pify@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pify/-/pify-4.0.1.tgz#4b2cd25c50d598735c50292224fd8c6df41e3231"
  integrity sha1-SyzSXFDVmHNcUCkiJP2MbfQeMjE=

pirates@^4.0.1, pirates@^4.0.4:
  version "4.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pirates/-/pirates-4.0.7.tgz#643b4a18c4257c8a65104b73f3049ce9a0a15e22"
  integrity sha1-ZDtKGMQlfIplEEtz8wSc6aChXiI=

pixelmatch@^5.2.0:
  version "5.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pixelmatch/-/pixelmatch-5.3.0.tgz#5e5321a7abedfb7962d60dbf345deda87cb9560a"
  integrity sha1-XlMhp6vt+3li1g2/NF3tqHy5Vgo=
  dependencies:
    pngjs "^6.0.0"

pkg-dir@^4.1.0, pkg-dir@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pkg-dir/-/pkg-dir-4.2.0.tgz#f099133df7ede422e81d1d8448270eeb3e4261f3"
  integrity sha1-8JkTPfft5CLoHR2ESCcO6z5CYfM=
  dependencies:
    find-up "^4.0.0"

pkg-types@^1.3.0:
  version "1.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pkg-types/-/pkg-types-1.3.1.tgz#bd7cc70881192777eef5326c19deb46e890917df"
  integrity sha1-vXzHCIEZJ3fu9TJsGd60bokJF98=
  dependencies:
    confbox "^0.1.8"
    mlly "^1.7.4"
    pathe "^2.0.1"

pkg-types@^2.0.1:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pkg-types/-/pkg-types-2.1.0.tgz#70c9e1b9c74b63fdde749876ee0aa007ea9edead"
  integrity sha1-cMnhucdLY/3edJh27gqgB+qe3q0=
  dependencies:
    confbox "^0.2.1"
    exsolve "^1.0.1"
    pathe "^2.0.3"

pluralize@^8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pluralize/-/pluralize-8.0.0.tgz#1a6fa16a38d12a1901e0320fa017051c539ce3b1"
  integrity sha1-Gm+hajjRKhkB4DIPoBcFHFOc47E=

pngjs@^3.3.3:
  version "3.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pngjs/-/pngjs-3.4.0.tgz#99ca7d725965fb655814eaf65f38f12bbdbf555f"
  integrity sha1-mcp9clll+2VYFOr2XzjxK72/VV8=

pngjs@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pngjs/-/pngjs-4.0.1.tgz#f803869bb2fc1bfe1bf99aa4ec21c108117cfdbe"
  integrity sha1-+AOGm7L8G/4b+Zqk7CHBCBF8/b4=

pngjs@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pngjs/-/pngjs-6.0.0.tgz#ca9e5d2aa48db0228a52c419c3308e87720da821"
  integrity sha1-yp5dKqSNsCKKUsQZwzCOh3INqCE=

polished@^4.2.2:
  version "4.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/polished/-/polished-4.3.1.tgz#5a00ae32715609f83d89f6f31d0f0261c6170548"
  integrity sha1-WgCuMnFWCfg9ifbzHQ8CYcYXBUg=
  dependencies:
    "@babel/runtime" "^7.17.8"

possible-typed-array-names@^1.0.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/possible-typed-array-names/-/possible-typed-array-names-1.1.0.tgz#93e3582bc0e5426586d9d07b79ee40fc841de4ae"
  integrity sha1-k+NYK8DlQmWG2dB7ee5A/IQd5K4=

postcss-attribute-case-insensitive@^6.0.3:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-attribute-case-insensitive/-/postcss-attribute-case-insensitive-6.0.3.tgz#d118023911a768dfccfc0b0147f5ff06d8485806"
  integrity sha1-0RgCORGnaN/M/AsBR/X/BthIWAY=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-clamp@^4.1.0:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-clamp/-/postcss-clamp-4.1.0.tgz#7263e95abadd8c2ba1bd911b0b5a5c9c93e02363"
  integrity sha1-cmPpWrrdjCuhvZEbC1pcnJPgI2M=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-color-functional-notation@^6.0.14:
  version "6.0.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-color-functional-notation/-/postcss-color-functional-notation-6.0.14.tgz#958d8fc434fafbb15ebc7964053f19d366773078"
  integrity sha1-lY2PxDT6+7FevHlkBT8Z02Z3MHg=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

postcss-color-hex-alpha@^9.0.4:
  version "9.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-color-hex-alpha/-/postcss-color-hex-alpha-9.0.4.tgz#f455902fb222453b2eb9699dfa9fc17a9c056f1e"
  integrity sha1-9FWQL7IiRTsuuWmd+p/BepwFbx4=
  dependencies:
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

postcss-color-rebeccapurple@^9.0.3:
  version "9.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-color-rebeccapurple/-/postcss-color-rebeccapurple-9.0.3.tgz#63e14d9b9ab196e62e3491606a2b77a9531a6825"
  integrity sha1-Y+FNm5qxluYuNJFgait3qVMaaCU=
  dependencies:
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

postcss-custom-media@^10.0.8:
  version "10.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-custom-media/-/postcss-custom-media-10.0.8.tgz#0b84916522eb1e8a4b9e3ecd2bce292844cd7323"
  integrity sha1-C4SRZSLrHopLnj7NK84pKETNcyM=
  dependencies:
    "@csstools/cascade-layer-name-parser" "^1.0.13"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/media-query-list-parser" "^2.1.13"

postcss-custom-properties@^13.3.12:
  version "13.3.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-custom-properties/-/postcss-custom-properties-13.3.12.tgz#e21960c7d13aed960b28236412d4da67f75317b0"
  integrity sha1-4hlgx9E67ZYLKCNkEtTaZ/dTF7A=
  dependencies:
    "@csstools/cascade-layer-name-parser" "^1.0.13"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

postcss-custom-selectors@^7.1.12:
  version "7.1.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-custom-selectors/-/postcss-custom-selectors-7.1.12.tgz#4d1bac2469003aad3aa3d73481a1b7a45290852b"
  integrity sha1-TRusJGkAOq06o9c0gaG3pFKQhSs=
  dependencies:
    "@csstools/cascade-layer-name-parser" "^1.0.13"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    postcss-selector-parser "^6.1.0"

postcss-dir-pseudo-class@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-dir-pseudo-class/-/postcss-dir-pseudo-class-8.0.1.tgz#b93755f52fb90215301b1d3ecb7c5e6416930a1e"
  integrity sha1-uTdV9S+5AhUwGx0+y3xeZBaTCh4=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-double-position-gradients@^5.0.7:
  version "5.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-double-position-gradients/-/postcss-double-position-gradients-5.0.7.tgz#1a4841daf7ac04e94de4672282e8d02d1b3dd274"
  integrity sha1-GkhB2vesBOlN5GcigujQLRs90nQ=
  dependencies:
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

postcss-focus-visible@^9.0.1:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-focus-visible/-/postcss-focus-visible-9.0.1.tgz#eede1032ce86b3bb2556d93ca5df63c68dfc2559"
  integrity sha1-7t4QMs6Gs7slVtk8pd9jxo38JVk=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-focus-within@^8.0.1:
  version "8.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-focus-within/-/postcss-focus-within-8.0.1.tgz#524af4c7eabae35cb1efa220a7903016fcc897fa"
  integrity sha1-Ukr0x+q641yx76Igp5AwFvzIl/o=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-font-variant@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-font-variant/-/postcss-font-variant-5.0.0.tgz#efd59b4b7ea8bb06127f2d031bfbb7f24d32fa66"
  integrity sha1-79WbS36ouwYSfy0DG/u38k0y+mY=

postcss-gap-properties@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-gap-properties/-/postcss-gap-properties-5.0.1.tgz#887b64655f42370b43f0ab266cc6dbabf504d276"
  integrity sha1-iHtkZV9CNwtD8KsmbMbbq/UE0nY=

postcss-image-set-function@^6.0.3:
  version "6.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-image-set-function/-/postcss-image-set-function-6.0.3.tgz#84c5e32cc1085198f2cf4a786028dae8a2632bb2"
  integrity sha1-hMXjLMEIUZjyz0p4YCja6KJjK7I=
  dependencies:
    "@csstools/utilities" "^1.0.0"
    postcss-value-parser "^4.2.0"

postcss-import@^15.1.0:
  version "15.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-import/-/postcss-import-15.1.0.tgz#41c64ed8cc0e23735a9698b3249ffdbf704adc70"
  integrity sha1-QcZO2MwOI3NalpizJJ/9v3BK3HA=
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-import@^16.1.0:
  version "16.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-import/-/postcss-import-16.1.1.tgz#cfbe79e6c9232b0dbbe1c18f35308825cfe8ff2a"
  integrity sha1-z7555skjKw274cGPNTCIJc/o/yo=
  dependencies:
    postcss-value-parser "^4.0.0"
    read-cache "^1.0.0"
    resolve "^1.1.7"

postcss-js@^4.0.1:
  version "4.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-js/-/postcss-js-4.0.1.tgz#61598186f3703bab052f1c4f7d805f3991bee9d2"
  integrity sha1-YVmBhvNwO6sFLxxPfYBfOZG+6dI=
  dependencies:
    camelcase-css "^2.0.1"

postcss-lab-function@^6.0.19:
  version "6.0.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-lab-function/-/postcss-lab-function-6.0.19.tgz#09b04c016bfbacd8576988a73dc19c0fdbeae2c4"
  integrity sha1-CbBMAWv7rNhXaYinPcGcD9vq4sQ=
  dependencies:
    "@csstools/css-color-parser" "^2.0.4"
    "@csstools/css-parser-algorithms" "^2.7.1"
    "@csstools/css-tokenizer" "^2.4.1"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/utilities" "^1.0.0"

postcss-load-config@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-load-config/-/postcss-load-config-4.0.2.tgz#7159dcf626118d33e299f485d6afe4aff7c4a3e3"
  integrity sha1-cVnc9iYRjTPimfSF1q/kr/fEo+M=
  dependencies:
    lilconfig "^3.0.0"
    yaml "^2.3.4"

postcss-logical@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-logical/-/postcss-logical-7.0.1.tgz#a3121f6510591b195321b16e65fbe13b1cfd3115"
  integrity sha1-oxIfZRBZGxlTIbFuZfvhOxz9MRU=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-nested@^6.2.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-nested/-/postcss-nested-6.2.0.tgz#4c2d22ab5f20b9cb61e2c5c5915950784d068131"
  integrity sha1-TC0iq18gucth4sXFkVlQeE0GgTE=
  dependencies:
    postcss-selector-parser "^6.1.1"

postcss-nesting@^12.1.5:
  version "12.1.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-nesting/-/postcss-nesting-12.1.5.tgz#e5e2dc1d63e6166c194da45aa28c04d4024db98f"
  integrity sha1-5eLcHWPmFmwZTaRaoowE1AJNuY8=
  dependencies:
    "@csstools/selector-resolve-nested" "^1.1.0"
    "@csstools/selector-specificity" "^3.1.1"
    postcss-selector-parser "^6.1.0"

postcss-opacity-percentage@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-opacity-percentage/-/postcss-opacity-percentage-2.0.0.tgz#c0a56060cd4586e3f954dbde1efffc2deed53002"
  integrity sha1-wKVgYM1FhuP5VNveHv/8Le7VMAI=

postcss-overflow-shorthand@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-overflow-shorthand/-/postcss-overflow-shorthand-5.0.1.tgz#c0a124edad4f7ad88109275a60510e1fb07ab833"
  integrity sha1-wKEk7a1PetiBCSdaYFEOH7B6uDM=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-page-break@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-page-break/-/postcss-page-break-3.0.4.tgz#7fbf741c233621622b68d435babfb70dd8c1ee5f"
  integrity sha1-f790HCM2IWIraNQ1ur+3DdjB7l8=

postcss-place@^9.0.1:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-place/-/postcss-place-9.0.1.tgz#c08c46a94e639c1ee3457ac96d50c50a89bd6ac3"
  integrity sha1-wIxGqU5jnB7jRXrJbVDFCom9asM=
  dependencies:
    postcss-value-parser "^4.2.0"

postcss-preset-env@^9.5.14:
  version "9.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-preset-env/-/postcss-preset-env-9.6.0.tgz#da5fc8606f95092b2788c3bdf6d4fc053e50075b"
  integrity sha1-2l/IYG+VCSsniMO99tT8BT5QB1s=
  dependencies:
    "@csstools/postcss-cascade-layers" "^4.0.6"
    "@csstools/postcss-color-function" "^3.0.19"
    "@csstools/postcss-color-mix-function" "^2.0.19"
    "@csstools/postcss-content-alt-text" "^1.0.0"
    "@csstools/postcss-exponential-functions" "^1.0.9"
    "@csstools/postcss-font-format-keywords" "^3.0.2"
    "@csstools/postcss-gamut-mapping" "^1.0.11"
    "@csstools/postcss-gradients-interpolation-method" "^4.0.20"
    "@csstools/postcss-hwb-function" "^3.0.18"
    "@csstools/postcss-ic-unit" "^3.0.7"
    "@csstools/postcss-initial" "^1.0.1"
    "@csstools/postcss-is-pseudo-class" "^4.0.8"
    "@csstools/postcss-light-dark-function" "^1.0.8"
    "@csstools/postcss-logical-float-and-clear" "^2.0.1"
    "@csstools/postcss-logical-overflow" "^1.0.1"
    "@csstools/postcss-logical-overscroll-behavior" "^1.0.1"
    "@csstools/postcss-logical-resize" "^2.0.1"
    "@csstools/postcss-logical-viewport-units" "^2.0.11"
    "@csstools/postcss-media-minmax" "^1.1.8"
    "@csstools/postcss-media-queries-aspect-ratio-number-values" "^2.0.11"
    "@csstools/postcss-nested-calc" "^3.0.2"
    "@csstools/postcss-normalize-display-values" "^3.0.2"
    "@csstools/postcss-oklab-function" "^3.0.19"
    "@csstools/postcss-progressive-custom-properties" "^3.3.0"
    "@csstools/postcss-relative-color-syntax" "^2.0.19"
    "@csstools/postcss-scope-pseudo-class" "^3.0.1"
    "@csstools/postcss-stepped-value-functions" "^3.0.10"
    "@csstools/postcss-text-decoration-shorthand" "^3.0.7"
    "@csstools/postcss-trigonometric-functions" "^3.0.10"
    "@csstools/postcss-unset-value" "^3.0.1"
    autoprefixer "^10.4.19"
    browserslist "^4.23.1"
    css-blank-pseudo "^6.0.2"
    css-has-pseudo "^6.0.5"
    css-prefers-color-scheme "^9.0.1"
    cssdb "^8.1.0"
    postcss-attribute-case-insensitive "^6.0.3"
    postcss-clamp "^4.1.0"
    postcss-color-functional-notation "^6.0.14"
    postcss-color-hex-alpha "^9.0.4"
    postcss-color-rebeccapurple "^9.0.3"
    postcss-custom-media "^10.0.8"
    postcss-custom-properties "^13.3.12"
    postcss-custom-selectors "^7.1.12"
    postcss-dir-pseudo-class "^8.0.1"
    postcss-double-position-gradients "^5.0.7"
    postcss-focus-visible "^9.0.1"
    postcss-focus-within "^8.0.1"
    postcss-font-variant "^5.0.0"
    postcss-gap-properties "^5.0.1"
    postcss-image-set-function "^6.0.3"
    postcss-lab-function "^6.0.19"
    postcss-logical "^7.0.1"
    postcss-nesting "^12.1.5"
    postcss-opacity-percentage "^2.0.0"
    postcss-overflow-shorthand "^5.0.1"
    postcss-page-break "^3.0.4"
    postcss-place "^9.0.1"
    postcss-pseudo-class-any-link "^9.0.2"
    postcss-replace-overflow-wrap "^4.0.0"
    postcss-selector-not "^7.0.2"

postcss-pseudo-class-any-link@^9.0.2:
  version "9.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-pseudo-class-any-link/-/postcss-pseudo-class-any-link-9.0.2.tgz#e436a7db1421f8a347fff3f19951a27d4e791987"
  integrity sha1-5Dan2xQh+KNH//PxmVGifU55GYc=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-replace-overflow-wrap@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-replace-overflow-wrap/-/postcss-replace-overflow-wrap-4.0.0.tgz#d2df6bed10b477bf9c52fab28c568b4b29ca4319"
  integrity sha1-0t9r7RC0d7+cUvqyjFaLSynKQxk=

postcss-selector-not@^7.0.2:
  version "7.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-selector-not/-/postcss-selector-not-7.0.2.tgz#f9184c7770be5dcb4abd7efa3610a15fbd2f0b31"
  integrity sha1-+RhMd3C+XctKvX76NhChX70vCzE=
  dependencies:
    postcss-selector-parser "^6.0.13"

postcss-selector-parser@^6.0.13, postcss-selector-parser@^6.1.0, postcss-selector-parser@^6.1.1, postcss-selector-parser@^6.1.2:
  version "6.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-selector-parser/-/postcss-selector-parser-6.1.2.tgz#27ecb41fb0e3b6ba7a1ec84fff347f734c7929de"
  integrity sha1-J+y0H7Djtrp6HshP/zR/c0x5Kd4=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-value-parser@^4.0.0, postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.43, postcss@^8.4.47, postcss@^8.5.3:
  version "8.5.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/postcss/-/postcss-8.5.6.tgz#2825006615a619b4f62a9e7426cc120b349a8f3c"
  integrity sha1-KCUAZhWmGbT2Kp50JswSCzSajzw=
  dependencies:
    nanoid "^3.3.11"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prefix-style@2.0.1:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prefix-style/-/prefix-style-2.0.1.tgz#66bba9a870cfda308a5dc20e85e9120932c95a06"
  integrity sha1-ZrupqHDP2jCKXcIOhekSCTLJWgY=

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prelude-ls/-/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

prettier-linter-helpers@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prettier-linter-helpers/-/prettier-linter-helpers-1.0.0.tgz#d23d41fe1375646de2d0104d3454a3008802cf7b"
  integrity sha1-0j1B/hN1ZG3i0BBNNFSjAIgCz3s=
  dependencies:
    fast-diff "^1.1.2"

prettier-plugin-tailwindcss@^0.6.5:
  version "0.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prettier-plugin-tailwindcss/-/prettier-plugin-tailwindcss-0.6.12.tgz#280cf901facf18014c79b8641f69623892006f0b"
  integrity sha1-KAz5AfrPGAFMebhkH2liOJIAbws=

prettier@^2.7.1:
  version "2.8.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prettier/-/prettier-2.8.8.tgz#e8c5d7e98a4305ffe3de2e1fc4aca1a71c28b1da"
  integrity sha1-6MXX6YpDBf/j3i4fxKyhpxwosdo=

prettier@^3.2.4:
  version "3.5.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prettier/-/prettier-3.5.3.tgz#4fc2ce0d657e7a02e602549f053b239cb7dfe1b5"
  integrity sha1-T8LODWV+egLmAlSfBTsjnLff4bU=

pretty-format@^27.0.2:
  version "27.5.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pretty-format/-/pretty-format-27.5.1.tgz#2181879fdea51a7a5851fb39d920faa63f01d88e"
  integrity sha1-IYGHn96lGnpYUfs52SD6pj8B2I4=
  dependencies:
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^17.0.1"

pretty-format@^28.0.0, pretty-format@^28.1.3:
  version "28.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pretty-format/-/pretty-format-28.1.3.tgz#c9fba8cedf99ce50963a11b27d982a9ae90970d5"
  integrity sha1-yfuozt+ZzlCWOhGyfZgqmukJcNU=
  dependencies:
    "@jest/schemas" "^28.1.3"
    ansi-regex "^5.0.1"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

pretty-format@^29.7.0:
  version "29.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pretty-format/-/pretty-format-29.7.0.tgz#ca42c758310f365bfa71a0bda0a807160b776812"
  integrity sha1-ykLHWDEPNlv6caC9oKgHFgt3aBI=
  dependencies:
    "@jest/schemas" "^29.6.3"
    ansi-styles "^5.0.0"
    react-is "^18.0.0"

prism-react-renderer@^2.4.1:
  version "2.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prism-react-renderer/-/prism-react-renderer-2.4.1.tgz#ac63b7f78e56c8f2b5e76e823a976d5ede77e35f"
  integrity sha1-rGO3945WyPK1526COpdtXt53418=
  dependencies:
    "@types/prismjs" "^1.26.0"
    clsx "^2.0.0"

process-nextick-args@~2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/process-nextick-args/-/process-nextick-args-2.0.1.tgz#7820d9b16120cc55ca9ae7792680ae7dba6d7fe2"
  integrity sha1-eCDZsWEgzFXKmud5JoCufbptf+I=

process@^0.11.10:
  version "0.11.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/process/-/process-0.11.10.tgz#7332300e840161bda3e69a1d1d91a7d4bc16f182"
  integrity sha1-czIwDoQBYb2j5podHZGn1LwW8YI=

prompts@^2.0.1:
  version "2.4.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prompts/-/prompts-2.4.2.tgz#7b57e73b3a48029ad10ebd44f74b01722a4cb069"
  integrity sha1-e1fnOzpIAprRDr1E90sBcipMsGk=
  dependencies:
    kleur "^3.0.3"
    sisteransi "^1.0.5"

prop-types@^15.5.10, prop-types@^15.5.8, prop-types@^15.6.2, prop-types@^15.8.1:
  version "15.8.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/prop-types/-/prop-types-15.8.1.tgz#67d87bf1a694f48435cf332c24af10214a3140b5"
  integrity sha1-Z9h78aaU9IQ1zzMsJK8QIUoxQLU=
  dependencies:
    loose-envify "^1.4.0"
    object-assign "^4.1.1"
    react-is "^16.13.1"

property-expr@^2.0.5:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/property-expr/-/property-expr-2.0.6.tgz#f77bc00d5928a6c748414ad12882e83f24aec1e8"
  integrity sha1-93vADVkopsdIQUrRKILoPySuweg=

punycode@1.3.2:
  version "1.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/punycode/-/punycode-1.3.2.tgz#9653a036fb7c1ee42342f2325cceefea3926c48d"
  integrity sha1-llOgNvt8HuQjQvIyXM7v6jkmxI0=

punycode@^2.1.0:
  version "2.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/punycode/-/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

pure-rand@^6.0.0:
  version "6.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/pure-rand/-/pure-rand-6.1.0.tgz#d173cf23258231976ccbdb05247c9787957604f2"
  integrity sha1-0XPPIyWCMZdsy9sFJHyXh5V2BPI=

quansync@^0.2.7, quansync@^0.2.8:
  version "0.2.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/quansync/-/quansync-0.2.10.tgz#32053cf166fa36511aae95fc49796116f2dc20e1"
  integrity sha1-MgU88Wb6NlEarpX8SXlhFvLcIOE=

querystring@0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/querystring/-/querystring-0.2.0.tgz#b209849203bb25df820da756e747005878521620"
  integrity sha1-sgmEkgO7Jd+CDadW50cAWHhSFiA=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/queue-microtask/-/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

raf@^3.1.0:
  version "3.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/raf/-/raf-3.4.1.tgz#0742e99a4a6552f445d73e3ee0328af0ff1ede39"
  integrity sha1-B0LpmkplUvRF1z4+4DKK8P8e3jk=
  dependencies:
    performance-now "^2.1.0"

ramda@^0.27.1:
  version "0.27.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ramda/-/ramda-0.27.2.tgz#84463226f7f36dc33592f6f4ed6374c48306c3f1"
  integrity sha1-hEYyJvfzbcM1kvb07WN0xIMGw/E=

react-clientside-effect@^1.2.6, react-clientside-effect@^1.2.7:
  version "1.2.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-clientside-effect/-/react-clientside-effect-1.2.8.tgz#0b90a9d7b2a1823a3a10ed1ea3f651f7e0301cb7"
  integrity sha1-C5Cp17Khgjo6EO0eo/ZR9+AwHLc=
  dependencies:
    "@babel/runtime" "^7.12.13"

react-confetti@^6.1.0:
  version "6.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-confetti/-/react-confetti-6.4.0.tgz#e9416b5b3c8baf6f0bb1c5a8e1e3c89babd2c837"
  integrity sha1-6UFrWzyLr28LscWo4ePIm6vSyDc=
  dependencies:
    tween-functions "^1.2.0"

react-custom-scrollbars-2@^4.5.0:
  version "4.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-custom-scrollbars-2/-/react-custom-scrollbars-2-4.5.0.tgz#cff18e7368bce9d570aea0be780045eda392c745"
  integrity sha1-z/GOc2i86dVwrqC+eABF7aOSx0U=
  dependencies:
    dom-css "^2.0.0"
    prop-types "^15.5.10"
    raf "^3.1.0"

react-day-picker@9.7.0:
  version "9.7.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-day-picker/-/react-day-picker-9.7.0.tgz#8d8d90c0539a31be5b01fa3613d9341c05513485"
  integrity sha1-jY2QwFOaMb5bAfo2E9k0HAVRNIU=
  dependencies:
    "@date-fns/tz" "1.2.0"
    date-fns "4.1.0"
    date-fns-jalali "4.1.0-0"

react-devtools-core@^4.19.1:
  version "4.28.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-devtools-core/-/react-devtools-core-4.28.5.tgz#c8442b91f068cdf0c899c543907f7f27d79c2508"
  integrity sha1-yEQrkfBozfDImcVDkH9/J9ecJQg=
  dependencies:
    shell-quote "^1.6.1"
    ws "^7"

react-docgen-typescript@^2.2.2:
  version "2.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-docgen-typescript/-/react-docgen-typescript-2.4.0.tgz#033428b4a6a639d050ac8baf2a5195c596521713"
  integrity sha1-AzQotKamOdBQrIuvKlGVxZZSFxM=

react-docgen@^7.0.0:
  version "7.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-docgen/-/react-docgen-7.1.1.tgz#a7a8e6b923a945acf0b7325a889ddd74fec74a63"
  integrity sha1-p6jmuSOpRazwtzJaiJ3ddP7HSmM=
  dependencies:
    "@babel/core" "^7.18.9"
    "@babel/traverse" "^7.18.9"
    "@babel/types" "^7.18.9"
    "@types/babel__core" "^7.18.0"
    "@types/babel__traverse" "^7.18.0"
    "@types/doctrine" "^0.0.9"
    "@types/resolve" "^1.20.2"
    doctrine "^3.0.0"
    resolve "^1.22.1"
    strip-indent "^4.0.0"

react-dom@18.2.0:
  version "18.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-dom/-/react-dom-18.2.0.tgz#22aaf38708db2674ed9ada224ca4aa708d821e3d"
  integrity sha1-IqrzhwjbJnTtmtoiTKSqcI2CHj0=
  dependencies:
    loose-envify "^1.1.0"
    scheduler "^0.23.0"

"react-dom@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0":
  version "19.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-dom/-/react-dom-19.1.0.tgz#133558deca37fa1d682708df8904b25186793623"
  integrity sha1-EzVY3so3+h1oJwjfiQSyUYZ5NiM=
  dependencies:
    scheduler "^0.26.0"

react-fast-compare@3.2.2, react-fast-compare@^3.2.2:
  version "3.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-fast-compare/-/react-fast-compare-3.2.2.tgz#929a97a532304ce9fee4bcae44234f1ce2c21d49"
  integrity sha1-kpqXpTIwTOn+5LyuRCNPHOLCHUk=

react-focus-lock@2.13.5:
  version "2.13.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-focus-lock/-/react-focus-lock-2.13.5.tgz#68b01618ef3a4717746a02e223afe9d86a69a95e"
  integrity sha1-aLAWGO86Rxd0agLiI6/p2GppqV4=
  dependencies:
    "@babel/runtime" "^7.0.0"
    focus-lock "^1.3.5"
    prop-types "^15.6.2"
    react-clientside-effect "^1.2.6"
    use-callback-ref "^1.3.2"
    use-sidecar "^1.1.2"

react-focus-lock@2.13.6:
  version "2.13.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-focus-lock/-/react-focus-lock-2.13.6.tgz#29751bf2e4e30f6248673cd87a347c74ff2af672"
  integrity sha1-KXUb8uTjD2JIZzzYejR8dP8q9nI=
  dependencies:
    "@babel/runtime" "^7.0.0"
    focus-lock "^1.3.6"
    prop-types "^15.6.2"
    react-clientside-effect "^1.2.7"
    use-callback-ref "^1.3.3"
    use-sidecar "^1.1.3"

react-hook-form@7.54.2:
  version "7.54.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-hook-form/-/react-hook-form-7.54.2.tgz#8c26ed54c71628dff57ccd3c074b1dd377cfb211"
  integrity sha1-jCbtVMcWKN/1fM08B0sd03fPshE=

react-infinite-scroller@1.2.6:
  version "1.2.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-infinite-scroller/-/react-infinite-scroller-1.2.6.tgz#8b80233226dc753a597a0eb52621247f49b15f18"
  integrity sha1-i4AjMibcdTpZeg61JiEkf0mxXxg=
  dependencies:
    prop-types "^15.5.8"

react-is@^16.13.1, react-is@^16.7.0:
  version "16.13.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-is/-/react-is-16.13.1.tgz#789729a4dc36de2999dc156dd6c1d9c18cea56a4"
  integrity sha1-eJcppNw23imZ3BVt1sHZwYzqVqQ=

react-is@^17.0.1:
  version "17.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-is/-/react-is-17.0.2.tgz#e691d4a8e9c789365655539ab372762b0efb54f0"
  integrity sha1-5pHUqOnHiTZWVVOas3J2Kw77VPA=

react-is@^18.0.0:
  version "18.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-is/-/react-is-18.3.1.tgz#e83557dc12eae63a99e003a46388b1dcbb44db7e"
  integrity sha1-6DVX3BLq5jqZ4AOkY4ix3LtE234=

react-reconciler@^0.26.2:
  version "0.26.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-reconciler/-/react-reconciler-0.26.2.tgz#bbad0e2d1309423f76cf3c3309ac6c96e05e9d91"
  integrity sha1-u60OLRMJQj92zzwzCaxsluBenZE=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"
    scheduler "^0.20.2"

"react-redux@8.x.x || 9.x.x":
  version "9.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-redux/-/react-redux-9.2.0.tgz#96c3ab23fb9a3af2cb4654be4b51c989e32366f5"
  integrity sha1-lsOrI/uaOvLLRlS+S1HJieMjZvU=
  dependencies:
    "@types/use-sync-external-store" "^0.0.6"
    use-sync-external-store "^1.4.0"

react-refresh@^0.17.0:
  version "0.17.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-refresh/-/react-refresh-0.17.0.tgz#b7e579c3657f23d04eccbe4ad2e58a8ed51e7e53"
  integrity sha1-t+V5w2V/I9BOzL5K0uWKjtUeflM=

react-transition-group@4.4.5:
  version "4.4.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react-transition-group/-/react-transition-group-4.4.5.tgz#e53d4e3f3344da8521489fbef8f2581d42becdd1"
  integrity sha1-5T1OPzNE2oUhSJ+++PJYHUK+zdE=
  dependencies:
    "@babel/runtime" "^7.5.5"
    dom-helpers "^5.0.1"
    loose-envify "^1.4.0"
    prop-types "^15.6.2"

react@18.2.0:
  version "18.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react/-/react-18.2.0.tgz#555bd98592883255fa00de14f1151a917b5d77d5"
  integrity sha1-VVvZhZKIMlX6AN4U8RUakXtdd9U=
  dependencies:
    loose-envify "^1.1.0"

"react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0":
  version "19.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react/-/react-19.1.0.tgz#926864b6c48da7627f004795d6cce50e90793b75"
  integrity sha1-kmhktsSNp2J/AEeV1szlDpB5O3U=

react@^17.0.2:
  version "17.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/react/-/react-17.0.2.tgz#d0b5cc516d29eb3eee383f75b62864cfb6800037"
  integrity sha1-0LXMUW0p6z7uOD91tihkz7aAADc=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

read-cache@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/read-cache/-/read-cache-1.0.0.tgz#e664ef31161166c9751cdbe8dbcf86b5fb58f774"
  integrity sha1-5mTvMRYRZsl1HNvo28+GtftY93Q=
  dependencies:
    pify "^2.3.0"

read-pkg-up@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/read-pkg-up/-/read-pkg-up-7.0.1.tgz#f3a6135758459733ae2b95638056e1854e7ef507"
  integrity sha1-86YTV1hFlzOuK5VjgFbhhU5+9Qc=
  dependencies:
    find-up "^4.1.0"
    read-pkg "^5.2.0"
    type-fest "^0.8.1"

read-pkg@^5.2.0:
  version "5.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/read-pkg/-/read-pkg-5.2.0.tgz#7bf295438ca5a33e56cd30e053b34ee7250c93cc"
  integrity sha1-e/KVQ4yloz5WzTDgU7NO5yUMk8w=
  dependencies:
    "@types/normalize-package-data" "^2.4.0"
    normalize-package-data "^2.5.0"
    parse-json "^5.0.0"
    type-fest "^0.6.0"

read-yaml-file@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/read-yaml-file/-/read-yaml-file-1.1.0.tgz#9362bbcbdc77007cc8ea4519fe1c0b821a7ce0d8"
  integrity sha1-k2K7y9x3AHzI6kUZ/hwLghp84Ng=
  dependencies:
    graceful-fs "^4.1.5"
    js-yaml "^3.6.1"
    pify "^4.0.1"
    strip-bom "^3.0.0"

readable-stream@^2.2.2, readable-stream@~2.3.6:
  version "2.3.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/readable-stream/-/readable-stream-2.3.8.tgz#91125e8042bba1b9887f49345f6277027ce8be9b"
  integrity sha1-kRJegEK7obmIf0k0X2J3Anzovps=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.3"
    isarray "~1.0.0"
    process-nextick-args "~2.0.0"
    safe-buffer "~5.1.1"
    string_decoder "~1.1.1"
    util-deprecate "~1.0.1"

readable-stream@~1.0.31:
  version "1.0.34"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/readable-stream/-/readable-stream-1.0.34.tgz#125820e34bc842d2f2aaafafe4c2916ee32c157c"
  integrity sha1-Elgg40vIQtLyqq+v5MKRbuMsFXw=
  dependencies:
    core-util-is "~1.0.0"
    inherits "~2.0.1"
    isarray "0.0.1"
    string_decoder "~0.10.x"

readdirp@~3.6.0:
  version "3.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/readdirp/-/readdirp-3.6.0.tgz#74a370bd857116e245b29cc97340cd431a02a6c7"
  integrity sha1-dKNwvYVxFuJFspzJc0DNQxoCpsc=
  dependencies:
    picomatch "^2.2.1"

recast@^0.23.5:
  version "0.23.11"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/recast/-/recast-0.23.11.tgz#8885570bb28cf773ba1dc600da7f502f7883f73f"
  integrity sha1-iIVXC7KM93O6HcYA2n9QL3iD9z8=
  dependencies:
    ast-types "^0.16.1"
    esprima "~4.0.0"
    source-map "~0.6.1"
    tiny-invariant "^1.3.3"
    tslib "^2.0.1"

recharts@*:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/recharts/-/recharts-3.1.0.tgz#8c97bc3b9d21c844052220c88d37d9913947342d"
  integrity sha1-jJe8O50hyEQFIiDIjTfZkTlHNC0=
  dependencies:
    "@reduxjs/toolkit" "1.x.x || 2.x.x"
    clsx "^2.1.1"
    decimal.js-light "^2.5.1"
    es-toolkit "^1.39.3"
    eventemitter3 "^5.0.1"
    immer "^10.1.1"
    react-redux "8.x.x || 9.x.x"
    reselect "5.1.1"
    tiny-invariant "^1.3.3"
    use-sync-external-store "^1.2.2"
    victory-vendor "^37.0.2"

recharts@3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/recharts/-/recharts-3.0.2.tgz#f81f411f57d5e41a9ab9fc5817be4a58a2181046"
  integrity sha1-+B9BH1fV5BqaufxYF75KWKIYEEY=
  dependencies:
    "@reduxjs/toolkit" "1.x.x || 2.x.x"
    clsx "^2.1.1"
    decimal.js-light "^2.5.1"
    es-toolkit "^1.39.3"
    eventemitter3 "^5.0.1"
    immer "^10.1.1"
    react-redux "8.x.x || 9.x.x"
    reselect "5.1.1"
    tiny-invariant "^1.3.3"
    use-sync-external-store "^1.2.2"
    victory-vendor "^37.0.2"

rechoir@^0.6.2:
  version "0.6.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/rechoir/-/rechoir-0.6.2.tgz#85204b54dba82d5742e28c96756ef43af50e3384"
  integrity sha1-hSBLVNuoLVdC4oyWdW70OvUOM4Q=
  dependencies:
    resolve "^1.1.6"

redent@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/redent/-/redent-3.0.0.tgz#e557b7998316bb53c9f1f56fa626352c6963059f"
  integrity sha1-5Ve3mYMWu1PJ8fVvpiY1LGljBZ8=
  dependencies:
    indent-string "^4.0.0"
    strip-indent "^3.0.0"

redux-thunk@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/redux-thunk/-/redux-thunk-3.1.0.tgz#94aa6e04977c30e14e892eae84978c1af6058ff3"
  integrity sha1-lKpuBJd8MOFOiS6uhJeMGvYFj/M=

redux@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/redux/-/redux-5.0.1.tgz#97fa26881ce5746500125585d5642c77b6e9447b"
  integrity sha1-l/omiBzldGUAElWF1WQsd7bpRHs=

reflect.getprototypeof@^1.0.6, reflect.getprototypeof@^1.0.9:
  version "1.0.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/reflect.getprototypeof/-/reflect.getprototypeof-1.0.10.tgz#c629219e78a3316d8b604c765ef68996964e7bf9"
  integrity sha1-xikhnnijMW2LYEx2XvaJlpZOe/k=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-abstract "^1.23.9"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.7"
    get-proto "^1.0.1"
    which-builtin-type "^1.2.1"

regexp-tree@^0.1.24, regexp-tree@~0.1.1:
  version "0.1.27"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/regexp-tree/-/regexp-tree-0.1.27.tgz#2198f0ef54518ffa743fe74d983b56ffd631b6cd"
  integrity sha1-IZjw71RRj/p0P+dNmDtW/9Yxts0=

regexp.prototype.flags@^1.5.3, regexp.prototype.flags@^1.5.4:
  version "1.5.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/regexp.prototype.flags/-/regexp.prototype.flags-1.5.4.tgz#1ad6c62d44a259007e55b3970e00f746efbcaa19"
  integrity sha1-GtbGLUSiWQB+VbOXDgD3Ru+8qhk=
  dependencies:
    call-bind "^1.0.8"
    define-properties "^1.2.1"
    es-errors "^1.3.0"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    set-function-name "^2.0.2"

require-directory@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/require-directory/-/require-directory-2.1.1.tgz#8c64ad5fd30dab1c976e2344ffe7f792a6a6df42"
  integrity sha1-jGStX9MNqxyXbiNE/+f3kqam30I=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/require-from-string/-/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

reselect@5.1.1, reselect@^5.1.0:
  version "5.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/reselect/-/reselect-5.1.1.tgz#c766b1eb5d558291e5e550298adb0becc24bb72e"
  integrity sha1-x2ax611VgpHl5VApitsL7MJLty4=

resolve-cwd@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve-cwd/-/resolve-cwd-3.0.0.tgz#0f0075f1bb2544766cf73ba6a6e2adfebcb13f2d"
  integrity sha1-DwB18bslRHZs9zumpuKt/ryxPy0=
  dependencies:
    resolve-from "^5.0.0"

resolve-from@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve-from/-/resolve-from-3.0.0.tgz#b22c7af7d9d6881bc8b6e653335eebcb0a188748"
  integrity sha1-six699nWiBvItuZTM17rywoYh0g=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve-from/-/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve-from/-/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

resolve-pkg-maps@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve-pkg-maps/-/resolve-pkg-maps-1.0.0.tgz#616b3dc2c57056b5588c31cdf4b3d64db133720f"
  integrity sha1-YWs9wsVwVrVYjDHN9LPWTbEzcg8=

resolve.exports@^2.0.0:
  version "2.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve.exports/-/resolve.exports-2.0.3.tgz#41955e6f1b4013b7586f873749a635dea07ebe3f"
  integrity sha1-QZVebxtAE7dYb4c3SaY13qB+vj8=

resolve@^1.1.6, resolve@^1.1.7, resolve@^1.10.0, resolve@^1.20.0, resolve@^1.22.0, resolve@^1.22.1, resolve@^1.22.4, resolve@^1.22.8, resolve@~1.22.1, resolve@~1.22.2:
  version "1.22.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve/-/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

resolve@^2.0.0-next.3:
  version "2.0.0-next.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/resolve/-/resolve-2.0.0-next.5.tgz#6b0ec3107e671e52b68cd068ef327173b90dc03c"
  integrity sha1-aw7DEH5nHlK2jNBo7zJxc7kNwDw=
  dependencies:
    is-core-module "^2.13.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

restore-cursor@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/restore-cursor/-/restore-cursor-3.1.0.tgz#39f67c54b3a7a58cea5236d95cf0034239631f7e"
  integrity sha1-OfZ8VLOnpYzqUjbZXPADQjljH34=
  dependencies:
    onetime "^5.1.0"
    signal-exit "^3.0.2"

reusify@^1.0.4:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/reusify/-/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^3.0.0, rimraf@^3.0.2:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/rimraf/-/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

rollup@^4.20.0, rollup@^4.34.9:
  version "4.43.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/rollup/-/rollup-4.43.0.tgz#275c09119eb7eaf0c3dea040523b81ef43c57b8c"
  integrity sha1-J1wJEZ636vDD3qBAUjuB70PFe4w=
  dependencies:
    "@types/estree" "1.0.7"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.43.0"
    "@rollup/rollup-android-arm64" "4.43.0"
    "@rollup/rollup-darwin-arm64" "4.43.0"
    "@rollup/rollup-darwin-x64" "4.43.0"
    "@rollup/rollup-freebsd-arm64" "4.43.0"
    "@rollup/rollup-freebsd-x64" "4.43.0"
    "@rollup/rollup-linux-arm-gnueabihf" "4.43.0"
    "@rollup/rollup-linux-arm-musleabihf" "4.43.0"
    "@rollup/rollup-linux-arm64-gnu" "4.43.0"
    "@rollup/rollup-linux-arm64-musl" "4.43.0"
    "@rollup/rollup-linux-loongarch64-gnu" "4.43.0"
    "@rollup/rollup-linux-powerpc64le-gnu" "4.43.0"
    "@rollup/rollup-linux-riscv64-gnu" "4.43.0"
    "@rollup/rollup-linux-riscv64-musl" "4.43.0"
    "@rollup/rollup-linux-s390x-gnu" "4.43.0"
    "@rollup/rollup-linux-x64-gnu" "4.43.0"
    "@rollup/rollup-linux-x64-musl" "4.43.0"
    "@rollup/rollup-win32-arm64-msvc" "4.43.0"
    "@rollup/rollup-win32-ia32-msvc" "4.43.0"
    "@rollup/rollup-win32-x64-msvc" "4.43.0"
    fsevents "~2.3.2"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/run-parallel/-/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

rxjs@^7.4.0:
  version "7.8.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/rxjs/-/rxjs-7.8.2.tgz#955bc473ed8af11a002a2be52071bf475638607b"
  integrity sha1-lVvEc+2K8RoAKivlIHG/R1Y4YHs=
  dependencies:
    tslib "^2.1.0"

safe-array-concat@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safe-array-concat/-/safe-array-concat-1.1.3.tgz#c9e54ec4f603b0bbb8e7e5007a5ee7aecd1538c3"
  integrity sha1-yeVOxPYDsLu45+UAel7nrs0VOMM=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    get-intrinsic "^1.2.6"
    has-symbols "^1.1.0"
    isarray "^2.0.5"

safe-buffer@~5.1.0, safe-buffer@~5.1.1:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safe-buffer/-/safe-buffer-5.1.2.tgz#991ec69d296e0313747d59bdfd2b745c35f8828d"
  integrity sha1-mR7GnSluAxN0fVm9/St0XDX4go0=

safe-push-apply@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safe-push-apply/-/safe-push-apply-1.0.0.tgz#01850e981c1602d398c85081f360e4e6d03d27f5"
  integrity sha1-AYUOmBwWAtOYyFCB82Dk5tA9J/U=
  dependencies:
    es-errors "^1.3.0"
    isarray "^2.0.5"

safe-regex-test@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safe-regex-test/-/safe-regex-test-1.1.0.tgz#7f87dfb67a3150782eaaf18583ff5d1711ac10c1"
  integrity sha1-f4fftnoxUHguqvGFg/9dFxGsEME=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    is-regex "^1.2.1"

safe-regex@^2.1.1:
  version "2.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safe-regex/-/safe-regex-2.1.1.tgz#f7128f00d056e2fe5c11e81a1324dd974aadced2"
  integrity sha1-9xKPANBW4v5cEegaEyTdl0qtztI=
  dependencies:
    regexp-tree "~0.1.1"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/safer-buffer/-/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

sass-embedded-android-arm64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-android-arm64/-/sass-embedded-android-arm64-1.86.0.tgz#176423c639ac0f26c045a6f4dbc1fb18a3ae00cc"
  integrity sha1-F2QjxjmsDybARab028H7GKOuAMw=

sass-embedded-android-arm@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-android-arm/-/sass-embedded-android-arm-1.86.0.tgz#0664724b1b14bab552aaec3267a6258ebfc6eb99"
  integrity sha1-BmRySxsUurVSquwyZ6Yljr/G65k=

sass-embedded-android-ia32@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-android-ia32/-/sass-embedded-android-ia32-1.86.0.tgz#b47043998cca8aa6f83cd9ee06e41712a6e563db"
  integrity sha1-tHBDmYzKiqb4PNnuBuQXEqblY9s=

sass-embedded-android-riscv64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-android-riscv64/-/sass-embedded-android-riscv64-1.86.0.tgz#f9d8828e92c0f722d23f5e2c450d9747777c8acc"
  integrity sha1-+diCjpLA9yLSP14sRQ2XR3d8isw=

sass-embedded-android-x64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-android-x64/-/sass-embedded-android-x64-1.86.0.tgz#d5f518e363fb74093cf9e0d7d0961eb98dbd80ff"
  integrity sha1-1fUY42P7dAk8+eDX0JYeuY29gP8=

sass-embedded-darwin-arm64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-darwin-arm64/-/sass-embedded-darwin-arm64-1.86.0.tgz#27c95876078e1769c02762eeccd958ad1764d612"
  integrity sha1-J8lYdgeOF2nAJ2LuzNlYrRdk1hI=

sass-embedded-darwin-x64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-darwin-x64/-/sass-embedded-darwin-x64-1.86.0.tgz#6bb7c9a4582a6d12238868465fef71528de635f2"
  integrity sha1-a7fJpFgqbRIjiGhGX+9xUo3mNfI=

sass-embedded-linux-arm64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-arm64/-/sass-embedded-linux-arm64-1.86.0.tgz#64f3f0caf1001602f4ec211d13f42fea19e62da8"
  integrity sha1-ZPPwyvEAFgL07CEdE/Qv6hnmLag=

sass-embedded-linux-arm@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-arm/-/sass-embedded-linux-arm-1.86.0.tgz#566cb78300f311e823133829ee4bd5077900a2f2"
  integrity sha1-Vmy3gwDzEegjEzgp7kvVB3kAovI=

sass-embedded-linux-ia32@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-ia32/-/sass-embedded-linux-ia32-1.86.0.tgz#b8e9f79c8da5353717078009ea33b659f8776470"
  integrity sha1-uOn3nI2lNTcXB4AJ6jO2Wfh3ZHA=

sass-embedded-linux-musl-arm64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-musl-arm64/-/sass-embedded-linux-musl-arm64-1.86.0.tgz#1276122fff9171b3493d28c0e8cde13548816562"
  integrity sha1-EnYSL/+RcbNJPSjA6M3hNUiBZWI=

sass-embedded-linux-musl-arm@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-musl-arm/-/sass-embedded-linux-musl-arm-1.86.0.tgz#f6e448a6f15dd0f37a72ff097311e034ee173b8e"
  integrity sha1-9uRIpvFd0PN6cv8JcxHgNO4XO44=

sass-embedded-linux-musl-ia32@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-musl-ia32/-/sass-embedded-linux-musl-ia32-1.86.0.tgz#6c2188a621ee4634373b865607eda2553ff5361f"
  integrity sha1-bCGIpiHuRjQ3O4ZWB+2iVT/1Nh8=

sass-embedded-linux-musl-riscv64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-musl-riscv64/-/sass-embedded-linux-musl-riscv64-1.86.0.tgz#67440171c30c38721dcaf145a143bc324af12599"
  integrity sha1-Z0QBccMMOHIdyvFFoUO8MkrxJZk=

sass-embedded-linux-musl-x64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-musl-x64/-/sass-embedded-linux-musl-x64-1.86.0.tgz#08b6b66f0ed7cf169a0f417b76f923579ec9458c"
  integrity sha1-CLa2bw7XzxaaD0F7dvkjV57JRYw=

sass-embedded-linux-riscv64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-riscv64/-/sass-embedded-linux-riscv64-1.86.0.tgz#be6c53cd044be437e4e059f4d0b23834f8dec020"
  integrity sha1-vmxTzQRL5Dfk4Fn00LI4NPjewCA=

sass-embedded-linux-x64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-linux-x64/-/sass-embedded-linux-x64-1.86.0.tgz#406a1ceaff89c4b04c2a06cc5556cb1ba031ec44"
  integrity sha1-QGoc6v+JxLBMKgbMVVbLG6Ax7EQ=

sass-embedded-win32-arm64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-win32-arm64/-/sass-embedded-win32-arm64-1.86.0.tgz#bd7217c0ee6ea900bfaf5d95fc5ead8e532cd668"
  integrity sha1-vXIXwO5uqQC/r12V/F6tjlMs1mg=

sass-embedded-win32-ia32@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-win32-ia32/-/sass-embedded-win32-ia32-1.86.0.tgz#d6eed647d48a1f8fb8ecf4b5cf24254d90fccd5c"
  integrity sha1-1u7WR9SKH4+47PS1zyQlTZD8zVw=

sass-embedded-win32-x64@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded-win32-x64/-/sass-embedded-win32-x64-1.86.0.tgz#1425fddaca6817af9ef26e3dc1c4c6376ce75b9a"
  integrity sha1-FCX92spoF6+e8m49wcTGN2znW5o=

sass-embedded@1.86.0:
  version "1.86.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sass-embedded/-/sass-embedded-1.86.0.tgz#c05c6141f1aea407f9a4eea78790209fdc74f0df"
  integrity sha1-wFxhQfGupAf5pO6nh5Agn9x08N8=
  dependencies:
    "@bufbuild/protobuf" "^2.0.0"
    buffer-builder "^0.2.0"
    colorjs.io "^0.5.0"
    immutable "^5.0.2"
    rxjs "^7.4.0"
    supports-color "^8.1.1"
    sync-child-process "^1.0.2"
    varint "^6.0.0"
  optionalDependencies:
    sass-embedded-android-arm "1.86.0"
    sass-embedded-android-arm64 "1.86.0"
    sass-embedded-android-ia32 "1.86.0"
    sass-embedded-android-riscv64 "1.86.0"
    sass-embedded-android-x64 "1.86.0"
    sass-embedded-darwin-arm64 "1.86.0"
    sass-embedded-darwin-x64 "1.86.0"
    sass-embedded-linux-arm "1.86.0"
    sass-embedded-linux-arm64 "1.86.0"
    sass-embedded-linux-ia32 "1.86.0"
    sass-embedded-linux-musl-arm "1.86.0"
    sass-embedded-linux-musl-arm64 "1.86.0"
    sass-embedded-linux-musl-ia32 "1.86.0"
    sass-embedded-linux-musl-riscv64 "1.86.0"
    sass-embedded-linux-musl-x64 "1.86.0"
    sass-embedded-linux-riscv64 "1.86.0"
    sass-embedded-linux-x64 "1.86.0"
    sass-embedded-win32-arm64 "1.86.0"
    sass-embedded-win32-ia32 "1.86.0"
    sass-embedded-win32-x64 "1.86.0"

sax@1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sax/-/sax-1.2.1.tgz#7b8e656190b228e81a66aea748480d828cd2d37a"
  integrity sha1-e45lYZCyKOgaZq6nSEgNgozS03o=

sax@>=0.6.0:
  version "1.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sax/-/sax-1.4.1.tgz#44cc8988377f126304d3b3fc1010c733b929ef0f"
  integrity sha1-RMyJiDd/EmME07P8EBDHM7kp7w8=

scheduler@^0.20.2:
  version "0.20.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/scheduler/-/scheduler-0.20.2.tgz#4baee39436e34aa93b4874bddcbf0fe8b8b50e91"
  integrity sha1-S67jlDbjSqk7SHS93L8P6Li1DpE=
  dependencies:
    loose-envify "^1.1.0"
    object-assign "^4.1.1"

scheduler@^0.23.0:
  version "0.23.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/scheduler/-/scheduler-0.23.2.tgz#414ba64a3b282892e944cf2108ecc078d115cdc3"
  integrity sha1-QUumSjsoKJLpRM8hCOzAeNEVzcM=
  dependencies:
    loose-envify "^1.1.0"

scheduler@^0.26.0:
  version "0.26.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/scheduler/-/scheduler-0.26.0.tgz#4ce8a8c2a2095f13ea11bf9a445be50c555d6337"
  integrity sha1-TOiowqIJXxPqEb+aRFvlDFVdYzc=

"semver@2 || 3 || 4 || 5":
  version "5.7.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/semver/-/semver-5.7.2.tgz#48d55db737c3287cd4835e17fa13feace1c41ef8"
  integrity sha1-SNVdtzfDKHzUg14X+hP+rOHEHvg=

semver@^6.0.0, semver@^6.3.0, semver@^6.3.1:
  version "6.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/semver/-/semver-6.3.1.tgz#556d2ef8689146e46dcea4bfdd095f3434dffcb4"
  integrity sha1-VW0u+GiRRuRtzqS/3QlfNDTf/LQ=

semver@^7.3.4, semver@^7.3.5, semver@^7.5.0, semver@^7.5.3, semver@^7.5.4, semver@^7.6.0, semver@^7.6.2:
  version "7.7.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/semver/-/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

semver@~7.5.4:
  version "7.5.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/semver/-/semver-7.5.4.tgz#483986ec4ed38e1c6c48c34894a9182dbff68a6e"
  integrity sha1-SDmG7E7TjhxsSMNIlKkYLb/2im4=
  dependencies:
    lru-cache "^6.0.0"

set-function-length@^1.2.2:
  version "1.2.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/set-function-length/-/set-function-length-1.2.2.tgz#aac72314198eaed975cf77b2c3b6b880695e5449"
  integrity sha1-qscjFBmOrtl1z3eyw7a4gGleVEk=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    function-bind "^1.1.2"
    get-intrinsic "^1.2.4"
    gopd "^1.0.1"
    has-property-descriptors "^1.0.2"

set-function-name@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/set-function-name/-/set-function-name-2.0.2.tgz#16a705c5a0dc2f5e638ca96d8a8cd4e1c2b90985"
  integrity sha1-FqcFxaDcL15jjKltiozU4cK5CYU=
  dependencies:
    define-data-property "^1.1.4"
    es-errors "^1.3.0"
    functions-have-names "^1.2.3"
    has-property-descriptors "^1.0.2"

set-proto@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/set-proto/-/set-proto-1.0.0.tgz#0760dbcff30b2d7e801fd6e19983e56da337565e"
  integrity sha1-B2Dbz/MLLX6AH9bhmYPlbaM3Vl4=
  dependencies:
    dunder-proto "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/shebang-command/-/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/shebang-regex/-/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

shell-quote@^1.6.1:
  version "1.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/shell-quote/-/shell-quote-1.8.3.tgz#55e40ef33cf5c689902353a3d8cd1a6725f08b4b"
  integrity sha1-VeQO8zz1xomQI1Oj2M0aZyXwi0s=

shelljs@^0.8.3:
  version "0.8.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/shelljs/-/shelljs-0.8.5.tgz#de055408d8361bed66c669d2f000538ced8ee20c"
  integrity sha1-3gVUCNg2G+1mxmnS8ABTjO2O4gw=
  dependencies:
    glob "^7.0.0"
    interpret "^1.0.0"
    rechoir "^0.6.2"

side-channel-list@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/side-channel-list/-/side-channel-list-1.0.0.tgz#10cb5984263115d3b7a0e336591e290a830af8ad"
  integrity sha1-EMtZhCYxFdO3oOM2WR4pCoMK+K0=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"

side-channel-map@^1.0.1:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/side-channel-map/-/side-channel-map-1.0.1.tgz#d6bb6b37902c6fef5174e5f533fab4c732a26f42"
  integrity sha1-1rtrN5Asb+9RdOX1M/q0xzKib0I=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"

side-channel-weakmap@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/side-channel-weakmap/-/side-channel-weakmap-1.0.2.tgz#11dda19d5368e40ce9ec2bdc1fb0ecbc0790ecea"
  integrity sha1-Ed2hnVNo5Azp7CvcH7DsvAeQ7Oo=
  dependencies:
    call-bound "^1.0.2"
    es-errors "^1.3.0"
    get-intrinsic "^1.2.5"
    object-inspect "^1.13.3"
    side-channel-map "^1.0.1"

side-channel@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/side-channel/-/side-channel-1.1.0.tgz#c3fcff9c4da932784873335ec9765fa94ff66bc9"
  integrity sha1-w/z/nE2pMnhIczNeyXZfqU/2a8k=
  dependencies:
    es-errors "^1.3.0"
    object-inspect "^1.13.3"
    side-channel-list "^1.0.0"
    side-channel-map "^1.0.1"
    side-channel-weakmap "^1.0.2"

siginfo@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/siginfo/-/siginfo-2.0.0.tgz#32e76c70b79724e3bb567cb9d543eb858ccfaf30"
  integrity sha1-MudscLeXJOO7Vny51UPrhYzPrzA=

signal-exit@^3.0.2, signal-exit@^3.0.3, signal-exit@^3.0.7:
  version "3.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/signal-exit/-/signal-exit-3.0.7.tgz#a9a1767f8af84155114eaabd73f99273c8f59ad9"
  integrity sha1-qaF2f4r4QVURTqq9c/mSc8j1mtk=

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/signal-exit/-/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

sirv@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sirv/-/sirv-3.0.1.tgz#32a844794655b727f9e2867b777e0060fbe07bf3"
  integrity sha1-MqhEeUZVtyf54oZ7d34AYPvge/M=
  dependencies:
    "@polka/url" "^1.0.0-next.24"
    mrmime "^2.0.0"
    totalist "^3.0.0"

sisteransi@^1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sisteransi/-/sisteransi-1.0.5.tgz#134d681297756437cc05ca01370d3a7a571075ed"
  integrity sha1-E01oEpd1ZDfMBcoBNw06elcQde0=

slash@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/slash/-/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/slice-ansi/-/slice-ansi-3.0.0.tgz#31ddc10930a1b7e0b67b08c96c2f49b77a789787"
  integrity sha1-Md3BCTCht+C2ewjJbC9Jt3p4l4c=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

snake-case@^3.0.4:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/snake-case/-/snake-case-3.0.4.tgz#4f2bbd568e9935abdfd593f34c691dadb49c452c"
  integrity sha1-Tyu9Vo6ZNavf1ZPzTGkdrbScRSw=
  dependencies:
    dot-case "^3.0.4"
    tslib "^2.0.3"

source-map-js@^1.2.0, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/source-map-js/-/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map-support@0.5.13:
  version "0.5.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/source-map-support/-/source-map-support-0.5.13.tgz#31b24a9c2e73c2de85066c0feb7d44767ed52932"
  integrity sha1-MbJKnC5zwt6FBmwP631Edn7VKTI=
  dependencies:
    buffer-from "^1.0.0"
    source-map "^0.6.0"

source-map@^0.6.0, source-map@^0.6.1, source-map@~0.6.1:
  version "0.6.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/source-map/-/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

spawndamnit@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/spawndamnit/-/spawndamnit-3.0.1.tgz#44410235d3dc4e21f8e4f740ae3266e4486c2aed"
  integrity sha1-REECNdPcTiH45PdArjJm5EhsKu0=
  dependencies:
    cross-spawn "^7.0.5"
    signal-exit "^4.0.1"

spdx-correct@^3.0.0:
  version "3.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/spdx-correct/-/spdx-correct-3.2.0.tgz#4f5ab0668f0059e34f9c00dce331784a12de4e9c"
  integrity sha1-T1qwZo8AWeNPnADc4zF4ShLeTpw=
  dependencies:
    spdx-expression-parse "^3.0.0"
    spdx-license-ids "^3.0.0"

spdx-exceptions@^2.1.0:
  version "2.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz#5d607d27fc806f66d7b64a766650fa890f04ed66"
  integrity sha1-XWB9J/yAb2bXtkp2ZlD6iQ8E7WY=

spdx-expression-parse@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz#cf70f50482eefdc98e3ce0a6833e4a53ceeba679"
  integrity sha1-z3D1BILu/cmOPOCmgz5KU87rpnk=
  dependencies:
    spdx-exceptions "^2.1.0"
    spdx-license-ids "^3.0.0"

spdx-license-ids@^3.0.0:
  version "3.0.21"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/spdx-license-ids/-/spdx-license-ids-3.0.21.tgz#6d6e980c9df2b6fc905343a3b2d702a6239536c3"
  integrity sha1-bW6YDJ3ytvyQU0OjstcCpiOVNsM=

sprintf-js@~1.0.2:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sprintf-js/-/sprintf-js-1.0.3.tgz#04e6926f662895354f3dd015203633b857297e2c"
  integrity sha1-BOaSb2YolTVPPdAVIDYzuFcpfiw=

stack-utils@^2.0.2, stack-utils@^2.0.3:
  version "2.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/stack-utils/-/stack-utils-2.0.6.tgz#aaf0748169c02fc33c8232abccf933f54a1cc34f"
  integrity sha1-qvB0gWnAL8M8gjKrzPkz9Uocw08=
  dependencies:
    escape-string-regexp "^2.0.0"

stackback@0.0.2:
  version "0.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/stackback/-/stackback-0.0.2.tgz#1ac8a0d9483848d1695e418b6d031a3c3ce68e3b"
  integrity sha1-Gsig2Ug4SNFpXkGLbQMaPDzmjjs=

std-env@^3.9.0:
  version "3.9.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/std-env/-/std-env-3.9.0.tgz#1a6f7243b339dca4c9fd55e1c7504c77ef23e8f1"
  integrity sha1-Gm9yQ7M53KTJ/VXhx1BMd+8j6PE=

stop-iteration-iterator@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/stop-iteration-iterator/-/stop-iteration-iterator-1.1.0.tgz#f481ff70a548f6124d0312c3aa14cbfa7aa542ad"
  integrity sha1-9IH/cKVI9hJNAxLDqhTL+nqlQq0=
  dependencies:
    es-errors "^1.3.0"
    internal-slot "^1.1.0"

storybook-dark-mode@4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/storybook-dark-mode/-/storybook-dark-mode-4.0.2.tgz#2536d1a229ac050172d37aa50bd9f6f7cdad0425"
  integrity sha1-JTbRoimsBQFy03qlC9n2982tBCU=
  dependencies:
    "@storybook/components" "^8.0.0"
    "@storybook/core-events" "^8.0.0"
    "@storybook/global" "^5.0.0"
    "@storybook/icons" "^1.2.5"
    "@storybook/manager-api" "^8.0.0"
    "@storybook/theming" "^8.0.0"
    fast-deep-equal "^3.1.3"
    memoizerific "^1.11.3"

storybook@8.6.12:
  version "8.6.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/storybook/-/storybook-8.6.12.tgz#dddd11644f0344524577bb600a4dabda9a73af55"
  integrity sha1-3d0RZE8DRFJFd7tgCk2r2ppzr1U=
  dependencies:
    "@storybook/core" "8.6.12"

string-argv@~0.3.1:
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string-argv/-/string-argv-0.3.2.tgz#2b6d0ef24b656274d957d54e0a4bbf6153dc02b6"
  integrity sha1-K20O8ktlYnTZV9VOCku/YVPcArY=

string-length@^4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string-length/-/string-length-4.0.2.tgz#a8a8dc7bd5c1a82b9b3c8b87e125f66871b6e57a"
  integrity sha1-qKjce9XBqCubPIuH4SX2aHG25Xo=
  dependencies:
    char-regex "^1.0.2"
    strip-ansi "^6.0.0"

"string-width-cjs@npm:string-width@^4.2.0":
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string-width/-/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^4.0.0, string-width@^4.1.0, string-width@^4.2.0, string-width@^4.2.2, string-width@^4.2.3:
  version "4.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string-width/-/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

string-width@^5.0.1, string-width@^5.1.2:
  version "5.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string-width/-/string-width-5.1.2.tgz#14f8daec6d81e7221d2a357e668cab73bdbca794"
  integrity sha1-FPja7G2B5yIdKjV+Zoyrc728p5Q=
  dependencies:
    eastasianwidth "^0.2.0"
    emoji-regex "^9.2.2"
    strip-ansi "^7.0.1"

string.prototype.matchall@^4.0.6:
  version "4.0.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string.prototype.matchall/-/string.prototype.matchall-4.0.12.tgz#6c88740e49ad4956b1332a911e949583a275d4c0"
  integrity sha1-bIh0DkmtSVaxMyqRHpSVg6J11MA=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.3"
    define-properties "^1.2.1"
    es-abstract "^1.23.6"
    es-errors "^1.3.0"
    es-object-atoms "^1.0.0"
    get-intrinsic "^1.2.6"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    internal-slot "^1.1.0"
    regexp.prototype.flags "^1.5.3"
    set-function-name "^2.0.2"
    side-channel "^1.1.0"

string.prototype.trim@^1.2.10:
  version "1.2.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string.prototype.trim/-/string.prototype.trim-1.2.10.tgz#40b2dd5ee94c959b4dcfb1d65ce72e90da480c81"
  integrity sha1-QLLdXulMlZtNz7HWXOcukNpIDIE=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-data-property "^1.1.4"
    define-properties "^1.2.1"
    es-abstract "^1.23.5"
    es-object-atoms "^1.0.0"
    has-property-descriptors "^1.0.2"

string.prototype.trimend@^1.0.9:
  version "1.0.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string.prototype.trimend/-/string.prototype.trimend-1.0.9.tgz#62e2731272cd285041b36596054e9f66569b6942"
  integrity sha1-YuJzEnLNKFBBs2WWBU6fZlabaUI=
  dependencies:
    call-bind "^1.0.8"
    call-bound "^1.0.2"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string.prototype.trimstart@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string.prototype.trimstart/-/string.prototype.trimstart-1.0.8.tgz#7ee834dda8c7c17eff3118472bb35bfedaa34dde"
  integrity sha1-fug03ajHwX7/MRhHK7Nb/tqjTd4=
  dependencies:
    call-bind "^1.0.7"
    define-properties "^1.2.1"
    es-object-atoms "^1.0.0"

string_decoder@~0.10.x:
  version "0.10.31"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string_decoder/-/string_decoder-0.10.31.tgz#62e203bc41766c6c28c9fc84301dab1c5310fa94"
  integrity sha1-YuIDvEF2bGwoyfyEMB2rHFMQ+pQ=

string_decoder@~1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/string_decoder/-/string_decoder-1.1.1.tgz#9cf1611ba62685d7030ae9e4ba34149c3af03fc8"
  integrity sha1-nPFhG6YmhdcDCunkujQUnDrwP8g=
  dependencies:
    safe-buffer "~5.1.0"

"strip-ansi-cjs@npm:strip-ansi@^6.0.1":
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^6.0.0, strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-ansi/-/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-ansi@^7.0.1, strip-ansi@^7.1.0:
  version "7.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-ansi/-/strip-ansi-7.1.0.tgz#d5b6568ca689d8561370b0707685d22434faff45"
  integrity sha1-1bZWjKaJ2FYTcLBwdoXSJDT6/0U=
  dependencies:
    ansi-regex "^6.0.1"

strip-bom@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-bom/-/strip-bom-3.0.0.tgz#2334c18e9c759f7bdd56fdef7e9ae3d588e68ed3"
  integrity sha1-IzTBjpx1n3vdVv3vfprj1YjmjtM=

strip-bom@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-bom/-/strip-bom-4.0.0.tgz#9c3505c1db45bcedca3d9cf7a16f5c5aa3901878"
  integrity sha1-nDUFwdtFvO3KPZz3oW9cWqOQGHg=

strip-final-newline@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-final-newline/-/strip-final-newline-2.0.0.tgz#89b852fb2fcbe936f6f4b3187afb0a12c1ab58ad"
  integrity sha1-ibhS+y/L6Tb29LMYevsKEsGrWK0=

strip-final-newline@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-final-newline/-/strip-final-newline-3.0.0.tgz#52894c313fbff318835280aed60ff71ebf12b8fd"
  integrity sha1-UolMMT+/8xiDUoCu1g/3Hr8SuP0=

strip-indent@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-indent/-/strip-indent-3.0.0.tgz#c32e1cee940b6b3432c771bc2c54bcce73cd3001"
  integrity sha1-wy4c7pQLazQyx3G8LFS8znPNMAE=
  dependencies:
    min-indent "^1.0.0"

strip-indent@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-indent/-/strip-indent-4.0.0.tgz#b41379433dd06f5eae805e21d631e07ee670d853"
  integrity sha1-tBN5Qz3Qb16ugF4h1jHgfuZw2FM=
  dependencies:
    min-indent "^1.0.1"

strip-json-comments@^3.1.1, strip-json-comments@~3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-json-comments/-/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

strip-literal@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/strip-literal/-/strip-literal-3.0.0.tgz#ce9c452a91a0af2876ed1ae4e583539a353df3fc"
  integrity sha1-zpxFKpGgryh27Rrk5YNTmjU98/w=
  dependencies:
    js-tokens "^9.0.1"

sucrase@^3.35.0:
  version "3.35.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sucrase/-/sucrase-3.35.0.tgz#57f17a3d7e19b36d8995f06679d121be914ae263"
  integrity sha1-V/F6PX4Zs22JlfBmedEhvpFK4mM=
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.2"
    commander "^4.0.0"
    glob "^10.3.10"
    lines-and-columns "^1.1.6"
    mz "^2.7.0"
    pirates "^4.0.1"
    ts-interface-checker "^0.1.9"

supports-color@^7.1.0:
  version "7.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/supports-color/-/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-color@^8.0.0, supports-color@^8.1.1, supports-color@~8.1.1:
  version "8.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/supports-color/-/supports-color-8.1.1.tgz#cd6fc17e28500cff56c1b86c0a7fd4a54a73005c"
  integrity sha1-zW/BfihQDP9WwbhsCn/UpUpzAFw=
  dependencies:
    has-flag "^4.0.0"

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

svg-parser@^2.0.4:
  version "2.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/svg-parser/-/svg-parser-2.0.4.tgz#fdc2e29e13951736140b76cb122c8ee6630eb6b5"
  integrity sha1-/cLinhOVFzYUC3bLEiyO5mMOtrU=

swiper@11.2.10:
  version "11.2.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/swiper/-/swiper-11.2.10.tgz#ed0b17286b56f7fe8d4b46ed61e6e0bd8daaccad"
  integrity sha1-7QsXKGtW9/6NS0btYebgvY2qzK0=

sync-child-process@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sync-child-process/-/sync-child-process-1.0.2.tgz#45e7c72e756d1243e80b547ea2e17957ab9e367f"
  integrity sha1-RefHLnVtEkPoC1R+ouF5V6ueNn8=
  dependencies:
    sync-message-port "^1.0.0"

sync-message-port@^1.0.0:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/sync-message-port/-/sync-message-port-1.1.3.tgz#6055c565ee8c81d2f9ee5aae7db757e6d9088c0c"
  integrity sha1-YFXFZe6MgdL57lqufbdX5tkIjAw=

synckit@^0.8.5:
  version "0.8.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/synckit/-/synckit-0.8.8.tgz#fe7fe446518e3d3d49f5e429f443cf08b6edfcd7"
  integrity sha1-/n/kRlGOPT1J9eQp9EPPCLbt/Nc=
  dependencies:
    "@pkgr/core" "^0.1.0"
    tslib "^2.6.2"

tabbable@^6.0.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tabbable/-/tabbable-6.2.0.tgz#732fb62bc0175cfcec257330be187dcfba1f3b97"
  integrity sha1-cy+2K8AXXPzsJXMwvhh9z7ofO5c=

tailwind-merge@3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tailwind-merge/-/tailwind-merge-3.1.0.tgz#451460cf5580b2da1120b45076e114814d593a36"
  integrity sha1-RRRgz1WAstoRILRQduEUgU1ZOjY=

tailwind-scrollbar@4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tailwind-scrollbar/-/tailwind-scrollbar-4.0.2.tgz#764dbc00aede9c8b885a2b8e8191f7785e05a92b"
  integrity sha1-dk28AK7enIuIWiuOgZH3eF4FqSs=
  dependencies:
    prism-react-renderer "^2.4.1"

tailwindcss@^3.4.13:
  version "3.4.17"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tailwindcss/-/tailwindcss-3.4.17.tgz#ae8406c0f96696a631c790768ff319d46d5e5a63"
  integrity sha1-roQGwPlmlqYxx5B2j/MZ1G1eWmM=
  dependencies:
    "@alloc/quick-lru" "^5.2.0"
    arg "^5.0.2"
    chokidar "^3.6.0"
    didyoumean "^1.2.2"
    dlv "^1.1.3"
    fast-glob "^3.3.2"
    glob-parent "^6.0.2"
    is-glob "^4.0.3"
    jiti "^1.21.6"
    lilconfig "^3.1.3"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    object-hash "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.4.47"
    postcss-import "^15.1.0"
    postcss-js "^4.0.1"
    postcss-load-config "^4.0.2"
    postcss-nested "^6.2.0"
    postcss-selector-parser "^6.1.2"
    resolve "^1.22.8"
    sucrase "^3.35.0"

temp-dir@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/temp-dir/-/temp-dir-2.0.0.tgz#bde92b05bdfeb1516e804c9c00ad45177f31321e"
  integrity sha1-vekrBb3+sVFugEycAK1FF38xMh4=

tempfile@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tempfile/-/tempfile-3.0.0.tgz#5376a3492de7c54150d0cc0612c3f00e2cdaf76c"
  integrity sha1-U3ajSS3nxUFQ0MwGEsPwDiza92w=
  dependencies:
    temp-dir "^2.0.0"
    uuid "^3.3.2"

tempy@^1.0.0:
  version "1.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tempy/-/tempy-1.0.1.tgz#30fe901fd869cfb36ee2bd999805aa72fbb035de"
  integrity sha1-MP6QH9hpz7Nu4r2ZmAWqcvuwNd4=
  dependencies:
    del "^6.0.0"
    is-stream "^2.0.0"
    temp-dir "^2.0.0"
    type-fest "^0.16.0"
    unique-string "^2.0.0"

term-size@^2.1.0:
  version "2.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/term-size/-/term-size-2.2.1.tgz#2a6a54840432c2fb6320fea0f415531e90189f54"
  integrity sha1-KmpUhAQywvtjIP6g9BVTHpAYn1Q=

test-exclude@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/test-exclude/-/test-exclude-6.0.0.tgz#04a8698661d805ea6fa293b6cb9e63ac044ef15e"
  integrity sha1-BKhphmHYBepvopO2y55jrARO8V4=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^7.1.4"
    minimatch "^3.0.4"

test-exclude@^7.0.1:
  version "7.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/test-exclude/-/test-exclude-7.0.1.tgz#20b3ba4906ac20994e275bbcafd68d510264c2a2"
  integrity sha1-ILO6SQasIJlOJ1u8r9aNUQJkwqI=
  dependencies:
    "@istanbuljs/schema" "^0.1.2"
    glob "^10.4.1"
    minimatch "^9.0.4"

text-table@^0.2.0:
  version "0.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/text-table/-/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

thenify-all@^1.0.0:
  version "1.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/thenify-all/-/thenify-all-1.6.0.tgz#1a1918d402d8fc3f98fbf234db0bcc8cc10e9726"
  integrity sha1-GhkY1ALY/D+Y+/I02wvMjMEOlyY=
  dependencies:
    thenify ">= 3.1.0 < 4"

"thenify@>= 3.1.0 < 4":
  version "3.3.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/thenify/-/thenify-3.3.1.tgz#8932e686a4066038a016dd9e2ca46add9838a95f"
  integrity sha1-iTLmhqQGYDigFt2eLKRq3Zg4qV8=
  dependencies:
    any-promise "^1.0.0"

through2@^2.0.1:
  version "2.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/through2/-/through2-2.0.5.tgz#01c1e39eb31d07cb7d03a96a70823260b23132cd"
  integrity sha1-AcHjnrMdB8t9A6lqcIIyYLIxMs0=
  dependencies:
    readable-stream "~2.3.6"
    xtend "~4.0.1"

tiny-case@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tiny-case/-/tiny-case-1.0.3.tgz#d980d66bc72b5d5a9ca86fb7c9ffdb9c898ddd03"
  integrity sha1-2YDWa8crXVqcqG+3yf/bnImN3QM=

tiny-invariant@^1.1.0, tiny-invariant@^1.3.1, tiny-invariant@^1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tiny-invariant/-/tiny-invariant-1.3.3.tgz#46680b7a873a0d5d10005995eb90a70d74d60127"
  integrity sha1-RmgLeoc6DV0QAFmV65CnDXTWASc=

tinybench@^2.9.0:
  version "2.9.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinybench/-/tinybench-2.9.0.tgz#103c9f8ba6d7237a47ab6dd1dcff77251863426b"
  integrity sha1-EDyfi6bXI3pHq23R3P93JRhjQms=

tinyexec@^0.3.2:
  version "0.3.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyexec/-/tinyexec-0.3.2.tgz#941794e657a85e496577995c6eef66f53f42b3d2"
  integrity sha1-lBeU5leoXklld5lcbu9m9T9Cs9I=

tinyglobby@^0.2.13, tinyglobby@^0.2.14:
  version "0.2.14"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyglobby/-/tinyglobby-0.2.14.tgz#5280b0cf3f972b050e74ae88406c0a6a58f4079d"
  integrity sha1-UoCwzz+XKwUOdK6IQGwKalj0B50=
  dependencies:
    fdir "^6.4.4"
    picomatch "^4.0.2"

tinypool@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinypool/-/tinypool-1.1.0.tgz#4252913ec76ef8f728f2524e2118f3bef9cf23f4"
  integrity sha1-QlKRPsdu+Pco8lJOIRjzvvnPI/Q=

tinyrainbow@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyrainbow/-/tinyrainbow-1.2.0.tgz#5c57d2fc0fb3d1afd78465c33ca885d04f02abb5"
  integrity sha1-XFfS/A+z0a/XhGXDPKiF0E8Cq7U=

tinyrainbow@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyrainbow/-/tinyrainbow-2.0.0.tgz#9509b2162436315e80e3eee0fcce4474d2444294"
  integrity sha1-lQmyFiQ2MV6A4+7g/M5EdNJEQpQ=

tinyspy@^3.0.0:
  version "3.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyspy/-/tinyspy-3.0.2.tgz#86dd3cf3d737b15adcf17d7887c84a75201df20a"
  integrity sha1-ht0889c3sVrc8X14h8hKdSAd8go=

tinyspy@^4.0.3:
  version "4.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tinyspy/-/tinyspy-4.0.3.tgz#d1d0f0602f4c15f1aae083a34d6d0df3363b1b52"
  integrity sha1-0dDwYC9MFfGq4IOjTW0N8zY7G1I=

tmp@^0.0.33:
  version "0.0.33"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tmp/-/tmp-0.0.33.tgz#6d34335889768d21b2bcda0aa277ced3b1bfadf9"
  integrity sha1-bTQzWIl2jSGyvNoKonfO07G/rfk=
  dependencies:
    os-tmpdir "~1.0.2"

tmpl@1.0.5:
  version "1.0.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tmpl/-/tmpl-1.0.5.tgz#8683e0b902bb9c20c4f726e3c0b69f36518c07cc"
  integrity sha1-hoPguQK7nCDE9ybjwLafNlGMB8w=

to-camel-case@1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/to-camel-case/-/to-camel-case-1.0.0.tgz#1a56054b2f9d696298ce66a60897322b6f423e46"
  integrity sha1-GlYFSy+daWKYzmamCJcyK29CPkY=
  dependencies:
    to-space-case "^1.0.0"

to-no-case@^1.0.0:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/to-no-case/-/to-no-case-1.0.2.tgz#c722907164ef6b178132c8e69930212d1b4aa16a"
  integrity sha1-xyKQcWTvaxeBMsjmmTAhLRtKoWo=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/to-regex-range/-/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

to-space-case@^1.0.0:
  version "1.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/to-space-case/-/to-space-case-1.0.0.tgz#b052daafb1b2b29dc770cea0163e5ec0ebc9fc17"
  integrity sha1-sFLar7Gysp3HcM6gFj5ewOvJ/Bc=
  dependencies:
    to-no-case "^1.0.0"

toposort@^2.0.2:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/toposort/-/toposort-2.0.2.tgz#ae21768175d1559d48bef35420b2f4962f09c330"
  integrity sha1-riF2gXXRVZ1IvvNUILL0li8JwzA=

totalist@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/totalist/-/totalist-3.0.1.tgz#ba3a3d600c915b1a97872348f79c127475f6acf8"
  integrity sha1-ujo9YAyRWxqXhyNI95wSdHX2rPg=

transliteration@^2.2.0:
  version "2.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/transliteration/-/transliteration-2.3.5.tgz#8f92309575f69e4a8a525dab4ff705ebcf961c45"
  integrity sha1-j5IwlXX2nkqKUl2rT/cF68+WHEU=
  dependencies:
    yargs "^17.5.1"

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ts-api-utils/-/ts-api-utils-2.1.0.tgz#595f7094e46eed364c13fd23e75f9513d29baf91"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

ts-dedent@^2.0.0, ts-dedent@^2.2.0:
  version "2.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ts-dedent/-/ts-dedent-2.2.0.tgz#39e4bd297cd036292ae2394eb3412be63f563bb5"
  integrity sha1-OeS9KXzQNikq4jlOs0Er5j9WO7U=

ts-interface-checker@^0.1.9:
  version "0.1.13"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz#784fd3d679722bc103b1b4b8030bcddb5db2a699"
  integrity sha1-eE/T1nlyK8EDsbS4AwvN212yppk=

ts-node@^10.9.2:
  version "10.9.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ts-node/-/ts-node-10.9.2.tgz#70f021c9e185bccdca820e26dc413805c101c71f"
  integrity sha1-cPAhyeGFvM3Kgg4m3EE4BcEBxx8=
  dependencies:
    "@cspotcode/source-map-support" "^0.8.0"
    "@tsconfig/node10" "^1.0.7"
    "@tsconfig/node12" "^1.0.7"
    "@tsconfig/node14" "^1.0.0"
    "@tsconfig/node16" "^1.0.2"
    acorn "^8.4.1"
    acorn-walk "^8.1.1"
    arg "^4.1.0"
    create-require "^1.1.0"
    diff "^4.0.1"
    make-error "^1.1.1"
    v8-compile-cache-lib "^3.0.1"
    yn "3.1.1"

tsconfig-paths@^3.14.1:
  version "3.15.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tsconfig-paths/-/tsconfig-paths-3.15.0.tgz#5299ec605e55b1abb23ec939ef15edaf483070d4"
  integrity sha1-UpnsYF5VsauyPsk57xXtr0gwcNQ=
  dependencies:
    "@types/json5" "^0.0.29"
    json5 "^1.0.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tsconfig-paths@^4.2.0:
  version "4.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tsconfig-paths/-/tsconfig-paths-4.2.0.tgz#ef78e19039133446d244beac0fd6a1632e2d107c"
  integrity sha1-73jhkDkTNEbSRL6sD9ahYy4tEHw=
  dependencies:
    json5 "^2.2.2"
    minimist "^1.2.6"
    strip-bom "^3.0.0"

tslib@^2.0.0, tslib@^2.0.1, tslib@^2.0.3, tslib@^2.1.0, tslib@^2.6.2:
  version "2.8.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tslib/-/tslib-2.8.1.tgz#612efe4ed235d567e8aba5f2a5fab70280ade83f"
  integrity sha1-YS7+TtI11Wfoq6Xypfq3AoCt6D8=

tsx@^4.19.4:
  version "4.20.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tsx/-/tsx-4.20.3.tgz#f913e4911d59ad177c1bcee19d1035ef8dd6e2fb"
  integrity sha1-+RPkkR1ZrRd8G87hnRA1743W4vs=
  dependencies:
    esbuild "~0.25.0"
    get-tsconfig "^4.7.5"
  optionalDependencies:
    fsevents "~2.3.3"

tween-functions@^1.2.0:
  version "1.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/tween-functions/-/tween-functions-1.2.0.tgz#1ae3a50e7c60bb3def774eac707acbca73bbc3ff"
  integrity sha1-GuOlDnxguz3vd06scHrLynO7w/8=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-check/-/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-detect@4.0.8:
  version "4.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-detect/-/type-detect-4.0.8.tgz#7646fb5f18871cfbb7749e69bd39a6388eb7450c"
  integrity sha1-dkb7XxiHHPu3dJ5pvTmmOI63RQw=

type-fest@^0.12.0:
  version "0.12.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.12.0.tgz#f57a27ab81c68d136a51fd71467eff94157fa1ee"
  integrity sha1-9Xonq4HGjRNqUf1xRn7/lBV/oe4=

type-fest@^0.16.0:
  version "0.16.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.16.0.tgz#3240b891a78b0deae910dbeb86553e552a148860"
  integrity sha1-MkC4kaeLDerpENvrhlU+VSoUiGA=

type-fest@^0.20.2:
  version "0.20.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

type-fest@^0.21.3:
  version "0.21.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.21.3.tgz#d260a24b0198436e133fa26a524a6d65fa3b2e37"
  integrity sha1-0mCiSwGYQ24TP6JqUkptZfo7Ljc=

type-fest@^0.6.0:
  version "0.6.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.6.0.tgz#8d2a2370d3df886eb5c90ada1c5bf6188acf838b"
  integrity sha1-jSojcNPfiG61yQraHFv2GIrPg4s=

type-fest@^0.8.1:
  version "0.8.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-0.8.1.tgz#09e249ebde851d3b1e48d27c105444667f17b83d"
  integrity sha1-CeJJ696FHTseSNJ8EFREZn8XuD0=

type-fest@^2.19.0:
  version "2.19.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/type-fest/-/type-fest-2.19.0.tgz#88068015bb33036a598b952e55e9311a60fd3a9b"
  integrity sha1-iAaAFbszA2pZi5UuVekxGmD9Ops=

typed-array-buffer@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typed-array-buffer/-/typed-array-buffer-1.0.3.tgz#a72395450a4869ec033fd549371b47af3a2ee536"
  integrity sha1-pyOVRQpIaewDP9VJNxtHrzou5TY=
  dependencies:
    call-bound "^1.0.3"
    es-errors "^1.3.0"
    is-typed-array "^1.1.14"

typed-array-byte-length@^1.0.3:
  version "1.0.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typed-array-byte-length/-/typed-array-byte-length-1.0.3.tgz#8407a04f7d78684f3d252aa1a143d2b77b4160ce"
  integrity sha1-hAegT314aE89JSqhoUPSt3tBYM4=
  dependencies:
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.14"

typed-array-byte-offset@^1.0.4:
  version "1.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typed-array-byte-offset/-/typed-array-byte-offset-1.0.4.tgz#ae3698b8ec91a8ab945016108aef00d5bff12355"
  integrity sha1-rjaYuOyRqKuUUBYQiu8A1b/xI1U=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    for-each "^0.3.3"
    gopd "^1.2.0"
    has-proto "^1.2.0"
    is-typed-array "^1.1.15"
    reflect.getprototypeof "^1.0.9"

typed-array-length@^1.0.7:
  version "1.0.7"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typed-array-length/-/typed-array-length-1.0.7.tgz#ee4deff984b64be1e118b0de8c9c877d5ce73d3d"
  integrity sha1-7k3v+YS2S+HhGLDejJyHfVznPT0=
  dependencies:
    call-bind "^1.0.7"
    for-each "^0.3.3"
    gopd "^1.0.1"
    is-typed-array "^1.1.13"
    possible-typed-array-names "^1.0.0"
    reflect.getprototypeof "^1.0.6"

typedarray@^0.0.6:
  version "0.0.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typedarray/-/typedarray-0.0.6.tgz#867ac74e3864187b1d3d47d996a78ec5c8830777"
  integrity sha1-hnrHTjhkGHsdPUfZlqeOxciDB3c=

typescript@5.8.2:
  version "5.8.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typescript/-/typescript-5.8.2.tgz#8170b3702f74b79db2e5a96207c15e65807999e4"
  integrity sha1-gXCzcC90t52y5aliB8FeZYB5meQ=

typescript@5.8.3:
  version "5.8.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/typescript/-/typescript-5.8.3.tgz#92f8a3e5e3cf497356f4178c34cd65a7f5e8440e"
  integrity sha1-kvij5ePPSXNW9BeMNM1lp/XoRA4=

ufo@^1.5.4:
  version "1.6.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ufo/-/ufo-1.6.1.tgz#ac2db1d54614d1b22c1d603e3aef44a85d8f146b"
  integrity sha1-rC2x1UYU0bIsHWA+Ou9EqF2PFGs=

unbox-primitive@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/unbox-primitive/-/unbox-primitive-1.1.0.tgz#8d9d2c9edeea8460c7f35033a88867944934d1e2"
  integrity sha1-jZ0snt7qhGDH81AzqIhnlEk00eI=
  dependencies:
    call-bound "^1.0.3"
    has-bigints "^1.0.2"
    has-symbols "^1.1.0"
    which-boxed-primitive "^1.1.1"

undici-types@~6.20.0:
  version "6.20.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/undici-types/-/undici-types-6.20.0.tgz#8171bf22c1f588d1554d55bf204bc624af388433"
  integrity sha1-gXG/IsH1iNFVTVW/IEvGJK84hDM=

undici-types@~7.8.0:
  version "7.8.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/undici-types/-/undici-types-7.8.0.tgz#de00b85b710c54122e44fbfd911f8d70174cd294"
  integrity sha1-3gC4W3EMVBIuRPv9kR+NcBdM0pQ=

unique-string@^2.0.0:
  version "2.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/unique-string/-/unique-string-2.0.0.tgz#****************************************"
  integrity sha1-OcZFH4GvsnSd4rIz4/fF6IQ72J0=
  dependencies:
    crypto-random-string "^2.0.0"

universalify@^0.1.0:
  version "0.1.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/universalify/-/universalify-0.1.2.tgz#b646f69be3942dabcecc9d6639c80dc105efaa66"
  integrity sha1-tkb2m+OULavOzJ1mOcgNwQXvqmY=

universalify@^2.0.0:
  version "2.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/universalify/-/universalify-2.0.1.tgz#168efc2180964e6386d061e094df61afe239b18d"
  integrity sha1-Fo78IYCWTmOG0GHglN9hr+I5sY0=

unplugin@^1.3.1:
  version "1.16.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/unplugin/-/unplugin-1.16.1.tgz#a844d2e3c3b14a4ac2945c42be80409321b61199"
  integrity sha1-qETS48OxSkrClFxCvoBAkyG2EZk=
  dependencies:
    acorn "^8.14.0"
    webpack-virtual-modules "^0.6.2"

untildify@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/untildify/-/untildify-4.0.0.tgz#2bc947b953652487e4600949fb091e3ae8cd919b"
  integrity sha1-K8lHuVNlJIfkYAlJ+wkeOujNkZs=

update-browserslist-db@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz#348377dd245216f9e7060ff50b15a1b740b75420"
  integrity sha1-NIN33SRSFvnnBg/1CxWht0C3VCA=
  dependencies:
    escalade "^3.2.0"
    picocolors "^1.1.1"

uri-js@^4.2.2, uri-js@^4.4.1:
  version "4.4.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/uri-js/-/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

url@0.10.3:
  version "0.10.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/url/-/url-0.10.3.tgz#021e4d9c7705f21bbf37d03ceb58767402774c64"
  integrity sha1-Ah5NnHcF8hu/N9A861h2dAJ3TGQ=
  dependencies:
    punycode "1.3.2"
    querystring "0.2.0"

use-callback-ref@^1.3.2, use-callback-ref@^1.3.3:
  version "1.3.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/use-callback-ref/-/use-callback-ref-1.3.3.tgz#98d9fab067075841c5b2c6852090d5d0feabe2bf"
  integrity sha1-mNn6sGcHWEHFssaFIJDV0P6r4r8=
  dependencies:
    tslib "^2.0.0"

use-sidecar@^1.1.2, use-sidecar@^1.1.3:
  version "1.1.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/use-sidecar/-/use-sidecar-1.1.3.tgz#10e7fd897d130b896e2c546c63a5e8233d00efdb"
  integrity sha1-EOf9iX0TC4luLFRsY6XoIz0A79s=
  dependencies:
    detect-node-es "^1.1.0"
    tslib "^2.0.0"

use-sync-external-store@^1.2.2, use-sync-external-store@^1.4.0:
  version "1.5.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz#55122e2a3edd2a6c106174c27485e0fd59bcfca0"
  integrity sha1-VRIuKj7dKmwQYXTCdIXg/Vm8/KA=

util-deprecate@^1.0.2, util-deprecate@~1.0.1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/util-deprecate/-/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

util@^0.12.4, util@^0.12.5:
  version "0.12.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/util/-/util-0.12.5.tgz#5f17a6059b73db61a875668781a1c2b136bd6fbc"
  integrity sha1-XxemBZtz22GodWaHgaHCsTa9b7w=
  dependencies:
    inherits "^2.0.3"
    is-arguments "^1.0.4"
    is-generator-function "^1.0.7"
    is-typed-array "^1.1.3"
    which-typed-array "^1.1.2"

uuid@8.0.0:
  version "8.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/uuid/-/uuid-8.0.0.tgz#bc6ccf91b5ff0ac07bbcdbf1c7c4e150db4dbb6c"
  integrity sha1-vGzPkbX/CsB7vNvxx8ThUNtNu2w=

uuid@^3.3.2:
  version "3.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/uuid/-/uuid-3.4.0.tgz#b23e4358afa8a202fe7a100af1f5f883f02007ee"
  integrity sha1-sj5DWK+oogL+ehAK8fX4g/AgB+4=

uuid@^9.0.0:
  version "9.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/uuid/-/uuid-9.0.1.tgz#e188d4c8853cc722220392c424cd637f32293f30"
  integrity sha1-4YjUyIU8xyIiA5LEJM1jfzIpPzA=

v8-compile-cache-lib@^3.0.1:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz#6336e8d71965cb3d35a1bbb7868445a7c05264bf"
  integrity sha1-Yzbo1xllyz01obu3hoRFp8BSZL8=

v8-to-istanbul@^9.0.1:
  version "9.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz#b9572abfa62bd556c16d75fdebc1a411d5ff3175"
  integrity sha1-uVcqv6Yr1VbBbXX968GkEdX/MXU=
  dependencies:
    "@jridgewell/trace-mapping" "^0.3.12"
    "@types/istanbul-lib-coverage" "^2.0.1"
    convert-source-map "^2.0.0"

validate-npm-package-license@^3.0.1:
  version "3.0.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz#fc91f6b9c7ba15c857f4cb2c5defeec39d4f410a"
  integrity sha1-/JH2uce6FchX9MssXe/uw51PQQo=
  dependencies:
    spdx-correct "^3.0.0"
    spdx-expression-parse "^3.0.0"

varint@^6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/varint/-/varint-6.0.0.tgz#9881eb0ce8feaea6512439d19ddf84bf551661d0"
  integrity sha1-mIHrDOj+rqZRJDnRnd+Ev1UWYdA=

victory-vendor@^37.0.2:
  version "37.3.6"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/victory-vendor/-/victory-vendor-37.3.6.tgz#401ac4b029a0b3d33e0cba8e8a1d765c487254da"
  integrity sha1-QBrEsCmgs9M+DLqOih12XEhyVNo=
  dependencies:
    "@types/d3-array" "^3.0.3"
    "@types/d3-ease" "^3.0.0"
    "@types/d3-interpolate" "^3.0.1"
    "@types/d3-scale" "^4.0.2"
    "@types/d3-shape" "^3.1.0"
    "@types/d3-time" "^3.0.0"
    "@types/d3-timer" "^3.0.0"
    d3-array "^3.1.6"
    d3-ease "^3.0.1"
    d3-interpolate "^3.0.1"
    d3-scale "^4.0.2"
    d3-shape "^3.1.0"
    d3-time "^3.0.0"
    d3-timer "^3.0.1"

vite-node@3.2.3:
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite-node/-/vite-node-3.2.3.tgz#1c5a2282fe100114c26fd221daf506e69d392a36"
  integrity sha1-HFoigv4QARTCb9Ih2vUG5p05KjY=
  dependencies:
    cac "^6.7.14"
    debug "^4.4.1"
    es-module-lexer "^1.7.0"
    pathe "^2.0.3"
    vite "^5.0.0 || ^6.0.0 || ^7.0.0-0"

vite-plugin-checker@^0.6.2:
  version "0.6.4"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite-plugin-checker/-/vite-plugin-checker-0.6.4.tgz#aca186ab605aa15bd2c5dd9cc6d7c8fdcbe214ec"
  integrity sha1-rKGGq2BaoVvSxd2cxtfI/cviFOw=
  dependencies:
    "@babel/code-frame" "^7.12.13"
    ansi-escapes "^4.3.0"
    chalk "^4.1.1"
    chokidar "^3.5.1"
    commander "^8.0.0"
    fast-glob "^3.2.7"
    fs-extra "^11.1.0"
    npm-run-path "^4.0.1"
    semver "^7.5.0"
    strip-ansi "^6.0.0"
    tiny-invariant "^1.1.0"
    vscode-languageclient "^7.0.0"
    vscode-languageserver "^7.0.0"
    vscode-languageserver-textdocument "^1.0.1"
    vscode-uri "^3.0.2"

vite-plugin-dts@4.5.3:
  version "4.5.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite-plugin-dts/-/vite-plugin-dts-4.5.3.tgz#9d9e64c12e21e9389efd9f999e034cd5864b0027"
  integrity sha1-nZ5kwS4h6Tie/Z+ZngNM1YZLACc=
  dependencies:
    "@microsoft/api-extractor" "^7.50.1"
    "@rollup/pluginutils" "^5.1.4"
    "@volar/typescript" "^2.4.11"
    "@vue/language-core" "2.2.0"
    compare-versions "^6.1.1"
    debug "^4.4.0"
    kolorist "^1.8.0"
    local-pkg "^1.0.0"
    magic-string "^0.30.17"

vite-plugin-sass-dts@1.3.31:
  version "1.3.31"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite-plugin-sass-dts/-/vite-plugin-sass-dts-1.3.31.tgz#5a8bdfe177362e07ca66c17d0f17a2d412fd0005"
  integrity sha1-Wovf4Xc2LgfKZsF9Dxei1BL9AAU=
  dependencies:
    postcss-js "^4.0.1"

vite-plugin-svgr@4.3.0:
  version "4.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite-plugin-svgr/-/vite-plugin-svgr-4.3.0.tgz#742f16f11375996306c696ec323e4d23f6005075"
  integrity sha1-dC8W8RN1mWMGxpbsMj5NI/YAUHU=
  dependencies:
    "@rollup/pluginutils" "^5.1.3"
    "@svgr/core" "^8.1.0"
    "@svgr/plugin-jsx" "^8.1.0"

"vite@^5.0.0 || ^6.0.0 || ^7.0.0-0":
  version "6.3.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite/-/vite-6.3.5.tgz#fec73879013c9c0128c8d284504c6d19410d12a3"
  integrity sha1-/sc4eQE8nAEoyNKEUExtGUENEqM=
  dependencies:
    esbuild "^0.25.0"
    fdir "^6.4.4"
    picomatch "^4.0.2"
    postcss "^8.5.3"
    rollup "^4.34.9"
    tinyglobby "^0.2.13"
  optionalDependencies:
    fsevents "~2.3.3"

vite@^5.0.12:
  version "5.4.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vite/-/vite-5.4.19.tgz#20efd060410044b3ed555049418a5e7d1998f959"
  integrity sha1-IO/QYEEARLPtVVBJQYpefRmY+Vk=
  dependencies:
    esbuild "^0.21.3"
    postcss "^8.4.43"
    rollup "^4.20.0"
  optionalDependencies:
    fsevents "~2.3.3"

vitest@^3.0.9:
  version "3.2.3"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vitest/-/vitest-3.2.3.tgz#c2497733cf51f8ec2a3327f80789b269324edb1c"
  integrity sha1-wkl3M89R+OwqMyf4B4myaTJO2xw=
  dependencies:
    "@types/chai" "^5.2.2"
    "@vitest/expect" "3.2.3"
    "@vitest/mocker" "3.2.3"
    "@vitest/pretty-format" "^3.2.3"
    "@vitest/runner" "3.2.3"
    "@vitest/snapshot" "3.2.3"
    "@vitest/spy" "3.2.3"
    "@vitest/utils" "3.2.3"
    chai "^5.2.0"
    debug "^4.4.1"
    expect-type "^1.2.1"
    magic-string "^0.30.17"
    pathe "^2.0.3"
    picomatch "^4.0.2"
    std-env "^3.9.0"
    tinybench "^2.9.0"
    tinyexec "^0.3.2"
    tinyglobby "^0.2.14"
    tinypool "^1.1.0"
    tinyrainbow "^2.0.0"
    vite "^5.0.0 || ^6.0.0 || ^7.0.0-0"
    vite-node "3.2.3"
    why-is-node-running "^2.3.0"

vscode-jsonrpc@6.0.0:
  version "6.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-jsonrpc/-/vscode-jsonrpc-6.0.0.tgz#108bdb09b4400705176b957ceca9e0880e9b6d4e"
  integrity sha1-EIvbCbRABwUXa5V87KngiA6bbU4=

vscode-languageclient@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-languageclient/-/vscode-languageclient-7.0.0.tgz#b505c22c21ffcf96e167799757fca07a6bad0fb2"
  integrity sha1-tQXCLCH/z5bhZ3mXV/ygemutD7I=
  dependencies:
    minimatch "^3.0.4"
    semver "^7.3.4"
    vscode-languageserver-protocol "3.16.0"

vscode-languageserver-protocol@3.16.0:
  version "3.16.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-languageserver-protocol/-/vscode-languageserver-protocol-3.16.0.tgz#34135b61a9091db972188a07d337406a3cdbe821"
  integrity sha1-NBNbYakJHblyGIoH0zdAajzb6CE=
  dependencies:
    vscode-jsonrpc "6.0.0"
    vscode-languageserver-types "3.16.0"

vscode-languageserver-textdocument@^1.0.1:
  version "1.0.12"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-languageserver-textdocument/-/vscode-languageserver-textdocument-1.0.12.tgz#457ee04271ab38998a093c68c2342f53f6e4a631"
  integrity sha1-RX7gQnGrOJmKCTxowjQvU/bkpjE=

vscode-languageserver-types@3.16.0:
  version "3.16.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-languageserver-types/-/vscode-languageserver-types-3.16.0.tgz#ecf393fc121ec6974b2da3efb3155644c514e247"
  integrity sha1-7POT/BIexpdLLaPvsxVWRMUU4kc=

vscode-languageserver@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-languageserver/-/vscode-languageserver-7.0.0.tgz#49b068c87cfcca93a356969d20f5d9bdd501c6b0"
  integrity sha1-SbBoyHz8ypOjVpadIPXZvdUBxrA=
  dependencies:
    vscode-languageserver-protocol "3.16.0"

vscode-uri@^3.0.2, vscode-uri@^3.0.8:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/vscode-uri/-/vscode-uri-3.1.0.tgz#dd09ec5a66a38b5c3fffc774015713496d14e09c"
  integrity sha1-3QnsWmaji1w//8d0AVcTSW0U4Jw=

wait-port@^1.1.0:
  version "1.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wait-port/-/wait-port-1.1.0.tgz#e5d64ee071118d985e2b658ae7ad32b2ce29b6b5"
  integrity sha1-5dZO4HERjZheK2WK560yss4ptrU=
  dependencies:
    chalk "^4.1.2"
    commander "^9.3.0"
    debug "^4.3.4"

walker@^1.0.8:
  version "1.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/walker/-/walker-1.0.8.tgz#bd498db477afe573dc04185f011d3ab8a8d7653f"
  integrity sha1-vUmNtHev5XPcBBhfAR06uKjXZT8=
  dependencies:
    makeerror "1.0.12"

webidl-conversions@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/webidl-conversions/-/webidl-conversions-7.0.0.tgz#256b4e1882be7debbf01d05f0aa2039778ea080a"
  integrity sha1-JWtOGIK+feu/AdBfCqIDl3jqCAo=

webpack-virtual-modules@^0.6.2:
  version "0.6.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/webpack-virtual-modules/-/webpack-virtual-modules-0.6.2.tgz#057faa9065c8acf48f24cb57ac0e77739ab9a7e8"
  integrity sha1-BX+qkGXIrPSPJMtXrA53c5q5p+g=

whatwg-mimetype@^3.0.0:
  version "3.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/whatwg-mimetype/-/whatwg-mimetype-3.0.0.tgz#5fa1a7623867ff1af6ca3dc72ad6b8a4208beba7"
  integrity sha1-X6GnYjhn/xr2yj3HKta4pCCL66c=

which-boxed-primitive@^1.1.0, which-boxed-primitive@^1.1.1:
  version "1.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which-boxed-primitive/-/which-boxed-primitive-1.1.1.tgz#d76ec27df7fa165f18d5808374a5fe23c29b176e"
  integrity sha1-127Cfff6Fl8Y1YCDdKX+I8KbF24=
  dependencies:
    is-bigint "^1.1.0"
    is-boolean-object "^1.2.1"
    is-number-object "^1.1.1"
    is-string "^1.1.1"
    is-symbol "^1.1.1"

which-builtin-type@^1.2.1:
  version "1.2.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which-builtin-type/-/which-builtin-type-1.2.1.tgz#89183da1b4907ab089a6b02029cc5d8d6574270e"
  integrity sha1-iRg9obSQerCJprAgKcxdjWV0Jw4=
  dependencies:
    call-bound "^1.0.2"
    function.prototype.name "^1.1.6"
    has-tostringtag "^1.0.2"
    is-async-function "^2.0.0"
    is-date-object "^1.1.0"
    is-finalizationregistry "^1.1.0"
    is-generator-function "^1.0.10"
    is-regex "^1.2.1"
    is-weakref "^1.0.2"
    isarray "^2.0.5"
    which-boxed-primitive "^1.1.0"
    which-collection "^1.0.2"
    which-typed-array "^1.1.16"

which-collection@^1.0.2:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which-collection/-/which-collection-1.0.2.tgz#627ef76243920a107e7ce8e96191debe4b16c2a0"
  integrity sha1-Yn73YkOSChB+fOjpYZHevksWwqA=
  dependencies:
    is-map "^2.0.3"
    is-set "^2.0.3"
    is-weakmap "^2.0.2"
    is-weakset "^2.0.3"

which-typed-array@^1.1.16, which-typed-array@^1.1.19, which-typed-array@^1.1.2:
  version "1.1.19"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which-typed-array/-/which-typed-array-1.1.19.tgz#df03842e870b6b88e117524a4b364b6fc689f956"
  integrity sha1-3wOELocLa4jhF1JKSzZLb8aJ+VY=
  dependencies:
    available-typed-arrays "^1.0.7"
    call-bind "^1.0.8"
    call-bound "^1.0.4"
    for-each "^0.3.5"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-tostringtag "^1.0.2"

which@^2.0.1:
  version "2.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which/-/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

which@^3.0.0:
  version "3.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/which/-/which-3.0.1.tgz#89f1cd0c23f629a8105ffe69b8172791c87b4be1"
  integrity sha1-ifHNDCP2KagQX/5puBcnkch7S+E=
  dependencies:
    isexe "^2.0.0"

why-is-node-running@^2.3.0:
  version "2.3.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/why-is-node-running/-/why-is-node-running-2.3.0.tgz#a3f69a97107f494b3cdc3bdddd883a7d65cebf04"
  integrity sha1-o/aalxB/SUs83Dvd3Yg6fWXOvwQ=
  dependencies:
    siginfo "^2.0.0"
    stackback "0.0.2"

widest-line@^3.1.0:
  version "3.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/widest-line/-/widest-line-3.1.0.tgz#8292333bbf66cb45ff0de1603b136b7ae1496eca"
  integrity sha1-gpIzO79my0X/DeFgOxNreuFJbso=
  dependencies:
    string-width "^4.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/word-wrap/-/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

"wrap-ansi-cjs@npm:wrap-ansi@^7.0.0":
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^6.2.0:
  version "6.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wrap-ansi/-/wrap-ansi-6.2.0.tgz#e9393ba07102e6c91a3b221478f0257cd2856e53"
  integrity sha1-6Tk7oHEC5skaOyIUePAlfNKFblM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^7.0.0:
  version "7.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wrap-ansi/-/wrap-ansi-7.0.0.tgz#67e145cff510a6a6984bdf1152911d69d2eb9e43"
  integrity sha1-Z+FFz/UQpqaYS98RUpEdadLrnkM=
  dependencies:
    ansi-styles "^4.0.0"
    string-width "^4.1.0"
    strip-ansi "^6.0.0"

wrap-ansi@^8.1.0:
  version "8.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wrap-ansi/-/wrap-ansi-8.1.0.tgz#56dc22368ee570face1b49819975d9b9a5ead214"
  integrity sha1-VtwiNo7lcPrOG0mBmXXZuaXq0hQ=
  dependencies:
    ansi-styles "^6.1.0"
    string-width "^5.0.1"
    strip-ansi "^7.0.1"

wrappy@1:
  version "1.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/wrappy/-/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^4.0.2:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/write-file-atomic/-/write-file-atomic-4.0.2.tgz#a9df01ae5b77858a027fd2e80768ee433555fcfd"
  integrity sha1-qd8Brlt3hYoCf9LoB2juQzVV/P0=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^3.0.7"

ws@^7, ws@^7.2.0, ws@^7.5.5:
  version "7.5.10"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ws/-/ws-7.5.10.tgz#58b5c20dc281633f6c19113f39b349bd8bd558d9"
  integrity sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=

ws@^8.2.3:
  version "8.18.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/ws/-/ws-8.18.2.tgz#42738b2be57ced85f46154320aabb51ab003705a"
  integrity sha1-QnOLK+V87YX0YVQyCqu1GrADcFo=

xml2js@0.6.2:
  version "0.6.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/xml2js/-/xml2js-0.6.2.tgz#dd0b630083aa09c161e25a4d0901e2b2a929b499"
  integrity sha1-3QtjAIOqCcFh4lpNCQHisqkptJk=
  dependencies:
    sax ">=0.6.0"
    xmlbuilder "~11.0.0"

xmlbuilder@~11.0.0:
  version "11.0.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/xmlbuilder/-/xmlbuilder-11.0.1.tgz#be9bae1c8a046e76b31127726347d0ad7002beb3"
  integrity sha1-vpuuHIoEbnazESdyY0fQrXACvrM=

xtend@~4.0.1:
  version "4.0.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/xtend/-/xtend-4.0.2.tgz#bb72779f5fa465186b1f438f674fa347fdb5db54"
  integrity sha1-u3J3n1+kZRhrH0OPZ0+jR/2121Q=

y18n@^5.0.5:
  version "5.0.8"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/y18n/-/y18n-5.0.8.tgz#7f4934d0f7ca8c56f95314939ddcd2dd91ce1d55"
  integrity sha1-f0k00PfKjFb5UxSTndzS3ZHOHVU=

yallist@^3.0.2:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yallist/-/yallist-3.1.1.tgz#dbb7daf9bfd8bac9ab45ebf602b8cbad0d5d08fd"
  integrity sha1-27fa+b/YusmrRev2ArjLrQ1dCP0=

yallist@^4.0.0:
  version "4.0.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yallist/-/yallist-4.0.0.tgz#9bb92790d9c0effec63be73519e11a35019a3a72"
  integrity sha1-m7knkNnA7/7GO+c1GeEaNQGaOnI=

yaml@^1.10.0:
  version "1.10.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yaml/-/yaml-1.10.2.tgz#2301c5ffbf12b467de8da2333a459e29e7920e4b"
  integrity sha1-IwHF/78StGfejaIzOkWeKeeSDks=

yaml@^2.3.4:
  version "2.8.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yaml/-/yaml-2.8.0.tgz#15f8c9866211bdc2d3781a0890e44d4fa1a5fff6"
  integrity sha1-FfjJhmIRvcLTeBoIkORNT6Gl//Y=

yargs-parser@^20.2.2:
  version "20.2.9"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yargs-parser/-/yargs-parser-20.2.9.tgz#2eb7dc3b0289718fc295f362753845c41a0c94ee"
  integrity sha1-LrfcOwKJcY/ClfNidThFxBoMlO4=

yargs-parser@^21.1.1:
  version "21.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yargs-parser/-/yargs-parser-21.1.1.tgz#9096bceebf990d21bb31fa9516e0ede294a77d35"
  integrity sha1-kJa87r+ZDSG7MfqVFuDt4pSnfTU=

yargs@^16.1.0:
  version "16.2.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yargs/-/yargs-16.2.0.tgz#1c82bf0f6b6a66eafce7ef30e376f49a12477f66"
  integrity sha1-HIK/D2tqZur85+8w43b0mhJHf2Y=
  dependencies:
    cliui "^7.0.2"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.0"
    y18n "^5.0.5"
    yargs-parser "^20.2.2"

yargs@^17.3.1, yargs@^17.5.1:
  version "17.7.2"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yargs/-/yargs-17.7.2.tgz#991df39aca675a192b816e1e0363f9d75d2aa269"
  integrity sha1-mR3zmspnWhkrgW4eA2P5110qomk=
  dependencies:
    cliui "^8.0.1"
    escalade "^3.1.1"
    get-caller-file "^2.0.5"
    require-directory "^2.1.1"
    string-width "^4.2.3"
    y18n "^5.0.5"
    yargs-parser "^21.1.1"

yn@3.1.1:
  version "3.1.1"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yn/-/yn-3.1.1.tgz#1e87401a09d767c1d5eab26a6e4c185182d2eb50"
  integrity sha1-HodAGgnXZ8HV6rJqbkwYUYLS61A=

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yocto-queue/-/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=

yoga-layout-prebuilt@^1.9.6:
  version "1.10.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yoga-layout-prebuilt/-/yoga-layout-prebuilt-1.10.0.tgz#2936fbaf4b3628ee0b3e3b1df44936d6c146faa6"
  integrity sha1-KTb7r0s2KO4LPjsd9Ek21sFG+qY=
  dependencies:
    "@types/yoga-layout" "1.9.2"

yup@1.4.0:
  version "1.4.0"
  resolved "https://pkgs.dev.azure.com/DevOpsAD/OneMenu/_packaging/onehub-universal-lib/npm/registry/yup/-/yup-1.4.0.tgz#898dcd660f9fb97c41f181839d3d65c3ee15a43e"
  integrity sha1-iY3NZg+fuXxB8YGDnT1lw+4VpD4=
  dependencies:
    property-expr "^2.0.5"
    tiny-case "^1.0.3"
    toposort "^2.0.2"
    type-fest "^2.19.0"
